import React, { useEffect } from 'react'
import Head from 'next/head'
import Image from 'next/image'
import styles from './login.module.scss'
import aldarLogo from '../../public/svg/aldarLogo.svg'
import Button from '@/src/component/shared/button'
import { StorageKeys } from '@/src/constant/enum'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import { persister } from '@/src/redux/store'
import { removeLocalStorageItem } from '@/src/utils/storageUtils'

const Login = () => {
  const { authUrlApi } = useAuthorization()
  const { setRecentProject } = useProjectSummary()
  const { clearCurrentPeriodDataApi } = useMasterPeriod()

  const handleLogin = async () => {
    removeLocalStorageItem(StorageKeys.IS_LOGIN)
    setRecentProject({ projectName: '' })
    const redirectUrl = `${window.location.origin}/redirect`
    const res: Record<string, any> = await authUrlApi(redirectUrl)
    if (!res.payload.success) return
    window.location.href = res.payload.data
    // router.replace(res.payload.data);`
  }

  useEffect(() => {
    clearCurrentPeriodDataApi()
    sessionStorage.removeItem('persist:root')
    persister.purge()
  }, [])

  return (
    <div className={styles.container}>
      <Head>
        <title>Login</title>
        <meta name="description" content="Login" />
      </Head>
      <div className={`${styles.card} ${styles.overlay}`}>
        <div className={styles.contain}>
          <div className={styles.fields}>
            <Image src={aldarLogo} height={100} width={100} alt="aldar" />
          </div>
          <h1>Welcome to PULSE</h1>
          <Button color="secondary" onClick={() => handleLogin()} className={styles.button}>
            Login
          </Button>

          <div className={styles.requestAccessLink}>
            <a
              href="https://sd.aldar.com/app/itdesk/ui/requests/add?reqTemplate=149471000074670023"
              target="_blank"
              rel="noopener noreferrer"
              // className={`${styles.requestAccessLink} ${styles.portalLink}`}
              aria-label="Visit ALDAR IT Service Desk Portal"
            >
              Request Access ?
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
