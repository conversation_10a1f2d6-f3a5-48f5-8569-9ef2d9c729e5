@import '/styles/color.scss';

.container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: url('../../public/svg/Login Bkcgd.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center bottom;
  position: relative;
  backdrop-filter: blur(2px);

  .accessDeiedMsg {
    color: $ERROR;
    font-weight: 600;
    text-align: center;
  }

  .startAdornment {
    width: 20px;
    height: 20px;
    fill: $DARK;
    margin-right: 0px;
  }

  .aldarLogo {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    margin-top: 14px;
    margin-right: 14px;
  }

  .card {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 360px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    border: 2px solid #fff;
    box-shadow: 0px 4px 200px 0px rgba(0, 0, 0, 0.1490196078);
    padding: 54px 40px;
    max-width: 352px;
    width: 100%;
    margin: 0;
    position: absolute;
    top: 50%;
    right: 6%;
    transform: translateY(-50%);
    box-shadow: 0px 8px 22px 4px rgba(0, 0, 0, 0.1);

    .contain,
    .fields,
    h1,
    .button {
      position: relative;
      z-index: 1;
    }

    h1 {
      text-align: center;
      font-family: Poppins;
      color: $BLACK;
    }

    .button {
      margin: auto;
      padding: 8px 24px;
      background-color: #0070c0;
      color: white;
      border: none;
      border-radius: 08px;
      min-width: 118px;
      display: block;
      font-family: 'Poppins';
      font-size: 18px;
    }
  }

  .header {
    font-family: Poppins;
    letter-spacing: -0.02em;
    display: flex;
    justify-content: center;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    text-align: left;
    color: $BLACK;
  }

  .fields {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .textField {
      width: 400px !important;
    }

    .passwords {
      display: flex;
      flex-direction: column;
      gap: 7px;

      .forgotPassword {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
      }
    }
  }
}

.requestAccessLink {
  text-align: center;
  position: relative;
  bottom: -100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    right: 20px;
    left: 20px;
    max-width: calc(100% - 40px);
    padding: 40px 30px;
  }

  .popupContainer {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .popupContent {
    padding: 24px 20px 20px;
  }

  .popupActions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .card {
    height: auto;
    min-height: 320px;
    padding: 30px 20px;
  }

  .popupTitle {
    font-size: 20px;
  }

  .popupMessage {
    font-size: 14px;
  }

  .contactAdminBtn,
  .closePopupBtn {
    padding: 10px 20px;
    font-size: 13px;
  }
}