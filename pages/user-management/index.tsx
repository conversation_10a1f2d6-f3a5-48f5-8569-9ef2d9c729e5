import React, { useEffect, useMemo, useState } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import styles from './userManagement.module.scss'
import { BaseURL } from '@/src/api'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import RoleTable from '@/src/component/roleTable'
import Button from '@/src/component/shared/button'
import Loader from '@/src/component/shared/loader'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { Routes, userType, whiteListedUserList } from '@/src/constant/enum'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import { IUser } from '@/src/redux/createUser/interface'
import useUser from '@/src/redux/createUser/useCreateUser'
import { getLastSelectedTab, setLastSelectedTab } from '@/src/redux/tabStore'
import {
  convertAndSortData,
  convertMultiSelectOption,
  getUniqueValuesFromArray,
  sortArrayByKeyWithTypeConversion,
} from '@/src/utils/arrayUtils'

const Tab: React.FC<any> = ({ name, onSelect, isActive, children }) => {
  const handleClick = () => {
    onSelect(name)
  }

  return (
    <div className={`${styles.tab} ${isActive ? styles.selectedTab : ''}`} onClick={handleClick}>
      <TypographyField variant={isActive ? 'h2' : 'h7'} text={children} />
    </div>
  )
}

const UserManagement = () => {
  const router = useRouter()
  const { addUserApi, users, getUserApi, deleteUserApi } = useUser()
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any>([])
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [selectedTab, setSelectedTab] = useState<string>(getLastSelectedTab())
  const { currentUser } = useAuthorization()

  const redirectToProject = async () => {
    if (whiteListedUserList.includes(currentUser?.email)) {
      return
    } else if (currentUser?.user_type?.includes(userType.USER)) {
      router.push(`${Routes.PROJECTS}`)
    }
  }
  useEffect(() => {
    redirectToProject()
  }, [currentUser])

  const handleData = () => {
    const mapData: any = users.map((item: IUser) => {
      return {
        id: item?.id,
        isSelect: false,
        name: item?.name?.trim(),
        email: item?.email,
        user_type: item?.user_type === 'super_admin' ? 'admin' : item?.user_type || '',
        role: item?.role,
        organization: item?.organization,
      }
    })

    setData(sortArrayByKeyWithTypeConversion(mapData, 'name'))
    setLoading(false)
  }

  useEffect(() => {
    setLoading(true)
    handleData()
  }, [users])

  useEffect(() => {
    getUserApi()
  }, [])

  const handleDelete = async (id: any) => {
    const res: Record<string, any> = await deleteUserApi(id)
    if (!res.payload.success) return
    getUserApi()
    setDeleteModel(null)
  }

  const handleTabSelect = (tabName: string) => {
    setSelectedTab(tabName)
    setLastSelectedTab(tabName)
  }

  const selectedData = data?.filter((item: any) => {
    return item.isSelect === true
  })

  const findUniqueElements = (arr: string[]) => {
    return [...getUniqueValuesFromArray(arr)]
  }

  const optionOfUserType = useMemo(
    () =>
      convertAndSortData(
        convertMultiSelectOption(findUniqueElements(data.map((item: any) => item.user_type))).filter(
          (item) => item.id !== null && item.name !== null,
        ),
      ),
    [data],
  )

  const optionOfRole = useMemo(
    () =>
      convertAndSortData(
        convertMultiSelectOption(findUniqueElements(data.map((item: any) => item.role))).filter(
          (item) => item.id !== null && item.name !== null,
        ),
      ),
    [data],
  )

  const optionOfOrganization = useMemo(
    () =>
      convertAndSortData(
        convertMultiSelectOption(findUniqueElements(data.map((item: any) => item.organization))).filter(
          (item) => item.id !== null && item.name !== null,
        ),
      ),
    [data],
  )
  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'actionCol',
      header: '',
      cell: ({ row }) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon
              className={styles.button}
              onClick={() => router.push({ pathname: `${Routes.USER_MANAGEMENT_USER}`, query: { id: row.id } })}
            />
            <DeleteIcon className={styles.button} onClick={() => setDeleteModel(Number(row.id))} />
          </div>
        )
      },
      size: 70,
      align: 'left',
    },

    {
      accessorKey: 'name',
      header: 'Name',
      // flex: 1,
      size: 300,
    },
    {
      accessorKey: 'email',
      header: 'Email',
      // flex: 1,
      size: 320,
    },
    {
      accessorKey: 'user_type',
      header: 'User Type',
      // flex: 1,
      size: 200,

      filterType: 'list',
      listOption: optionOfUserType,
    },
    {
      accessorKey: 'role',
      header: 'Role Template',
      // flex: 1,
      size: 200,

      filterType: 'list',
      listOption: optionOfRole,
    },
    {
      accessorKey: 'organization',
      header: 'Organization',
      // flex: 1,
      size: 200,

      filterType: 'list',
      listOption: optionOfOrganization,
    },
  ]

  const handleDownload = async () => {
    try {
      const response = await fetch(`${BaseURL}/user/excel`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // or the appropriate content type for your Excel file
        },
      })

      if (!response.ok) {
        throw new Error('Failed to download file')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = url
      a.download = 'user_record.xlsx' // Specify the name of the file
      document.body.appendChild(a)
      a.click()
      a.remove()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading the file:', error)
    }
  }

  return (
    <div className={styles.container}>
      <Head>
        <title>User Management</title>
        <meta name="description" content="User Management" />
      </Head>
      <div className={styles.header}>
        <TypographyField variant="h5" text={'User Management'} />
        <div className={styles.actionButtons}>
          <Button onClick={() => router.push(`${Routes.USER_MANAGEMENT_USER}`)}>Add User</Button>
          <Button onClick={() => router.push(`${Routes.USER_MANAGEMENT_ROLE}`)}>Add Role Template</Button>
          <Button onClick={() => handleDownload()}>Export Record</Button>
        </div>
      </div>
      <div className={styles.contain}>
        <div className={styles.tabContainer}>
          <div className={styles.tabContain}>
            {['User', 'Role'].map((tabName) => (
              <Tab key={tabName} name={tabName} onSelect={handleTabSelect} isActive={selectedTab === tabName}>
                {tabName}
              </Tab>
            ))}
          </div>
        </div>
        <div className={styles.tableContainer}>
          {selectedTab.toLowerCase() === 'user' && (
            <>{loading ? <Loader /> : <TanStackTable rows={data || []} columns={columns} />}</>
          )}
          {selectedTab.toLowerCase() === 'role' && <RoleTable />}
        </div>
      </div>
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDelete(deleteModel as number)}
      />
    </div>
  )
}

export default UserManagement
