import React, { useEffect, useMemo, useState } from 'react'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './CreateUser.module.scss'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import TextInput<PERSON>ield from '@/src/component/shared/textInputField'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/src/component/shared/typography'
import <PERSON><PERSON><PERSON> from '@/src/component/svgImages/leftArrow'
import { Routes, userType } from '@/src/constant/enum'
import useUser from '@/src/redux/createUser/useCreateUser'
import useRole from '@/src/redux/role/useRole'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'

export interface IAddUserValue {
  name: string
  email: string
  role: string
  user_type?: string
  // role_template: string;
  organization: string
}

const AddUserDrawer: React.FC = () => {
  const [user, setUser] = useState<any>()
  const { roles, getRole<PERSON>pi } = useRole()
  const { addUserApi, users, getUser<PERSON><PERSON>, updateUser<PERSON>pi } = useUser()
  const router = useRouter()

  useEffect(() => {
    const data: any = users.find((item: any) => {
      return item?.id == router.query.id
    })
    setUser(data)
  }, [router.query.id])

  useEffect(() => {
    getRoleApi()
  }, [])

  const initialValues = useMemo(
    () => ({
      name: user?.name || '',
      email: user?.email || '',
      role: user?.role || '',
      user_type: user?.user_type === 'super_admin' ? 'admin' : user?.user_type || '',
      organization: user?.organization || '',
    }),
    [user],
  )

  const handleSubmit = async (values: any) => {
    const payload = { ...values, user_type: values?.user_type === 'admin' ? 'super_admin' : values?.user_type }
    if (user?.id) {
      const res: any = await updateUserApi({ id: user?.id, data: payload })
      if (res.payload.success === true) {
        // Optionally handle success
        formik.resetForm()
        router.push(`${Routes.USER_MANAGEMENT}`)
        getUserApi()
      }
    } else {
      const res: any = await addUserApi(payload)
      if (res.payload.success === true) {
        formik.resetForm()
        router.push(`${Routes.USER_MANAGEMENT}`)
        getUserApi()
      }
    }
  }

  const formik = useFormik<IAddUserValue>({
    initialValues: initialValues,
    enableReinitialize: true,
    onSubmit: (values) => {
      handleSubmit(values)
    },
  })

  const roleOptions = useMemo(() => {
    return populateDropdownOptions(roles, 'role_name')
  }, [roles])

  return (
    <div className={styles.container}>
      <div className={styles.headerTitle}>
        <TypographyField variant="h5" text={'User Management'} />
      </div>{' '}
      <div className={styles.header} onClick={() => router.push(`${Routes.USER_MANAGEMENT}`)}>
        <LeftArrow className={styles.leftArrow} />
        <TypographyField
          variant="subheadingSemiBold"
          text={`${user?.id ? 'Edit User' : 'Create User'}`}
          className={styles.detailsText}
        />
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr 1fr',
              gap: '24px',
            }}
          >
            <TextInputField
              className={styles.textField}
              name="name"
              labelText={'Name'}
              placeholder="Type Something..."
              variant={'outlined'}
              value={formik.values.name}
              onChange={formik.handleChange}
            />
            <TextInputField
              className={styles.textField}
              name="email"
              labelText={'Email'}
              placeholder="Type Something..."
              variant={'outlined'}
              value={formik.values.email}
              onChange={formik.handleChange}
            />
            <ComboBox
              options={roleOptions}
              labelText={'Role Template'}
              placeholder="Type Something..."
              value={
                formik.values?.role
                  ? {
                      label: formik.values?.role,
                      value: formik.values?.role,
                    }
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  role: val?.value || '',
                })
              }
            />
            <ComboBox
              options={[
                { label: userType.USER, value: userType.USER },
                { label: userType.SUPER_USER, value: userType.SUPER_USER },
                { label: 'admin', value: 'admin' },
              ]}
              labelText={'User Type'}
              placeholder="Type Something..."
              value={
                formik.values?.user_type
                  ? {
                      label: formik.values?.user_type,
                      value: formik.values?.user_type,
                    }
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  user_type: val?.value || '',
                })
              }
            />
            {/* <ComboBox
              options={roleTemplateOptions}
              labelText={"Role Template"}
              placeholder="Type Something..."
              value={
                formik.values?.role_template
                  ? {
                      label: formik.values?.role_template,
                      value: formik.values?.role_template,
                    }
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  role_template: val?.value || "",
                })
              }
            /> */}
            <TextInputField
              className={styles.textField}
              name="organization"
              labelText={'Organization'}
              placeholder="Type Something..."
              variant={'outlined'}
              value={formik.values.organization}
              onChange={formik.handleChange}
            />
          </div>
          <Button type="submit" className={styles.addUserButton}>
            + {`${router.query.id ? 'Update' : 'Create'}`} User
          </Button>
        </form>
      </div>
    </div>
  )
}

export default AddUserDrawer
