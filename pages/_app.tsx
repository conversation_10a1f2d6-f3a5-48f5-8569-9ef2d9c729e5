import { ThemeProvider } from '@mui/material'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { AppProps } from 'next/app'
import dynamic from 'next/dynamic'
import '../styles/globals.css'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import ToastManager from '@/src/component/toast/ToastManager'
import store, { persister } from '@/src/redux/store'
import { theme } from '@/styles/theme'

const queryClient = new QueryClient()

export default function App({ Component, pageProps }: AppProps) {
  const Layout = dynamic(() => import('../src/layout'), { ssr: false })

  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryDevtools initialIsOpen={false} />
      <Provider store={store}>
        <PersistGate loading={null} persistor={persister}>
          <ThemeProvider theme={theme}>
            <Layout>
              <Component {...pageProps} />
              <ToastManager />
            </Layout>
          </ThemeProvider>
        </PersistGate>
      </Provider>
    </QueryClientProvider>
  )
}
