import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { useQuery } from '@tanstack/react-query'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import styles from './UpdateProgress.module.scss'
import ConfirmSaveModal from '@/src/component/confirmSaveModel'
import PulseActionButtons from '@/src/component/shared/button/PulseActionButtons'
import Loader from '@/src/component/shared/loader'
import PulseModel from '@/src/component/shared/pulseModel'
import TypographyField from '@/src/component/shared/typography'
import DiscardModel from '@/src/component/summary/summaryTab/discardModel'
import LeftArrow from '@/src/component/svgImages/leftArrow'
import { updateProgressFormField } from '@/src/component/updateProgress/constant'
import Header from '@/src/component/updateProgress/header'
import { buildStatusData, findProjectManagementData, hasFieldChanged } from '@/src/component/updateProgress/helper'
import ProgressForm from '@/src/component/updateProgress/progressForm'
import ConfirmationModel from '@/src/component/updateProgress/progressForm/confirmationModel'
import ValidationModel from '@/src/component/updateProgress/progressForm/validationModel'
import { handleProjectManagement, handleStatus } from '@/src/component/updateProgress/utils'
import { Routes } from '@/src/constant/enum'
import { wait } from '@/src/helpers/helpers'
import { PROJECT_QUERY_KEY, useUpdateProject } from '@/src/hooks/useProjects'
import useAreaOfConcern from '@/src/redux/areaOfConcern/useAreaOfConcern'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useDataEntryScreen from '@/src/redux/dataEntryScreen/useDataEntryScreen'
import useDevelopers from '@/src/redux/developers/useDeveloper'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useKeyHighlights from '@/src/redux/keyHighlight/useKeyHighlight'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IProjectManagement } from '@/src/redux/projectManagement/interface'
import useProjectManagement from '@/src/redux/projectManagement/useProjectManagement'
import { ILookupProjectToPhase, IStatus } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import { getMasterOneProject } from '@/src/services/projects'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'
import { confirmationStatus, validateStatus } from '@/src/utils/progressForm/statusValidation'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'
import { errorToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const updateProgress = () => {
  const router = useRouter()
  //state
  const [navigate, setNavigate] = useState(false)
  const [edit, setEdit] = useState<boolean>(false)
  const [loader, setLoader] = useState<boolean>(true)
  const [confirmSaveModel, setConfirmSaveModel] = useState(false)
  const [isDiscard, setIsDiscard] = useState(false)
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState<string[]>([])
  // Note: this state is for open close confirmation model
  const [isConfirmationModel, setIsConfirmationModel] = useState(false)
  // Note: this state is for set validation message in confirmation model
  const [confirmationMessage, setConfirmationMessage] = useState<string>('')
  // Note: this state is for whether the user has confirmed the action or no from model
  const [isConfirmFromUser, setIsConfirmFromUser] = useState(false)
  //custom-hook
  const { status, statuses, getStatusApi, getOneStatusApi, updateStatusApi, addStatusApi, setStatusByIdApi } =
    useStatus()
  const { getKeyAchievementsApi } = useKeyAchievement()
  const { highlights, isUpdateHighlight, setIsUpdateHighlight } = useKeyHighlights()
  const { projectManagements, /* getProjectManagementsApi, */ updateProjectManagementsApi, addProjectManagementsApi } =
    useProjectManagement()
  const { currentUser } = useAuthorization()
  const { getAreaOfConcernsApi } = useAreaOfConcern()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { getMasterDevelopersApi } = useDevelopers()
  const { setSelectedTabApi } = useDataEntryScreen()

  const foundStausById =
    status && status.id == router.query?.slug ? status : statuses?.find((item) => item.id == router.query?.slug)

  const getProjectPayload = {
    projectName: encodeURIComponent(foundStausById?.project_name as string),
    period: currentPeriod,
  }

  const { data: currentProject } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: !!status?.project_name, // only call API when project_name is available
  })

  const { mutate: updateMasterProjectApi } = useUpdateProject()

  const handleData = async () => {
    setLoader(true)
    try {
      // Define all API calls in an array for better maintainability
      const apiCalls = [
        getKeyAchievementsApi({
          period: currentPeriod,
          project_name: (currentProject?.project_name || status?.project_name) as string,
        }),
        getAreaOfConcernsApi({
          period: currentPeriod,
          project_name: (currentProject?.project_name || status?.project_name) as string,
        }),
        getStatusApi({
          period: currentPeriod,
          project_name: (currentProject?.project_name || status?.project_name) as string,
        }),
        // getMasterConsultantsApi(),
        // getMasterPmcConsultantApi(),
        // getMasterContractorsApi(),
        // getMasterDevelopersApi(),
        // getMasterProjectSubStageApi(),
        // getMasterProjectStageApi(),
        // getMasterDesignManagersApi(),
        // getSvpsApi(),
      ]

      // Await all API calls
      await Promise.all(apiCalls)
      // setStatusByIdApi(router.query.slug as string)

      const res: Record<string, any> = await getOneStatusApi({
        period: currentPeriod,
        project_name: (currentProject?.project_name || status?.project_name) as string,
        id: router.query.slug as string,
      })
      // const allStatus: any = await getStatusApi({
      //   project_name: (currentProject?.project_name || status?.project_name) as string,
      //   period: currentPeriod,
      // })

      if (!res?.payload.success) return

      // const response: Record<string, any> = await getProjectManagementsApi({
      //   period: currentPeriod,
      //   project_name: res.payload.data?.project_name,
      // })

      // if (!response?.payload?.success) return
    } catch (error) {
      console.error('Error while fetching data:', error)
    } finally {
      setLoader(false)
    }
  }

  useEffect(() => {
    if (currentPeriod) {
      handleData()
    }
  }, [currentPeriod, router.query.slug])

  useEffect(() => {
    if (loader) {
      getUpdatedInitialValues()
    }
  }, [loader])

  //TODO: merge project management data into project status
  // const projectManagement = useMemo(
  //   () => projectManagements.filter((item: IProjectManagement) => item.project_name === status?.project_name),
  //   [projectManagements, status],
  // )

  const getUpdatedInitialValues = useCallback(() => {
    //TODO: merge project management data into project status
    // Use destructuring to extract relevant properties to avoid repeated reference to `status`
    // const { project_name, LookupProjectToPhase } = status || {}
    // const project_stage_status = status?.MasterProjectStageStatus
    // const project_sub_stage = status?.MasterProjectSubStage

    // const projectManagementData: IProjectManagement = findProjectManagementData(
    //   projectManagement,
    //   LookupProjectToPhase,
    //   project_stage_status,
    //   project_sub_stage,
    // )

    // const isThatProject =
    //   project_name === projectManagementData?.project_name &&
    //   project_stage_status?.id === projectManagementData?.master_project_stage_status_id &&
    //   !!projectManagementData?.LookupProjectToPhase?.some((itemPhase: ILookupProjectToPhase) =>
    //     LookupProjectToPhase?.some((lookupPhase: ILookupProjectToPhase) => lookupPhase.id === itemPhase.id),
    //   )

    // const projectManagementValue = buildProjectManagementValue(projectManagementData, isThatProject)
    const statusData = buildStatusData(status, statuses)
    // return { ...statusData, ...projectManagementValue }
    return { ...statusData }
  }, [status, statuses])

  const handleFormSubmission = async (values: any) => {
    try {
      setLoader(true)
      // Process Project Management values
      await handleProjectManagementValues(values)
      // Handle Status updates
      await handleStatusUpdates(values)
      afterUpdateNavigateToScreen()
      updateFormikValues()

      // Reset confirmation state
      setIsConfirmationModel(false)
      setIsConfirmFromUser(false)
      setConfirmationMessage('')
      setIsUpdateHighlight(false)
      setEdit(!edit)
      // Reset the form to clear the dirty state
      formik.resetForm({ values: formik.values })
    } catch (error) {
      console.error('Error submitting form:', error)
      errorToast('Failed to update progress')
    } finally {
      // setLoader(false)
    }
  }

  const submitForm = async (values: any) => {
    const allowedStatuses = statuses.filter((item) =>
      getStageStatusByPermission(currentUser.role).includes(item?.MasterProjectStageStatus?.project_stage_status),
    )
    const sortedStatuses = sortArrayByKeyWithTypeConversion(allowedStatuses, 'project_status_sorting_order', true)

    const validationMessages = await validateStatus(values, status as IStatus, sortedStatuses)
    if (validationMessages.length > 0) {
      setValidationMessage(validationMessages)
      setIsValidationModel(true)
      return
    }

    const confirmationMessage = await confirmationStatus(values, status as IStatus, sortedStatuses)
    if (confirmationMessage && !isConfirmFromUser) {
      setConfirmationMessage(confirmationMessage)
      setIsConfirmationModel(true)
      return
    }
    // If validated and confirmed, proceed with submission
    await handleFormSubmission(values)
  }

  const handleConfirm = async () => {
    const initialValues: any = getUpdatedInitialValues()
    // send only updated fields in api
    const updatedFields = Object.keys(formik.values).reduce((acc: any, key: any) => {
      if (formik.values[key] !== initialValues[key]) {
        acc[key] = formik.values[key]
      }
      return acc
    }, {})
    setIsConfirmFromUser(true)
    await handleFormSubmission(updatedFields)
  }

  const handleClose = () => {
    setIsConfirmFromUser(false)
    const initialValues: any = getUpdatedInitialValues()
    const constructionFieldsToCheck = ['supervision_consultant', 'PMC_Consultant', 'contractor']
    const designFieldsToCheck = ['consultant']
    const fieldToCheck = formik.values?.stage_status === 'Design' ? designFieldsToCheck : constructionFieldsToCheck
    // send only updated fields and also field is not exist in constructionFieldsToCheck array
    const updatedFields = Object.keys(formik.values).reduce((acc: any, key: any) => {
      if (formik.values[key] !== initialValues[key] && !fieldToCheck.includes(key)) {
        acc[key] = formik.values[key]
      }
      return acc
    }, {})

    if (Object.keys(updatedFields).length === 0) {
      const revertedFields = fieldToCheck.reduce((acc: any, key: string) => {
        acc[key] = initialValues[key] // set value from initialValues
        return acc
      }, {})

      // Update only those fields in Formik
      formik.setValues({ ...formik.values, ...revertedFields })
      setIsConfirmationModel(false)
      setIsConfirmFromUser(false)
      setConfirmationMessage('')
      return
    }
    handleFormSubmission(updatedFields)
  }

  const handleProjectManagementValues = async (values: any) => {
    await handleProjectManagement(
      values,
      projectManagements,
      status,
      currentPeriod,
      router,
      addProjectManagementsApi,
      updateProjectManagementsApi,
    )
  }

  const handleStatusUpdates = async (values: any) => {
    await handleStatus(
      values,
      status,
      highlights,
      currentPeriod,
      router,
      addStatusApi,
      updateStatusApi,
      formik,
      statuses,
      getStatusApi,
      updateMasterProjectApi,
      currentProject,
      isUpdateHighlight,
      setIsUpdateHighlight,
    )
  }

  const afterUpdateNavigateToScreen = () => {
    if (navigate) {
      // Handle saving progress and navigating away from the update progress screen
      // When the user attempts to navigate away from the update progress screen,
      // check if there are any unsaved changes that need to be saved.
      // If so, save the current values and update the database before allowing navigation.
      // This is necessary to ensure that any changes made by the user are not lost
      // when they navigate away from the screen. The following logic ensures
      // that the data is saved properly before the user leaves the screen.
      if (router?.query?.search) {
        router.push(
          `${Routes.SUMMARY}/${encodeURIComponent(status?.project_name?.toString() || '')}?search=${router?.query?.search}`,
        )
        setSelectedTabApi('Progress')
      } else {
        router.push(`${Routes.SUMMARY}/${encodeURIComponent(status?.project_name?.toString() || '')}`)
        setSelectedTabApi('Progress')
      }
      setNavigate(false)
    }
  }

  // USE_FOR: After saving the records, update the fields with the latest data.
  const updateFormikValues = async () => {
    // const allStatus: any = await getStatusApi({
    //   project_name: (currentProject?.project_name || status?.project_name) as string,
    //   period: currentPeriod,
    // })

    // const resOfOneStatus = allStatus?.payload?.data?.find(
    //   (item: IStatus) => item.id == Number(router.query.slug),
    // ) as IStatus | null

    const resOfOneStatus: any = await getOneStatusApi({
      period: currentPeriod,
      id: router.query.slug as string,
      project_name: (currentProject?.project_name || status?.project_name) as string,
    })

    const resStatus: any = await getStatusApi({ period: currentPeriod, project_name: currentProject?.project_name })

    // const resStatusById: any = resStatus.payload?.data?.find((item: IStatus) => item.id == router.query?.slug)
    // console.log('resStatusById: ', resStatusById)

    //TODO: merge project management data into project status
    /*   
    const resOfOneProjectManagement: any = await getProjectManagementsApi({ period: currentPeriod })
    const pmValue = resOfOneProjectManagement.payload.data?.filter((item: any) => {
      return item.project_name === status?.project_name
    })

     const projectManagementData: any = findProjectManagementData(
      pmValue,
      status?.LookupProjectToPhase,
      status?.MasterProjectStageStatus,
      status?.MasterProjectSubStage,
    ) 

    //we are not cheking sub stage because design management only have sub stage and fore design management project management section is hide
    const isThatProject =
      status?.project_name === projectManagementData?.project_name &&
      status?.MasterProjectStageStatus?.id === projectManagementData?.master_project_stage_status_id &&
      !!status?.LookupProjectToPhase.find(
        (item) =>
          !!projectManagementData?.LookupProjectToPhase?.some(
            (lookupPhase: ILookupProjectToPhase) => lookupPhase.id === item.id,
          ),
      )  
    
    const projectManagementValue = buildProjectManagementValue(projectManagementData, isThatProject) 
    */
    const statusData = buildStatusData(resOfOneStatus?.payload?.data, resStatus.payload.data)

    //TODO: merge project management data into project status
    // const data = { ...statusData, ...projectManagementValue }

    const data = { ...statusData }
    formik.setValues(data)
    formik.resetForm({ values: data })
    setLoader(false)
    setEdit(false)
  }

  const convertToNumberField: any = {
    pte: 'pte',
    master_consultant_id: 'master_consultant_id',
  }

  const formik = useFormik({
    initialValues: getUpdatedInitialValues(),
    enableReinitialize: true,
    onSubmit: (values: any) => {
      const initialValues: any = getUpdatedInitialValues()
      const updatedFields = Object.keys(values).reduce((acc: any, key: any) => {
        if (values[key] !== initialValues[key]) {
          acc[key] = convertToNumberField[key] && values[key] !== '' ? Number(values[key]) : values[key]
        }
        return acc
      }, {})

      // Call onSubmit with the updated fields only
      submitForm(updatedFields)
    },
  })

  const handleNavigateWithoutDiscard = () => {
    if (router?.query?.search) {
      setSelectedTabApi('Progress')
      router.push(
        `${Routes.SUMMARY}/${encodeURIComponent(status?.project_name?.toString() || '')}?search=${router?.query?.search}`,
      )
    } else {
      setSelectedTabApi('Progress')
      router.push(`${Routes.SUMMARY}/${encodeURIComponent(status?.project_name?.toString() || '')}`)
    }
    setConfirmSaveModel(false)
  }

  const handleNavigateWithSave = () => {
    setNavigate(true)
    formik.handleSubmit()
    setConfirmSaveModel(false)
  }

  // USE_FOR: When we return to this screen, check if the data has changed and ask whether to save it.
  const hasFormChanged = () => {
    return updateProgressFormField.some((field) => hasFieldChanged(formik.initialValues, formik.values, field))
  }

  const handleNavigate = () => {
    // USE_FOR: When we return to this screen, check if the data has changed and ask whether to save it.
    if (hasFormChanged()) {
      setConfirmSaveModel(true)
    } else {
      handleNavigateWithoutDiscard()
    }
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  return (
    <div className={styles.container}>
      {loader ? (
        <Loader />
      ) : (
        <>
          <Header status={status as IStatus} />
          <form onSubmit={formik.handleSubmit}>
            <div className={styles.editStatusHeader}>
              <div className={styles.header} onClick={() => handleNavigate()}>
                <LeftArrow className={styles.leftArrow} />
                <TypographyField variant="subheadingSemiBold" text="Update Progress" className={styles.detailsText} />
              </div>
              <div className={styles.buttons}>
                <div className={`${styles.actionHeader} `}>
                  <PulseActionButtons
                    edit={edit}
                    onEdit={() => {
                      !isEditForUser
                        ? toast(`The current reporting period is locked`, {
                            icon: <WarningAmberOutlined />,
                          })
                        : setEdit(!edit)
                    }}
                    onDiscard={() => setIsDiscard(true)}
                    onSave={() => formik.handleSubmit()}
                    editDisable={formik.dirty || isUpdateHighlight}
                    discardDisable={!formik.dirty && !isUpdateHighlight}
                    saveDisable={(!formik.dirty && !isUpdateHighlight) || !isEditForUser}
                  />
                </div>
              </div>
            </div>
            <ProgressForm
              formik={formik}
              edit={edit}
              projectName={status?.project_name || ''}
              isEditForUser={isEditForUser}
            />
          </form>
          <PulseModel
            style={{ width: '350px' }}
            open={confirmSaveModel}
            closable={false}
            onClose={handleNavigateWithoutDiscard}
            content={<ConfirmSaveModal onClose={handleNavigateWithoutDiscard} handleConfirm={handleNavigateWithSave} />}
          />
          <PulseModel
            style={{
              position: 'absolute' as 'absolute',
              maxWidth: '402px',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 'fitContent',
              bgcolor: 'background.paper',
              borderRadius: '12px',
              boxShadow: 24,
              pt: '20px',
              px: '20px',
              pb: '20px',
            }}
            open={isDiscard}
            closable={false}
            onClose={() => setIsDiscard(false)}
            content={
              <DiscardModel
                onClose={() => setIsDiscard(false)}
                onDiscard={() => {
                  setEdit(false)
                  formik.resetForm()
                }}
              />
            }
          />
          <PulseModel
            closable={false}
            style={{ width: 'fitContent' }}
            open={isValidationModel}
            onClose={() => setIsValidationModel(false)}
            content={<ValidationModel messages={validationMessage} onClose={() => setIsValidationModel(false)} />}
          />
          <PulseModel
            closable={false}
            style={{ width: 'fitContent' }}
            open={isConfirmationModel}
            onClose={() => setIsConfirmationModel(false)}
            content={
              <ConfirmationModel message={confirmationMessage} handleConfirm={handleConfirm} onClose={handleClose} />
            }
          />
        </>
      )}
    </div>
  )
}

export default updateProgress
