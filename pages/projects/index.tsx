import React, { useState } from 'react'
import Head from 'next/head'
import { Toaster } from 'sonner'
import styles from './Projects.module.scss'
import ProjectHeader from '@/src/component/projects/header'
import ProjectTable from '@/src/component/projects/projectTable'

const Projects = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true)

  return (
    <>
      <Head>
        <title>Projects</title>
        <meta name="description" content="Projects" />
      </Head>
      <ProjectHeader isLoading={isLoading} />
      {/* <Toaster position="bottom-right" /> */}
      <div className={styles.projectContent}>
        <ProjectTable isLoading={isLoading} setIsLoading={setIsLoading} />
      </div>
    </>
  )
}

export default Projects
