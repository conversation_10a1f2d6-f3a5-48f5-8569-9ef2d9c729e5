@import '/styles/color.scss';

.categoryBox {
  flex: 1;
  border: 1px solid #ccc;
  padding: 20px;
  border-radius: 5px;
  background-color: #f9f9f9;
  width: 300px;
  text-align: center;

  .dropZone {
    margin: 10px 0;
    padding: 20px;
    border: 2px dashed #ccc;
    text-align: center;
    background-color: #fafafa;
    border-radius: 5px;
    position: relative;

    .customFileInput {
      display: block;
      margin: 10px 0;
      position: relative;

      .fileInput {
        display: none;
      }

      span {
        display: block;
        cursor: pointer;
        background-color: #444444;
        color: white;
        padding: 10px;
        text-align: center;
        border-radius: 5px;
        opacity: 1;
      }
      .isEditForUser {
        opacity: 30%;
        cursor: default;
      }
    }
  }

  .error {
    color: red;
    margin-top: 10px;
    font-size: 12px;
  }

  .previews {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    justify-content: center;
    position: relative;

    .preview {
      width: 265px;
      height: 175px;
      overflow: hidden;
      border: 1px solid #ccc;
      border-radius: 5px;
      background-color: #fff;

      .previewImage {
        width: 100%;
        object-fit: cover;
      }
    }
  }

  .uploadButton {
    display: block;
    width: 100%;
    padding: 10px;
    background-color: #444444;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;

    &:hover {
      background-color: #444444;
    }
  }
}
.batchUpload {
  height: 100%;
  display: flex;
  gap: 20px;
  padding-right: 20px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  border-bottom: 3px solid red;
  display: inline-block;
  margin-top: 40px;
}

.media {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  .imageWrapper {
    flex: 1;
    max-width: 400px;
    width: 100%;
    background-color: white;
  }
  .imageName {
    padding: 0 20px;
    word-wrap: break-word;
  }
}

.deleteButton {
  position: absolute;
  z-index: 9;
  display: inline-block;
  right: 10px;
  top: 10px;
  background: white;
  display: flex;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
}
.content {
  display: flex;
  position: relative;
  .deleteIcon {
    cursor: pointer;
    position: absolute;
    top: 20px;
    right: 20px;
  }
}

.uploadContainer {
  width: 100%;
  height: fit-content;
  display: flex;
  gap: 20px;
}
