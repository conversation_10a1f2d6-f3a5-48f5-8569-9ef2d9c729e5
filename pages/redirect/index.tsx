import React, { useEffect } from 'react'
import { useRouter } from 'next/router'
import Loader from '@/src/component/shared/loader'
import { Routes, StorageKeys, userType } from '@/src/constant/enum'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { setLocalStorageItem } from '@/src/utils/storageUtils'

const Redirect = () => {
  const router = useRouter()
  const { getAccessTokenApi, logInUserApi } = useAuthorization()
  const { getMasterPeriodsApi, currentPeriod } = useMasterPeriod()

  const handleAuth = async (res: Record<string, any>) => {
    const code: any = router.query.code
    const redirectUrl = `${window.location.origin}/redirect`
    const getTokenResponse: Record<string, any> = await getAccessTokenApi({
      code: code,
      redirectUrl: redirectUrl,
    })
    if (getTokenResponse.payload.success) {
      const getUserResponse: Record<string, any> = await logInUserApi({
        period: res.payload.data.period || currentPeriod,
      })
      if (!getUserResponse.payload.success) return
      if (getUserResponse.payload.data.user_type.includes(userType.USER)) {
        router.push(`${Routes.PROJECTS}`)
      }
      setLocalStorageItem(StorageKeys.IS_LOGIN, true)
      router.push(`${Routes.HOME}`)
    }
  }

  const handleToken = async () => {
    const res: Record<string, any> = await getMasterPeriodsApi()
    if (!res.payload.success) return
    handleAuth(res)
  }

  useEffect(() => {
    handleToken()
  }, [router.query.code]) // Adding router.query.code to dependency array ensures it's only called when code changes

  return (
    <>
      <Loader />
    </>
  )
}

export default Redirect
