@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.tabData {
  background: #f1f1ef;
  flex: 1 1 auto;
  height: calc(100vh - 294px);

  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 5px;
  }
  @include respond-to('mobile') {
    margin-left: 20px;
  }

  @include respond-to('laptop') {
    margin-left: 0px;
  }
}

.collapseHeight {
  height: calc(100vh - 303px);
}
.isNotCollapseHeight {
  height: calc(100vh - 171px);
}
