import { useEffect } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { io, Socket } from 'socket.io-client'
import { BaseURL } from '@/src/api'
import Home from '@/src/component/home'
import { Routes, userType, whiteListedUserList } from '@/src/constant/enum'
import useAuthorization from '@/src/redux/authorization/useAuthorization'

let socket: Socket
const socketURL = BaseURL?.replace(/\/api\/?$/, '')

export default function HomePage() {
  const router = useRouter()
  const { currentUser } = useAuthorization()

  useEffect(() => {
    // Connect to Socket.IO server
    socket = io(socketURL, {
      transports: ['websocket'],
      reconnection: true,
    })

    // On success
    socket.on('connect', () => {
      console.log('✅ Socket connected: ', socket.id)
    })

    // On error
    socket.on('connect_error', (err) => {
      console.error('❌ Socket connect error:', err.message)
    })

    socket.on('error', (err) => {
      console.error('❌ Socket general error:', err)
    })

    return () => {
      socket.disconnect()
      console.log('🔌 Socket disconnected')
    }
  }, [])

  const redirectToProject = async () => {
    if (whiteListedUserList.includes(currentUser?.email)) {
      return
    } else if (currentUser?.user_type?.includes(userType.USER)) {
      router.push(Routes.PROJECTS)
    }
  }
  useEffect(() => {
    redirectToProject()
  }, [currentUser])

  return (
    <>
      <Head>
        <title>Admin Panel</title>
        <meta name="description" content="Home" />
      </Head>
      {/* NEW DESIGN    */}
      <Home />
      {/* <HomeHeader />   // OLD DESIGN IS COMMENTED
      <MasterCards /> */}
    </>
  )
}
