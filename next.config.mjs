import path from 'path'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

const nextConfig = {
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:4000/api/:path*',
      },
    ]
  },
  skipTrailingSlashRedirect: true,
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'alddadevblobstorage.blob.core.windows.net',
      },
      {
        protocol: 'https',
        hostname: 'alddaprodblobstorage.blob.core.windows.net',
      },
    ],
  },

  webpack: (config, { isServer }) => {
    // Resolve alias for styles directory
    config.resolve.alias['styles'] = path.resolve(__dirname, 'styles')
    config.resolve.alias['src'] = path.resolve(__dirname, 'src')
    return config
  },
  reactStrictMode: false,
  output: 'standalone',
  // output: 'export',
}

export default nextConfig
