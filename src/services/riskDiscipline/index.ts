import { IRiskDisciplinePayload, IUpdateRiskDiscipline } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_RISK_DISCIPLINE_URL = `/master-risk-discipline`

// Get Master Risk Discipline data
export const getRiskDiscipline = async () => {
  const response = await api.get(`${MASTER_RISK_DISCIPLINE_URL}`)
  return response?.data
}
export const deleteRiskDiscipline = async (data: number) => {
  const response = await api.delete(`${MASTER_RISK_DISCIPLINE_URL}/${data}`)
  return response?.data
}

// Create Master Risk Discipline entry
export const createRiskDiscipline = async (data: IRiskDisciplinePayload) => {
  const response = await api.post(`${MASTER_RISK_DISCIPLINE_URL}`, data)
  return response
}

// Update Master Risk Discipline entry
export const updateRiskDiscipline = async (data: IUpdateRiskDiscipline): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_RISK_DISCIPLINE_URL}/${data.id}`, { ...rest })
  return response
}
