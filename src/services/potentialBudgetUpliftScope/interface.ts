export interface IPotentialBudgetUpliftScopePayload {
  period: string
  project_name: string
  current_scope: string | null
  amended_scope: string | null
  cost_impact: number | null
}

export interface IUpdatePotentialBudgetUpliftScope extends IPotentialBudgetUpliftScopePayload {
  id: number
  period: string
  project_name: string
}

export interface IPotentialBudgetUpliftScope {
  id: number
  period: string
  project_name: string
  current_scope: string | null
  amended_scope: string | null
  cost_impact: number | null
}

export interface IGetPotentialBudgetUpliftScope {
  data: IPotentialBudgetUpliftScope[]
  message: string
  success: true
}
