import { IContractTypePayload, IUpdateContractType } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_CONTRACT_TYPE_URL = `/master-contract-type`

// Get Master Contract Type data
export const getMasterContractType = async () => {
  const response = await api.get(`${MASTER_CONTRACT_TYPE_URL}`)
  return response?.data
}

export const deleteMasterContractType = async (data: number) => {
  const response = await api.delete(`${MASTER_CONTRACT_TYPE_URL}/${data}`)
  return response?.data
}

// Create Master Contract Type entry
export const createMasterContractType = async (data: IContractTypePayload) => {
  const response = await api.post(`${MASTER_CONTRACT_TYPE_URL}`, data)
  return response
}

// Update Master Contract Type entry
export const updateMasterContractType = async (data: IUpdateContractType): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_CONTRACT_TYPE_URL}/${data.id}`, { ...rest })
  return response
}
