import { AvatarItem } from '@/src/redux/avatars/interface'

export interface IControlManagerPayload {
  control_manager: string
  avatar?: string
  email: string
  phone_number: string
  location: string
}

export interface IUpdateControlManager extends IControlManagerPayload {
  id: number
}

export interface IControlManager {
  id: number
  control_manager: string
  avatar: AvatarItem
  email: string
  phone_number: string
  location: string
}

export interface IGetMasterControlManagersResponse {
  data: IControlManager[]
  message: string
  success: true
}
