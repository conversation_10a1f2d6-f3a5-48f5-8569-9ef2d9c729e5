import { IControlManagerPayload, IUpdateControlManager } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_CONTROL_MANAGER_URL = `/master-control-manager`

// Get Master Control Manager data
export const getMasterControlManagers = async () => {
  const response = await api.get(`${MASTER_CONTROL_MANAGER_URL}`)
  return response?.data
}

export const deleteMasterControlManager = async (data: number) => {
  const response = await api.delete(`${MASTER_CONTROL_MANAGER_URL}/${data}`)
  return response?.data
}

// Create Master Control Manager entry
export const createMasterControlManager = async (data: IControlManagerPayload) => {
  const response = await api.post(`${MASTER_CONTROL_MANAGER_URL}`, data)
  return response
}

// Update Master Control Manager entry
export const updateMasterControlManager = async (data: IUpdateControlManager): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_CONTROL_MANAGER_URL}/${data.id}`, { ...rest })
  return response
}
