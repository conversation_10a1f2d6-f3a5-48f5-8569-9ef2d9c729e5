export interface IDistrictPayload {
  district: string
}

export interface IUpdateDistrict extends IDistrictPayload {
  id: number
}

export interface IDistrict {
  id: number
  district: string
}

export interface IGetMasterDistrictsResponse {
  data: IDistrict[]
  message: string
  success: true
}

export interface ILocationPayload {
  location: string
}

export interface IUpdateLocation extends ILocationPayload {
  id: number
}

export interface ILocation {
  id: number
  location: string
}

export interface IGetMasterLocationsResponse {
  data: ILocation[]
  message: string
  success: true
}
