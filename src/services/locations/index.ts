import { ILocationPayload, IUpdateLocation } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_LOCATION_URL = `/master-location`

// Get Master Locations data
export const getMasterLocations = async () => {
  const response = await api.get(`${MASTER_LOCATION_URL}`)
  return response?.data
}

export const deleteMasterLocation = async (data: number) => {
  const response = await api.delete(`${MASTER_LOCATION_URL}/${data}`)
  return response?.data
}

// Create Master Locations entry
export const createMasterLocation = async (data: ILocationPayload) => {
  const response = await api.post(`${MASTER_LOCATION_URL}`, data)
  return response
}

// Update Master Locations entry
export const updateMasterLocation = async (data: IUpdateLocation): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_LOCATION_URL}/${data.id}`, { ...rest })
  return response
}
