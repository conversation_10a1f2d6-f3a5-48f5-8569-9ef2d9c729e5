export interface FeedbackPayload {
  currentPeriod: string
  rating: string
  workingWell: string
  doBetter: string
  attachment?: File[]
  id?: string | number
  pros_feedback?: string | number
  cons_feedback?: string | number
}

export interface IGetMasterFeedbackResponse {
  data: FeedbackPayload[]
  message: string
  success: true
}

export interface IUpdateFeedback extends FeedbackPayload {
  id: number
}
