import { IProjectStageStatusPayload, IUpdateProjectStageStatus } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PROJECT_STAGE_STATUS_URL = `/master-Project-Stage-Status`

// Get Master Project Stage Status data
export const getMasterProjectStageStatus = async () => {
  const response = await api.get(`${MASTER_PROJECT_STAGE_STATUS_URL}`)
  return response?.data
}

export const deleteMasterProjectStageStatus = async (data: number) => {
  const response = await api.delete(`${MASTER_PROJECT_STAGE_STATUS_URL}/${data}`)
  return response?.data
}

// Create Master Project Stage Status entry
export const createMasterProjectStageStatus = async (data: IProjectStageStatusPayload) => {
  const response = await api.post(`${MASTER_PROJECT_STAGE_STATUS_URL}`, data)
  return response
}

// Update Master Project Stage Status entry
export const updateMasterProjectStageStatus = async (data: IUpdateProjectStageStatus): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PROJECT_STAGE_STATUS_URL}/${data.id}`, { ...rest })
  return response
}
