import { IRiskOwnerPayload, IUpdateRiskOwner } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_RISK_OWNER_URL = `/master-risk-owner`

// Get Master Risk Owner data
export const getRiskOwner = async () => {
  const response = await api.get(`${MASTER_RISK_OWNER_URL}`)
  return response?.data
}
export const deleteRiskOwner = async (data: number) => {
  const response = await api.delete(`${MASTER_RISK_OWNER_URL}/${data}`)
  return response?.data
}

// Create Master Risk Owner entry
export const createRiskOwner = async (data: IRiskOwnerPayload) => {
  const response = await api.post(`${MASTER_RISK_OWNER_URL}`, data)
  return response
}

// Update Master Risk Owner entry
export const updateRiskOwner = async (data: IUpdateRiskOwner): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_RISK_OWNER_URL}/${data.id}`, { ...rest })
  return response
}
