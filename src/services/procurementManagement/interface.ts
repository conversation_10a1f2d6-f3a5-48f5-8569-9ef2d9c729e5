import { AvatarItem } from '@/src/redux/avatars/interface'

export interface IProcureManagementPayload {
  procurement_manager: string
  avatar?: string
  email: string
  phone_number: string
  location: string
}

export interface IUpdateProcureManagement extends IProcureManagementPayload {
  id: number
}

export interface IProcureManagement {
  id: number
  procurement_manager: string
  avatar: AvatarItem
  email: string
  phone_number: string
  location: string
}

export interface IGetMasterProcurementManagement {
  data: IProcureManagement[]
  message: string
  success: true
}
