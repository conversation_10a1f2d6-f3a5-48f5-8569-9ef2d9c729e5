import { IDirectorPayload, IUpdateDirector } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_DIRECTOR_URL = `/master-director`

// Get Master Directors data
export const getDirectors = async () => {
  const response = await api.get(`${MASTER_DIRECTOR_URL}`)
  return response?.data
}
export const deleteDirector = async (data: number) => {
  const response = await api.delete(`${MASTER_DIRECTOR_URL}/${data}`)
  return response?.data
}

// Create Master Directors entry
export const createDirector = async (data: IDirectorPayload) => {
  const response = await api.post(`${MASTER_DIRECTOR_URL}`, data)
  return response
}

// Update Master Directors entry
export const updateDirector = async (data: IUpdateDirector): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_DIRECTOR_URL}/${data.id}`, { ...rest })
  return response
}
