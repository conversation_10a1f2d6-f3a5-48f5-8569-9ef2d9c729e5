import { api } from '@/src/api/axios'

export const MASTER_PROJECT_PHASE_CATEGORY_URL = `/master-project-to-project-phase-categories`

// Get Master Project Phase Category data
export const getProjectToProjectPhaseCategories = async (period: string, project_name: string) => {
  const response =
    await api.get(`${MASTER_PROJECT_PHASE_CATEGORY_URL}?period=${period}&project_name=${encodeURIComponent(project_name)}
`)
  return response?.data
}
