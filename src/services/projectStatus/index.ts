import { IProjectStatusPayload, IUpdateProjectStatus } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PROJECT_STATUS_URL = `/master-Project-Status`

// Get Master Project Status data
export const getMasterProjectStatuses = async () => {
  const response = await api.get(`${MASTER_PROJECT_STATUS_URL}`)
  return response?.data
}
export const deleteMasterProjectStatus = async (data: number) => {
  const response = await api.delete(`${MASTER_PROJECT_STATUS_URL}/${data}`)
  return response?.data
}

// Create Master Project Status entry
export const createMasterProjectStatus = async (data: IProjectStatusPayload) => {
  const response = await api.post(`${MASTER_PROJECT_STATUS_URL}`, data)
  return response
}

// Update Master Project Status entry
export const updateMasterProjectStatus = async (data: IUpdateProjectStatus): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PROJECT_STATUS_URL}/${data.id}`, { ...rest })
  return response
}
