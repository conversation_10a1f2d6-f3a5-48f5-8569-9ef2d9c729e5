import { IPricingTypePayload, IUpdatePricingType } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PRICING_TYPE_URL = `/master-pricing-type`

// Get Master Pricing Type data
export const getPricingType = async () => {
  const response = await api.get(`${MASTER_PRICING_TYPE_URL}`)
  return response?.data
}

export const deletePricingType = async (data: number) => {
  const response = await api.delete(`${MASTER_PRICING_TYPE_URL}/${data}`)
  return response?.data
}

// Create Master Pricing Type entry
export const createPricingType = async (data: IPricingTypePayload) => {
  const response = await api.post(`${MASTER_PRICING_TYPE_URL}`, data)
  return response
}

// Update Master Pricing Type entry
export const updatePricingType = async (data: IUpdatePricingType): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PRICING_TYPE_URL}/${data.id}`, { ...rest })
  return response
}
