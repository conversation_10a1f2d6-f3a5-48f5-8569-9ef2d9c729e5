import { IProjectSubStagePayload, IUpdateProjectSubStage } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PROJECT_SUB_STAGE_URL = `/master-project-sub-stage`

// Get Master Project Sub Stage data
export const getMasterProjectSubStage = async () => {
  const response = await api.get(`${MASTER_PROJECT_SUB_STAGE_URL}`)
  return response?.data
}

export const deleteMasterProjectSubStage = async (data: number) => {
  const response = await api.delete(`${MASTER_PROJECT_SUB_STAGE_URL}/${data}`)
  return response?.data
}

// Create Master Project Sub Stage entry
export const createMasterProjectSubStage = async (data: IProjectSubStagePayload) => {
  const response = await api.post(`${MASTER_PROJECT_SUB_STAGE_URL}`, data)
  return response
}

// Update Master Project Sub Stage entry
export const updateMasterProjectSubStage = async (data: IUpdateProjectSubStage): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PROJECT_SUB_STAGE_URL}/${data.id}`, { ...rest })
  return response
}
