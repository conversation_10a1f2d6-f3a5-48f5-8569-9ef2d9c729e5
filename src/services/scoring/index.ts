import { IScoringPayload, IUpdateScoring } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_SCORING_URL = `/score`

// Get Master Scoring data
export const getMasterScoring = async (quarter: string) => {
  const params = { quarter: quarter }
  const response = await api.get(`${MASTER_SCORING_URL}`, { params })
  return response?.data
}

export const deleteMasterScoring = async (data: number) => {
  const response = await api.delete(`${MASTER_SCORING_URL}/${data}`)
  return response?.data
}

export const deleteQuarterScoring = async (data: number) => {
  const response = await api.delete(`${MASTER_SCORING_URL}/quarter/${data}`)
  return response?.data
}

// Create Master Scoring entry
export const createMasterScoring = async (data: IUpdateScoring) => {
  const response = await api.post(`${MASTER_SCORING_URL}`, data)
  return response
}

// Update Master Scoring entry
export const updateMasterScoring = async (data: IUpdateScoring): Promise<any> => {
  const response = await api.put(`${MASTER_SCORING_URL}`, { ...data })
  return response
}
