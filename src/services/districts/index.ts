import { IDistrictPayload, IUpdateDistrict } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_DISTRICT_URL = `/master-district`

/**
 * Get Master Districts data
 * @returns Master Districts data
 */
export const getMasterDistricts = async () => {
  const response = await api.get(`${MASTER_DISTRICT_URL}`)
  return response?.data
}

/**
 * Delete Master Districts entry
 * @param data - District ID
 * @returns Master Districts data
 */
export const deleteMasterDistrict = async (data: number) => {
  const response = await api.delete(`${MASTER_DISTRICT_URL}/${data}`)
  return response?.data
}

/**
 * Create Master Districts entry
 * @param data - District data
 * @returns Master Districts data
 */
export const createMasterDistrict = async (data: IDistrictPayload) => {
  const response = await api.post(`${MASTER_DISTRICT_URL}`, data)
  return response
}

/**
 * Update Master Districts entry
 * @param data - District data
 * @returns Master Districts data
 */
export const updateMasterDistrict = async (data: IUpdateDistrict): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_DISTRICT_URL}/${data.id}`, { ...rest })
  return response
}
