import { IDesignProjectManagerPayload, IUpdateDesignProjectManager } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_DESIGN_PROJECT_MANAGER_URL = `/master-design-project-manager`

// Get Master Design Project Manager data
export const getMasterDesignManagers = async () => {
  const response = await api.get(`${MASTER_DESIGN_PROJECT_MANAGER_URL}`)
  return response?.data
}
export const deleteMasterDesignManager = async (data: number) => {
  const response = await api.delete(`${MASTER_DESIGN_PROJECT_MANAGER_URL}/${data}`)
  return response?.data
}

// Create Master Design Project Manager entry
export const createMasterDesignManager = async (data: IDesignProjectManagerPayload) => {
  const response = await api.post(`${MASTER_DESIGN_PROJECT_MANAGER_URL}`, data)
  return response
}

// Update Master Design Project Manager entry
export const updateMasterDesignManager = async (data: IUpdateDesignProjectManager): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_DESIGN_PROJECT_MANAGER_URL}/${data.id}`, { ...rest })
  return response
}
