import { AvatarItem } from '@/src/redux/avatars/interface'

export interface IProjectOwnerPayload {
  design_executive_director: string
  avatar?: string
  phone_number: string
  email: string
  location: string
}

export interface IUpdateProjectOwner extends IProjectOwnerPayload {
  id: number
}

export interface IProjectOwner {
  id: number
  design_executive_director: string
  avatar: AvatarItem
  phone_number: string
  email: string
  location: string
}

export interface IGetProjectOwnerResponse {
  data: IProjectOwner[]
  message: string
  success: true
}
