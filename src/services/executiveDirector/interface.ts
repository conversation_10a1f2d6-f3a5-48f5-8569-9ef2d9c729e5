import { AvatarItem } from '@/src/redux/avatars/interface'

export interface IExecutiveDirectorPayload {
  executive_director: string
  avatar?: string
  email: string
  phone_number: string
  location: string
}

export interface IUpdateExecutiveDirector extends IExecutiveDirectorPayload {
  id: number
}

export interface IExecutiveDirector {
  id: number
  executive_director: string
  avatar: AvatarItem
  email: string
  phone_number: string
  location: string
}

export interface IGetMasterExecutiveDirectorsResponse {
  data: IExecutiveDirector[]
  message: string
  success: true
}
