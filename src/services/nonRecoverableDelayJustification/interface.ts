export interface INonRecoverableDelayJustificationPayload {
  non_recoverable_delay_justification: string
  non_recoverable_delay_justification_code: string
}

export interface IUpdateNonRecoverableDelayJustification extends INonRecoverableDelayJustificationPayload {
  id: number
}

export interface INonRecoverableDelayJustification {
  id: number
  non_recoverable_delay_justification: string
  non_recoverable_delay_justification_code: string
}

export interface IGetNonRecoverableDelayJustificationRes {
  data: INonRecoverableDelayJustification[]
  message: string
  success: true
}
