import { INonRecoverableDelayJustificationPayload, IUpdateNonRecoverableDelayJustification } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_NON_RECOVERABLE_DELAY_JUSTIFICATION_URL = `/master-non-recoverable-delay-justification`

// Get Master Non Recoverable Delay Justifications data
export const getNonRecoverableDelayJustifications = async () => {
  const response = await api.get(`${MASTER_NON_RECOVERABLE_DELAY_JUSTIFICATION_URL}`)
  return response?.data
}

export const deleteNonRecoverableDelayJustification = async (data: number) => {
  const response = await api.delete(`${MASTER_NON_RECOVERABLE_DELAY_JUSTIFICATION_URL}/${data}`)
  return response?.data
}

// Create Master Non Recoverable Delay Justifications entry
export const createNonRecoverableDelayJustification = async (data: INonRecoverableDelayJustificationPayload) => {
  const response = await api.post(`${MASTER_NON_RECOVERABLE_DELAY_JUSTIFICATION_URL}`, data)
  return response
}

// Update Master Non Recoverable Delay Justifications entry
export const updateNonRecoverableDelayJustification = async (
  data: IUpdateNonRecoverableDelayJustification,
): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_NON_RECOVERABLE_DELAY_JUSTIFICATION_URL}/${data.id}`, { ...rest })
  return response
}
