import { IProjectPhaseCategoryPayload, IUpdateProjectPhaseCategory } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_PROJECT_PHASE_CATEGORY_URL = `/master-project-phase-category`

// Get Master Project Phase Category data
export const getProjectPhaseCategories = async () => {
  const response = await api.get(`${MASTER_PROJECT_PHASE_CATEGORY_URL}`)
  return response?.data
}

export const deleteProjectPhaseCategory = async (data: number) => {
  const response = await api.delete(`${MASTER_PROJECT_PHASE_CATEGORY_URL}/${data}`)
  return response?.data
}

// Create Master Project Phase Category entry
export const createProjectPhaseCategory = async (data: IProjectPhaseCategoryPayload) => {
  const response = await api.post(`${MASTER_PROJECT_PHASE_CATEGORY_URL}`, data)
  return response
}

// Update Master Project Phase Category entry
export const updateProjectPhaseCategory = async (data: IUpdateProjectPhaseCategory): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_PROJECT_PHASE_CATEGORY_URL}/${data.id}`, { ...rest })
  return response
}
