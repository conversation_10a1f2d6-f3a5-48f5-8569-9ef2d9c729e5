import { INewsFeedPayload, IUpdateNewsFeed } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_NEWS_FEED_URL = `/news-feed`

// Get Master News Feed data
export const getNewsFeed = async (period: string) => {
  const response = await api.get(`${MASTER_NEWS_FEED_URL}?period=${period}`)
  return response?.data
}

// Delete Master News Feed entry
export const deleteNewsFeed = async (data: number) => {
  const response = await api.delete(`${MASTER_NEWS_FEED_URL}/${data}`)
  return response?.data
}

// Create Master News Feed entry
export const createNewsFeed = async (data: INewsFeedPayload) => {
  const response = await api.post(`${MASTER_NEWS_FEED_URL}`, data)
  return response
}

// Update Master News Feed entry
export const updateNewsFeed = async (data: IUpdateNewsFeed): Promise<any> => {
  const response = await api.put(`${MASTER_NEWS_FEED_URL}`, { ...data })
  return response
}
