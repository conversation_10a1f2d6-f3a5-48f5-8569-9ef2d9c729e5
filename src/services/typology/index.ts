import { ITypologyPayload, IUpdateTypology } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_TYPOLOGY_URL = `/master-typology`

// Get Master Typology data
export const getMasterTypologies = async () => {
  const response = await api.get(`${MASTER_TYPOLOGY_URL}`)
  return response?.data
}

export const deleteMasterTypology = async (data: number) => {
  const response = await api.delete(`${MASTER_TYPOLOGY_URL}/${data}`)
  return response?.data
}

// Create Master Typology entry
export const createMasterTypology = async (data: ITypologyPayload) => {
  const response = await api.post(`${MASTER_TYPOLOGY_URL}`, data)
  return response
}

// Update Master Typology entry
export const updateMasterTypology = async (data: IUpdateTypology): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_TYPOLOGY_URL}/${data.id}`, { ...rest })
  return response
}
