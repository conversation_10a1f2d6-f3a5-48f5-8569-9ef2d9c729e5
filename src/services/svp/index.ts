import { ISvpPayload, IUpdateSvp } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_SVP_URL = `/master-svp`

// Get Master SVP data
export const getSvps = async () => {
  const response = await api.get(`${MASTER_SVP_URL}`)
  return response?.data
}
export const deleteSvp = async (data: number) => {
  const response = await api.delete(`${MASTER_SVP_URL}/${data}`)
  return response?.data
}

// Create Master SVP entry
export const createSvp = async (data: ISvpPayload) => {
  const response = await api.post(`${MASTER_SVP_URL}`, data)
  return response
}

// Update Master SVP entry
export const updateSvp = async (data: IUpdateSvp): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_SVP_URL}/${data.id}`, { ...rest })
  return response
}
