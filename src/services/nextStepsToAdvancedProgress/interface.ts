export interface INstAdvancedProgressPayload {
  next_steps_to_advance_progress: string
  next_steps_to_advance_progress_code: string
}

export interface IUpdateNstAdvancedProgress extends INstAdvancedProgressPayload {
  id: number
}

export interface INstAdvancedProgress {
  id: number
  next_steps_to_advance_progress: string
  next_steps_to_advance_progress_code: string
}

export interface IGetMasterNstAdvancedProgressRes {
  data: INstAdvancedProgress[]
  message: string
  success: true
}
