import { INstAdvancedProgressPayload, IUpdateNstAdvancedProgress } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_NEXT_STEPS_TO_ADVANCE_PROGRESS_URL = `/master-next-steps-to-advance-progress`

// Get Master Next Steps to Advance Progress data
export const getMasterNstAdvancedProgresses = async () => {
  const response = await api.get(`${MASTER_NEXT_STEPS_TO_ADVANCE_PROGRESS_URL}`)
  return response?.data
}
export const deleteMasterNstAdvancedProgress = async (data: number) => {
  const response = await api.delete(`${MASTER_NEXT_STEPS_TO_ADVANCE_PROGRESS_URL}/${data}`)
  return response?.data
}

// Create Master Next Steps to Advance Progress entry
export const createMasterNstAdvancedProgress = async (data: INstAdvancedProgressPayload) => {
  const response = await api.post(`${MASTER_NEXT_STEPS_TO_ADVANCE_PROGRESS_URL}`, data)
  return response
}

// Update Master Next Steps to Advance Progress entry
export const updateMasterNstAdvancedProgress = async (data: IUpdateNstAdvancedProgress): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_NEXT_STEPS_TO_ADVANCE_PROGRESS_URL}/${data.id}`, { ...rest })
  return response
}
