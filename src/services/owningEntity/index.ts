import { IOwningEntityPayload, IUpdateOwningEntity } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_OWNING_ENTITY_URL = `/master-owning-entity`

// Get Master Owning Entity data
export const getMasterOwningEntity = async () => {
  const response = await api.get(`${MASTER_OWNING_ENTITY_URL}`)
  return response?.data
}
export const deleteMasterOwningEntity = async (data: number) => {
  const response = await api.delete(`${MASTER_OWNING_ENTITY_URL}/${data}`)
  return response?.data
}

// Create Master Owning Entity entry
export const createMasterOwningEntity = async (data: IOwningEntityPayload) => {
  const response = await api.post(`${MASTER_OWNING_ENTITY_URL}`, data)
  return response
}

// Update Master Owning Entity entry
export const updateMasterOwningEntity = async (data: IUpdateOwningEntity): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_OWNING_ENTITY_URL}/${data.id}`, { ...rest })
  return response
}
