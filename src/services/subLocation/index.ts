import { ISubLocationPayload, IUpdateSubLocation } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_SUB_LOCATION_URL = `/master-sub-location`

// Get Master Sub Locations data
export const getMasterSubLocations = async () => {
  const response = await api.get(`${MASTER_SUB_LOCATION_URL}`)
  return response?.data
}

export const deleteMasterSubLocations = async (data: number) => {
  const response = await api.delete(`${MASTER_SUB_LOCATION_URL}/${data}`)
  return response?.data
}

// Create Master Sub Locations entry
export const createMasterSubLocations = async (data: ISubLocationPayload) => {
  const response = await api.post(`${MASTER_SUB_LOCATION_URL}`, data)
  return response
}

// Update Master Sub Locations entry
export const updateMasterSubLocations = async (data: IUpdateSubLocation): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_SUB_LOCATION_URL}/${data.id}`, { ...rest })
  return response
}
