import { IReasonForDelayPayload, IUpdateReasonForDelay } from './interface'
import { api } from '@/src/api/axios'

export const MASTER_REASON_FOR_DELAY_URL = `/master-reason-for-delay`

// Get Master Reason For Delay data
export const getReasonForDelays = async () => {
  const response = await api.get(`${MASTER_REASON_FOR_DELAY_URL}`)
  return response?.data
}
export const deleteReasonForDelay = async (data: number) => {
  const response = await api.delete(`${MASTER_REASON_FOR_DELAY_URL}/${data}`)
  return response?.data
}

// Create Master Reason For Delay entry
export const createReasonForDelay = async (data: IReasonForDelayPayload) => {
  const response = await api.post(`${MASTER_REASON_FOR_DELAY_URL}`, data)
  return response
}

// Update Master Reason For Delay entry
export const updateReasonForDelay = async (data: IUpdateReasonForDelay): Promise<any> => {
  const { id, ...rest } = data
  const response = await api.put(`${MASTER_REASON_FOR_DELAY_URL}/${data.id}`, { ...rest })
  return response
}
