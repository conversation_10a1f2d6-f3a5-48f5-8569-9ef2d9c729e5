import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterRatingResponse, ISubOwningEntityState } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: ISubOwningEntityState = {
  getMasterSubOwningEntityStatus: StatusEnum.Idle,
  addMasterSubOwningEntityStatus: StatusEnum.Idle,
  deleteMasterSubOwningEntityStatus: StatusEnum.Idle,
  updateMasterSubOwningEntityStatus: StatusEnum.Idle,
  subOwningEntities: [],
  subOwningEntity: {
    id: 0,
    sub_owning_entity: '',
  },
}

export const getMasterSubOwningEntity = createAsyncThunk('/get-master-sub-owning-entity', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-sub-owning-entity`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addMasterSubOwningEntity = createAsyncThunk(
  '/add-master-sub-owning-entity',
  async (data: { sub_owning_entity: string }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/master-sub-owning-entity`, {
        sub_owning_entity: data.sub_owning_entity,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateMasterSubOwningEntity = createAsyncThunk(
  '/update-master-sub-owning-entity',
  async (data: { id: number; sub_owning_entity: string }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/master-sub-owning-entity/${data.id}`, {
        sub_owning_entity: data.sub_owning_entity,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteMasterSubOwningEntity = createAsyncThunk(
  '/delete-master-sub-owning-entity',
  async (data: number, thunkAPI) => {
    try {
      const response = await ApiDeleteNoAuth(`/master-sub-owning-entity/${data}`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const subOwningEntitySlice = createSlice({
  name: 'subOwningEntity',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getMasterSubOwningEntity
    builder.addCase(getMasterSubOwningEntity.pending, (state) => {
      state.getMasterSubOwningEntityStatus = StatusEnum.Pending
    })
    builder.addCase(getMasterSubOwningEntity.fulfilled, (state, action) => {
      state.getMasterSubOwningEntityStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterRatingResponse
      state.subOwningEntities = actionPayload.data
    })
    builder.addCase(getMasterSubOwningEntity.rejected, (state) => {
      state.getMasterSubOwningEntityStatus = StatusEnum.Failed
    })
    //addMasterSubOwningEntity
    builder.addCase(addMasterSubOwningEntity.pending, (state) => {
      state.addMasterSubOwningEntityStatus = StatusEnum.Pending
    })
    builder.addCase(addMasterSubOwningEntity.fulfilled, (state, action) => {
      state.addMasterSubOwningEntityStatus = StatusEnum.Success
    })
    builder.addCase(addMasterSubOwningEntity.rejected, (state) => {
      state.addMasterSubOwningEntityStatus = StatusEnum.Failed
    })
    //updateMasterSubOwningEntity
    builder.addCase(updateMasterSubOwningEntity.pending, (state) => {
      state.updateMasterSubOwningEntityStatus = StatusEnum.Pending
    })
    builder.addCase(updateMasterSubOwningEntity.fulfilled, (state, action) => {
      state.updateMasterSubOwningEntityStatus = StatusEnum.Success
    })
    builder.addCase(updateMasterSubOwningEntity.rejected, (state) => {
      state.updateMasterSubOwningEntityStatus = StatusEnum.Failed
    })
    //deleteMasterSubOwningEntity
    builder.addCase(deleteMasterSubOwningEntity.pending, (state) => {
      state.deleteMasterSubOwningEntityStatus = StatusEnum.Pending
    })
    builder.addCase(deleteMasterSubOwningEntity.fulfilled, (state, action) => {
      state.deleteMasterSubOwningEntityStatus = StatusEnum.Success
    })
    builder.addCase(deleteMasterSubOwningEntity.rejected, (state) => {
      state.deleteMasterSubOwningEntityStatus = StatusEnum.Failed
    })
  },
})

export const {} = subOwningEntitySlice.actions

// Export the reducer
export default subOwningEntitySlice.reducer
