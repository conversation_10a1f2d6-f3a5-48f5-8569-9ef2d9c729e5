import { StatusEnum } from '../types'

export interface ISubOwningEntityState {
  getMasterSubOwningEntityStatus: StatusEnum
  addMasterSubOwningEntityStatus: StatusEnum
  deleteMasterSubOwningEntityStatus: StatusEnum
  updateMasterSubOwningEntityStatus: StatusEnum
  subOwningEntities: ISubOwningEntity[]
  subOwningEntity: ISubOwningEntity
}
export interface ISubOwningEntity {
  id: number
  sub_owning_entity: string
}

export interface IGetMasterRatingResponse {
  data: ISubOwningEntity[]
  message: string
  success: true
}
