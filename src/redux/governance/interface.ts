import { StatusEnum } from '../types'

export interface IConsultantStatus {
  getGovernanceStatus: StatusEnum
  addGovernanceStatus: StatusEnum
  updateGovernanceStatus: StatusEnum
  deleteGovernanceStatus: StatusEnum
  governances: IGovernance[]
  governance: IGovernance
}

export interface IGovernance {
  id?: string | number
  period?: string | null
  project_name?: string
  initiation?: number
  ldc_procurement?: number
  design?: number
  contractor_procurement?: number
  construction?: number
  handover?: number
  // overall?: number
  last_updated?: Date
}

export interface IGetGovernanceResponse {
  data: IGovernance[]
  message: string
  success: true
}
