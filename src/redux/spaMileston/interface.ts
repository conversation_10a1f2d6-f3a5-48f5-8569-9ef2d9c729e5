import { ILookupProjectToPhase } from '../status/interface'
import { StatusEnum } from '../types'
import { IMilestoneNumber } from '@/src/services/milestoneNumber/interface'

export interface ISpaStatus {
  getSpaStatus: StatusEnum
  addSpaStatus: StatusEnum
  updateSpaStatus: StatusEnum
  spaSortStatus: StatusEnum
  deleteSpaStatus: StatusEnum
  spas: ISpa[]
  spa: ISpa
}

export interface ILetterRefAttach {
  url: string
  name: string
}

export interface ISpa {
  id?: number
  period?: string
  isEdit?: boolean
  project_name?: string
  milestone_description?: string
  LookupProjectToPhase?: ILookupProjectToPhase
  lookup_project_to_phase_id?: number | null
  milestone_date?: string
  // plan_end_date?: string
  // forecasted_end_date?: string
  milestone_sorting_order?: number
  permit_and_land_clearances_date?: string
  SPA_Column_1?: string
  SPA_Column_2?: string
  SPA_Column_3?: string
  SPA_Column_4?: string
  milestone_status?: string
  SPA_Column_5?: string
  SPA_Column_6?: string
  milestone_number?: number
  master_milestone_number_id?: number
  MasterMilestoneNumber?: IMilestoneNumber
  revised_spa_date?: string
  spa_variance?: string
  communication_variance?: string
  contract_variance?: string
  letter_reference?: string
  letter_reference_attachment?: ILetterRefAttach[]
  milestone_type?: string
  duration_from_start?: number
  delay_damage_per_diem?: number
  // status?: string
  conditions_precedence?: string
  justification?: string
  remarks?: string
  actual_forecast_milestone_collection?: string | null
  revised_spa_communication?: string
  baseline_end_date?: string
  eot?: string
  forecasted_end_date?: string
  actual_forecast_milestone_completion?: string
  planned_completion_date?: string
  contract_date?: string
  latest_communication_date?: string
  last_updated?: Date
}

export interface IGetSpaResponse {
  data: ISpa[]
  message: string
  success: true
}
