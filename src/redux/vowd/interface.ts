import { StatusEnum } from '../types'

export interface IVOWDStates {
  getAllVowdsStatus: StatusEnum
  addVowdStatus: StatusEnum
  deleteVowdStatus: StatusEnum
  updateVowdStatus: StatusEnum
  vowds: IVowds[]
}

export interface IVowds {
  budget: string
  forecast_budget: string
  id: number
  input_date: string
  justification: string
  last_updated: string | null
  project_summary_id: number
  updated_by: string | null
  vowd_status: string
}

export interface IGetVowdStates {
  data: IVowds[]
  message: string
  success: true
}
