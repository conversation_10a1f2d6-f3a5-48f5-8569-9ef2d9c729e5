import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetVowdStates, IVOWDStates } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IVOWDStates = {
  getAllVowdsStatus: StatusEnum.Idle,
  addVowdStatus: StatusEnum.Idle,
  deleteVowdStatus: StatusEnum.Idle,
  updateVowdStatus: StatusEnum.Idle,
  vowds: [],
}

export const getAllVowds = createAsyncThunk('/get-vowd', async (data: { projectSummaryId: number }, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/vowd?project_summary_id=${data?.projectSummaryId}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const getVowd = createAsyncThunk(
  '/get-vowd',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    try {
      const response = await ApiGetNoAuth(`/vowd`)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addVowd = createAsyncThunk('/add-vowd', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/vowd`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateVowd = createAsyncThunk('/update-vowd', async (data: { id: number; data: any }, thunkAPI) => {
  const vowd = data.data
  delete vowd.id
  try {
    const response = await ApiPutNoAuth(`/vowd/${data.id}`, vowd)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const deleteVowd = createAsyncThunk('/delete-vowd', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/vowd/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const vowdSlice = createSlice({
  name: 'vowd',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    builder.addCase(getAllVowds.pending, (state) => {
      state.getAllVowdsStatus = StatusEnum.Pending
    })
    builder.addCase(getAllVowds.fulfilled, (state, action) => {
      state.getAllVowdsStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetVowdStates
      state.vowds = actionPayload.data
    })
    builder.addCase(getAllVowds.rejected, (state) => {
      state.getAllVowdsStatus = StatusEnum.Failed
    })
    // //addContractorAndVos
    builder.addCase(addVowd.pending, (state) => {
      state.addVowdStatus = StatusEnum.Pending
    })
    builder.addCase(addVowd.fulfilled, (state, action) => {
      state.addVowdStatus = StatusEnum.Success
    })
    builder.addCase(addVowd.rejected, (state) => {
      state.addVowdStatus = StatusEnum.Failed
    })
    // //updateContractorAndVos
    builder.addCase(updateVowd.pending, (state) => {
      state.updateVowdStatus = StatusEnum.Pending
    })
    builder.addCase(updateVowd.fulfilled, (state, action) => {
      state.updateVowdStatus = StatusEnum.Success
    })
    builder.addCase(updateVowd.rejected, (state) => {
      state.updateVowdStatus = StatusEnum.Failed
    })
    // //deleteContractorAndVos
    builder.addCase(deleteVowd.pending, (state) => {
      state.deleteVowdStatus = StatusEnum.Pending
    })
    builder.addCase(deleteVowd.fulfilled, (state, action) => {
      state.deleteVowdStatus = StatusEnum.Success
    })
    builder.addCase(deleteVowd.rejected, (state) => {
      state.deleteVowdStatus = StatusEnum.Failed
    })
  },
})

export const {} = vowdSlice.actions

// Export the reducer
export default vowdSlice.reducer
