import { useDispatch, useSelector } from 'react-redux'
import { addVowd, deleteVowd, getAllVowds, updateVowd } from '.'
import { AppDispatch, RootState } from '@/src/redux/store'

const useVowd = () => {
  const dispatch: AppDispatch = useDispatch()

  const getVowdStatus = useSelector((state: RootState) => state.vowd.getAllVowdsStatus)
  const vowds = useSelector((state: RootState) => state.vowd.vowds)

  const getAllVowdApi = (data: { projectSummaryId: number }) => dispatch(getAllVowds(data))
  const addVowdApi = (data: any) => dispatch(addVowd(data))
  const updateVowdApi = (data: { id: number; data: any }) => dispatch(updateVowd(data))
  const deleteVowdApi = (data: number) => dispatch(deleteVowd(data))

  return {
    getVowdStatus,
    vowds,
    getAllVowdApi,
    addVowdApi,
    updateVowdApi,
    deleteVowdApi,
  }
}
export default useVowd
