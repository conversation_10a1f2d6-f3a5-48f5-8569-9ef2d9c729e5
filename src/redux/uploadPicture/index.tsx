import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import FileState from './interface'
import { IDeleteDocProjectManagementPayload } from '../projectManagement/interface'
import { StatusEnum } from '../types'
import { ApiPostNoAuth, ApiGetNoAuth, ApiPost } from '@/src/api'

const initialState: FileState = {
  uploadFileStatus: StatusEnum.Idle,
  getImageStatus: StatusEnum.Idle,
  deleteFileStatus: StatusEnum.Idle,
  updatePhaseStatus: StatusEnum.Idle,
  files: '',
  project_name: '',
  field_type: '',
  period: '',
}

export const uploadFile = createAsyncThunk('/files/upload', async (formData: FormData, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/files/upload`, formData, {
      isFormData: true,
    })
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const deleteFile = createAsyncThunk(
  '/files/remove-files',
  async (data: IDeleteDocProjectManagementPayload, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/files/remove-file`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updatePhase = createAsyncThunk('/files/update-phase', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/files/update-phase`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateOverallPhaseWithMultiPhase = createAsyncThunk(
  '/project-status/update-overall-phase',
  async (data: any, thunkAPI) => {
    try {
      const response = await ApiPost(`/project-status/update-overall-phase`, data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const fetchImage = createAsyncThunk('/files/fetch', async (data: any, thunkAPI) => {
  try {
    const fileName = encodeURIComponent(data.name)
    const projectName = encodeURIComponent(data.projectName)
    const response = await ApiGetNoAuth(
      `/files/fetch?name=${fileName}&projectName=${projectName}&period=${data.period}`,
    )
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

const fileSlice = createSlice({
  name: 'file',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // Reducers for file upload
    builder.addCase(uploadFile.pending, (state) => {
      state.uploadFileStatus = StatusEnum.Pending
    })
    builder.addCase(uploadFile.fulfilled, (state, action) => {
      state.uploadFileStatus = StatusEnum.Success
    })
    builder.addCase(uploadFile.rejected, (state) => {
      state.uploadFileStatus = StatusEnum.Failed
    })

    // Reducers for file delete
    builder.addCase(deleteFile.pending, (state) => {
      state.deleteFileStatus = StatusEnum.Pending
    })
    builder.addCase(deleteFile.fulfilled, (state, action) => {
      state.deleteFileStatus = StatusEnum.Success
    })
    builder.addCase(deleteFile.rejected, (state) => {
      state.deleteFileStatus = StatusEnum.Failed
    })

    // Reducers for updatePhase
    builder.addCase(updatePhase.pending, (state) => {
      state.updatePhaseStatus = StatusEnum.Pending
    })
    builder.addCase(updatePhase.fulfilled, (state, action) => {
      state.updatePhaseStatus = StatusEnum.Success
    })
    builder.addCase(updatePhase.rejected, (state) => {
      state.updatePhaseStatus = StatusEnum.Failed
    })

    // Reducers for fetchImage
    builder.addCase(fetchImage.pending, (state) => {
      state.uploadFileStatus = StatusEnum.Pending
    })
    builder.addCase(fetchImage.fulfilled, (state, action) => {
      state.uploadFileStatus = StatusEnum.Success
    })
    builder.addCase(fetchImage.rejected, (state) => {
      state.uploadFileStatus = StatusEnum.Failed
    })
  },
})

// Export actions from slice
export const {} = fileSlice.actions

// Export the reducer
export default fileSlice.reducer
