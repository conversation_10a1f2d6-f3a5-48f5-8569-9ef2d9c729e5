import { useDispatch, useSelector } from 'react-redux'
import { deleteFile, fetchImage, updatePhase, updateOverallPhaseWithMultiPhase, uploadFile } from '.'
import { IDeleteDocProjectManagementPayload } from '../projectManagement/interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useFile = () => {
  const dispatch: AppDispatch = useDispatch()

  const UploadFileApi = (data: any) => dispatch(uploadFile(data))
  const deleteFileApi = (data: IDeleteDocProjectManagementPayload) => dispatch(deleteFile(data))
  const updatePhaseApi = (data: any) => dispatch(updatePhase(data))
  const updateOverallPhaseWithMultiPhaseApi = (data: any) => dispatch(updateOverallPhaseWithMultiPhase(data))
  const getImageApi = (data: any) => dispatch(fetchImage(data))

  return {
    UploadFileApi,
    getImage<PERSON>pi,
    deleteFile<PERSON>pi,
    updatePhase<PERSON>pi,
    updateOverallPhaseWithMultiPhaseApi,
  }
}

export default useFile
