import { IProjectManagement } from '../projectManagement/interface'
import { StatusEnum } from '../types'
import { IContractor } from '@/src/services/contractors/interface'
import { IProcureManagement } from '@/src/services/procurementManagement/interface'

export interface IStatusState {
  getStatusProcess: StatusEnum
  getOneStatusProcess: StatusEnum
  addStatusProcess: StatusEnum
  deleteStatusProcess: StatusEnum
  updateStatusProcess: StatusEnum
  updateTableStatusProcess: StatusEnum
  statusSortingStatus: StatusEnum
  renamePhaseProcess: StatusEnum
  addPhaseProcess: StatusEnum
  deletePhaseProcess: StatusEnum
  statuses: IStatus[]
  status: IStatus | null
  progressTableVisibilities: any
  progressFilterValue: { colId: string; values: any }[]
  progressTableColumnsWidth: Record<string, number>
  progressTableColumnsOrder: any
}

export interface IMasterProjectStageStatus {
  id: number
  project_stage_status: string
}
export interface IConsultant {
  consultant: string
  id: number
  long_name: string | null
}
export interface IMasterPMCConsultant {
  id: number
  long_name: string | null
  pmc_consultant: string
}
export interface ISupervisionConsultant {
  id: number
  long_name: string | null
  consultant: string
}
export interface IMasterContractor {
  contractor: string
  id: number
  long_name: string | null
}
export interface IMasterProjectPhaseCategory {
  id: number
  project_phase_category: string
}

export interface ILookupProjectToProjectPhaseCategory {
  MasterProjectPhaseCategory: IMasterProjectPhaseCategory
  id: number
}

export interface ILookupProjectToPhase extends ILookupProjectToProjectPhaseCategory {
  phase: string
}

export interface IMasterProjectSubStage {
  id: number
  project_sub_stage: string
}

export interface IDesignManager {
  id: number
  design_manager: string
  avatar: string
}

export interface IDeliveryProjectManager {
  id: number
  delivery_project_manager: string
  avatar: string
}

export interface ILookupProjectStatusPredecessor {
  project_status_id: number
  association_type: string
  DestinationProjectStatus: Partial<IStatus>
}

export interface ILookupProjectStatusSuccessor {
  project_status_id: number
  association_type: string
  DestinationProjectStatus: Partial<IStatus>
}

export interface IStatus {
  id?: number
  LookupProjectToPhase?: ILookupProjectToPhase[]
  LookupProjectToProjectPhaseCategory?: ILookupProjectToProjectPhaseCategory[]
  ProjectManagement?: IProjectManagement
  period?: string
  phase?: string
  project_name?: string
  variance_in_days?: string
  DesignManagers?: IDesignManager[]
  DeliveryProjectManagers?: IDeliveryProjectManager[]
  svp?: string
  MasterContractor?: IContractor
  MasterProjectStageStatus?: IMasterProjectStageStatus
  MasterProjectPhaseCategory?: IMasterProjectPhaseCategory
  MasterProjectSubStage?: IMasterProjectSubStage
  design_stage_weightage?: number
  executing_entity?: string
  phase_weightage?: number
  Consultant?: IConsultant
  contractor?: string
  revised_plan_perc?: string
  actual_plan_perc?: string
  contract_start_date?: string
  procurement_start_date?: string
  contract_end_date?: string
  decree_end_date?: string
  plan_start_date?: string
  plan_end_date?: string
  revised_plan_end_date?: string
  forecast_start_date?: string
  forecasted_end_date?: string
  key_highlights?: string
  latitude?: number
  longitude?: number
  MasterPMCConsultant?: IMasterPMCConsultant
  master_pmc_consultant_id?: number
  milestone_date?: string
  last_updated?: Date
  key_highlights_last_updated?: string
  key_highlights_updated_by?: string
  actual_progress_percentage_of_last_week?: number
  actual_progress_percentage_for_last_week?: number
  eot_to_contractor?: string
  eot_to_entity?: string
  SupervisionConsultant?: ISupervisionConsultant
  master_supervision_consultant_id?: number
  cost_per_sqm?: boolean
  project_status_sorting_order?: number
  bua?: number
  gfa?: number
  no_units?: number
  no_plots?: number
  updated_by?: string
  plan_contractor_progress_percentage?: string
  duration_in_days?: string
  buffer_in_days?: string
  is_contract_signature_received?: boolean
  is_advance_payment_bond_received?: boolean
  is_insurance_received?: boolean
  is_advance_payment_released?: boolean
  is_performance_bond_received?: boolean
  forecast_completion_last_week?: string | null
  kickoff_meeting_date?: string
  pte?: number
  pte_attachment?: string
  LookupProjectStatusPredecessor?: ILookupProjectStatusPredecessor[]
  LookupProjectStatusSuccessor?: ILookupProjectStatusSuccessor[]
  forecast_duration?: number | null
  plan_duration?: number | null
  projectPhaseCategory?: string
  MasterProcurementManager?: IProcureManagement
  sub_stage?: string
  stage_status?: string
  actual_plan_percentage?: number
  actual_progress_percentage?: number
  revised_plan_percentage?: number
  actual_percentage?: number
  rev_plan_percentage?: number
  planned_progress_percentage?: number
  baseline_plan_finish?: string
  revised_baseline_finish?: number
  forecast_finish?: string
  baselinePlanFinish?: string
  baseline_plan_finish_date?: string
  forecast_finish_date?: string
  calculated_plan_progress?: number | null
  slippage_justification?: string
  destination_project_status_id?: number
  project_to_project_phase_ids?: number[]
  project_to_project_phase_category_ids?: number[]
  master_project_stage_status_id?: number | null
  predecessor?: number[]
  successor?: number[]
  PreviousPeriodProjectStatus?: IPreviousPeriodProjectStatus
  time_elapsed_percentage?: number | null
  spi?: number | null
  delay_this_period?: number | null
  cumulative_delay?: number | null
  project_management_var?: number | null
}

export interface IPreviousPeriodProjectStatus {
  id: number
  period: string
  project_name: string
  design_stage_weightage: any
  executing_entity: string
  phase_weightage: number
  baseline_plan_finish: any
  plan_end_date: string
  forecast_finish: any
  forecasted_end_date: string
  forecast_finish_date: string
  rev_plan_percentage: any
  actual_percentage: any
  revised_plan_percentage: number
  planned_progress_percentage: number
  actual_plan_percentage: number
  actual_progress_percentage: number
  MasterProjectStageStatus: IMasterProjectStageStatus
  MasterProjectSubStage: any
}

export interface ExtraStatusFields {
  categoryIDs: string
  phaseIDs: string
  statusID: string
  slippage_justification?: string
}

export interface IGetMasterRatingResponse {
  data: IStatus[]
  message: string
  success: true
}

export interface IRenamePhasePayload {
  id: number
  // period: string
  // project_name: string
  master_project_phase_category_id: number | null
  phase: string | null
}

// export interface IRenamePhasePayload {
//   id: string
//   newPhase: string | null
//   newCategory: string
// }

export interface IAddPhasePayload {
  period: string
  project_name: string
  master_project_phase_category_id: number
  phase: string | null
}

export interface IDeletePhasePayload {
  projectName: string
  period: string
  project_name: string
  master_project_phase_category_id: number
  phase: string
}

export interface IDropDownOptionFormate {
  value: number | string
  label: string
}

/* // export interface IDeletePhasePayload {
//   projectName: string
//   period: string
//   id: string
// }
 */
