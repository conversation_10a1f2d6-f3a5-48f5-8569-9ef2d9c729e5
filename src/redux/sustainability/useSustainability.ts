import { useDispatch, useSelector } from 'react-redux'
import { getSustainabilities, addSustainability, updateSustainability, deleteSustainability } from '.'
import { ISustainability } from './interface'
import { AppDispatch, RootState } from '@/src/redux/store'

const useSustainability = () => {
  const dispatch: AppDispatch = useDispatch()

  const getSustainabilitiesStatus = useSelector((state: RootState) => state.sustainability.getSustainabilitiesStatus)
  const sustainabilities = useSelector((state: RootState) => state.sustainability.sustainabilities)

  const getSustainabilitiesApi = (data: { period: string; project_name?: string }) =>
    dispatch(getSustainabilities(data))
  const addSustainabilityApi = (data: ISustainability) => dispatch(addSustainability(data))
  const updateSustainabilityApi = (data: { id: string; data: ISustainability }) => dispatch(updateSustainability(data))
  const deleteSustainabilityApi = (data: number) => dispatch(deleteSustainability(data))

  return {
    getSustainabilitiesStatus,
    sustainabilities,
    getSustainabilitiesApi,
    addSustainabilityApi,
    updateSustainability<PERSON>pi,
    deleteSustainabilityApi,
  }
}
export default useSustainability
