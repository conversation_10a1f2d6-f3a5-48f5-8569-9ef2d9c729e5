import { ILookupProjectToPhase } from '../status/interface'
import { StatusEnum } from '../types'

export interface IProjectKpiStates {
  getProjectKpiStates: StatusEnum
  addProjectKpiStates: StatusEnum
  deleteProjectKpiStates: StatusEnum
  updateProjectKpiStates: StatusEnum
  projectKpis: IProjectKpi[]
  projectKpi: IProjectKpi
}

export interface IProjectKpi {
  id?: any
  period?: string
  project_name?: string
  is_executive_kpi?: Boolean
  LookupProjectToPhase?: ILookupProjectToPhase
  master_typology_id?: number
  lookup_project_to_phase_id?: number
  typology?: string
  kpi?: string
  kpi_value?: number
  last_updated?: Date
}

export interface IGetProjectKpisResponse {
  data: IProjectKpi[]
  message: string
  success: true
}
