import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { ITableStates } from './interface'
import { StatusEnum } from '@/src/redux/types'

const initialState: ITableStates = {
  selectedRows: [],
}

export const tableSlice = createSlice({
  name: 'interest',
  initialState,
  reducers: {
    //set selected rows
    setSelectedRows(state, action) {
      if (state.selectedRows.includes(action.payload)) {
        const updatedSelectedRows = state.selectedRows.filter((row) => row !== action.payload)
        state.selectedRows = updatedSelectedRows
      } else {
        const updatedSelectedRows = [...state.selectedRows, action.payload]
        state.selectedRows = updatedSelectedRows
      }
    },

    emptySelectedRows(state) {
      state.selectedRows = []
    },

    allSelectedRows(state, action) {
      const updatedSelectedRows = []
      for (var i = 1; i <= action.payload; i++) {
        updatedSelectedRows.push(i)
      }
      state.selectedRows = updatedSelectedRows
    },
  },
})

export const { setSelectedRows, emptySelectedRows, allSelectedRows } = tableSlice.actions

// Export the reducer
export default tableSlice.reducer
