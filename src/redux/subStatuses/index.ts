import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetMasterSubStatusesResponse, ISubStatusesState } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: ISubStatusesState = {
  getMasterSubStatuesStatus: StatusEnum.Idle,
  addMasterSubStatuesStatus: StatusEnum.Idle,
  deleteMasterSubStatuesStatus: StatusEnum.Idle,
  updateMasterSubStatuesStatus: StatusEnum.Idle,
  subStatuses: [],
  subStatus: {
    id: 0,
    sub_status: '',
  },
}

export const getMasterSubStatues = createAsyncThunk('/get-master-sub-status', async (_, thunkAPI) => {
  try {
    const response = await ApiGetNoAuth(`/master-sub-status`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const addMasterSubStatues = createAsyncThunk(
  '/add-master-sub-status',
  async (data: { sub_status: string }, thunkAPI) => {
    try {
      const response = await ApiPostNoAuth(`/master-sub-status`, {
        sub_status: data.sub_status,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const updateMasterSubStatues = createAsyncThunk(
  '/update-master-sub-status',
  async (data: { id: number; sub_status: string }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/master-sub-status/${data.id}`, {
        sub_status: data.sub_status,
      })
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteMasterSubStatues = createAsyncThunk('/delete-master-sub-status', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/master-sub-status/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const contractorsSlice = createSlice({
  name: 'developer',
  initialState,
  reducers: {
    //set selected rows
  },
  extraReducers: (builder) => {
    //getMasterSubStatues
    builder.addCase(getMasterSubStatues.pending, (state) => {
      state.getMasterSubStatuesStatus = StatusEnum.Pending
    })
    builder.addCase(getMasterSubStatues.fulfilled, (state, action) => {
      state.getMasterSubStatuesStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetMasterSubStatusesResponse
      state.subStatuses = actionPayload.data
    })
    builder.addCase(getMasterSubStatues.rejected, (state) => {
      state.getMasterSubStatuesStatus = StatusEnum.Failed
    })
    //addMasterSubStatues
    builder.addCase(addMasterSubStatues.pending, (state) => {
      state.addMasterSubStatuesStatus = StatusEnum.Pending
    })
    builder.addCase(addMasterSubStatues.fulfilled, (state, action) => {
      state.addMasterSubStatuesStatus = StatusEnum.Success
    })
    builder.addCase(addMasterSubStatues.rejected, (state) => {
      state.addMasterSubStatuesStatus = StatusEnum.Failed
    })
    //updateMasterSubStatues
    builder.addCase(updateMasterSubStatues.pending, (state) => {
      state.updateMasterSubStatuesStatus = StatusEnum.Pending
    })
    builder.addCase(updateMasterSubStatues.fulfilled, (state, action) => {
      state.updateMasterSubStatuesStatus = StatusEnum.Success
    })
    builder.addCase(updateMasterSubStatues.rejected, (state) => {
      state.updateMasterSubStatuesStatus = StatusEnum.Failed
    })
    //deleteMasterSubStatues
    builder.addCase(deleteMasterSubStatues.pending, (state) => {
      state.deleteMasterSubStatuesStatus = StatusEnum.Pending
    })
    builder.addCase(deleteMasterSubStatues.fulfilled, (state, action) => {
      state.deleteMasterSubStatuesStatus = StatusEnum.Success
    })
    builder.addCase(deleteMasterSubStatues.rejected, (state) => {
      state.deleteMasterSubStatuesStatus = StatusEnum.Failed
    })
  },
})

export const {} = contractorsSlice.actions

// Export the reducer
export default contractorsSlice.reducer
