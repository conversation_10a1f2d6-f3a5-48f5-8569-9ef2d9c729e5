import { StatusEnum } from '../types'

export interface ISubStatusesState {
  getMasterSubStatuesStatus: StatusEnum
  addMasterSubStatuesStatus: StatusEnum
  deleteMasterSubStatuesStatus: StatusEnum
  updateMasterSubStatuesStatus: StatusEnum
  subStatuses: ISubStatuses[]
  subStatus?: ISubStatuses
}
export interface ISubStatuses {
  id: number
  sub_status: string
}

export interface IGetMasterSubStatusesResponse {
  data: ISubStatuses[]
  message: string
  success: true
}
