import { IStatus } from '@/src/redux/status/interface'

export interface DragAndDropResponse {
  updatedDropItem: IStatus
  dragItem: IStatus
  updatedDragItem: IStatus
  dropItem: IStatus
}

export const dragNDropStatus = async (
  statuses: IStatus[],
  data: any,
  dragId: string,
  dropId: string,
): Promise<DragAndDropResponse | null> => {
  const dragItem = statuses.find((item: IStatus) => item.id?.toString() === dragId.toString())
  const dropItem = statuses.find((item: IStatus) => item.id?.toString() === dropId.toString())

  // Check if both items are found and if their ids are valid
  if (!dragItem || !dropItem || dragItem.id === undefined || dropItem.id === undefined) {
    return null
  }

  const updatedDragItem = { ...dragItem }
  const updatedDropItem = { ...dropItem }

  // Remove unnecessary properties
  delete updatedDragItem.id
  delete updatedDragItem.last_updated
  delete updatedDragItem.updated_by
  delete updatedDragItem.key_highlights_updated_by
  delete updatedDropItem.id
  delete updatedDropItem.last_updated
  delete updatedDropItem.updated_by
  delete updatedDropItem.key_highlights_updated_by

  return { updatedDropItem, dragItem, updatedDragItem, dropItem }
}
