import { ExternalToast, toast } from 'sonner'
import { ERROR, G<PERSON><PERSON>, WHIT<PERSON> } from '../constant/color'

export const successToast = (message: string) => {
  toast.success(message, {
    style: { backgroundColor: GRE<PERSON>, color: WHITE }, // Green background
  })
}

export const errorToast = (message: string) => {
  toast.error(message, {
    style: { backgroundColor: ERROR, color: WHITE }, // Red background
  })
}

export const intoToast = (message: string, data?: ExternalToast) => {
  toast.info(message, data)
}
