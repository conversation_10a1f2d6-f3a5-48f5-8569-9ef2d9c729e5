/**
 * Stores an item in localStorage with the given key.
 *
 * @param key - The key under which the value will be stored.
 * @param value - The value to be stored. This will be stringified before storage.
 *
 * @example
 * setLocalStorageItem('user', { name: '<PERSON>', age: 30 });
 * // Stores the object { name: '<PERSON>', age: 30 } under the key 'user'
 */
export const setLocalStorageItem = (key: string, value: any): void => {
  localStorage.setItem(key, JSON.stringify(value))
}

/**
 * Retrieves an item from localStorage by the given key.
 *
 * @param key - The key of the item to be retrieved.
 * @returns The value associated with the key, or null if the key does not exist.
 *
 * @example
 * const user = getLocalStorageItem('user');
 * // Retrieves the value associated with the key 'user', which was previously stored
 */
export const getLocalStorageItem = (key: string): any | null => {
  const value = localStorage.getItem(key)
  return value ? JSON.parse(value) : null
}

/**
 * Removes an item from localStorage by the given key.
 *
 * @param key - The key of the item to be removed.
 *
 * @example
 * removeLocalStorageItem('user');
 * // Removes the item associated with the key 'user'
 */
export const removeLocalStorageItem = (key: string): void => {
  localStorage.removeItem(key)
}
