import { isValueBetween } from '@/src/component/updateProgress/progressForm/keyAchievements/helper'

export const reorderAreaOfConcerns = (areaOfConcerns: any, dragItem: any, dropItem: any) => {
  const min = Math.min(dragItem.key_risk_sorting_order, dropItem.key_risk_sorting_order)
  const max = Math.max(dragItem.key_risk_sorting_order, dropItem.key_risk_sorting_order)

  const filteredAndSortedData = areaOfConcerns
    .filter((item: any) => isValueBetween(Number(item.key_risk_sorting_order), min, max))
    .sort((a: any, b: any) => Number(a.key_risk_sorting_order) - Number(b.key_risk_sorting_order))

  const findFirstValue = filteredAndSortedData.find(
    (item: any) => item.key_risk_sorting_order === dragItem.key_risk_sorting_order,
  )
  const findFirstPlace = filteredAndSortedData.findIndex(
    (item: any) => item.key_risk_sorting_order === dropItem.key_risk_sorting_order,
  )

  let arrangedData = [...filteredAndSortedData]

  if (findFirstValue && findFirstPlace !== -1) {
    arrangedData = arrangedData.filter((item) => item?.key_risk_sorting_order !== dragItem?.key_risk_sorting_order)
    arrangedData.splice(findFirstPlace, 0, findFirstValue)
  }

  return arrangedData.map((item, index) => ({ ...item, key_risk_sorting_order: min + index }))
}

// Utility to sort areaOfConcerns by rating and then by key_risk_sorting_order
// Usage: sortAreaOfConcerns(array)

const RATING_ORDER = ['High', 'Medium', 'Low', 'Closed']

export function sortAreaOfConcernsWithRating(areaOfConcerns: any[]) {
  // Convert null key_risk_sorting_order to 0 before sorting
  const normalized = areaOfConcerns.map((item) => ({
    ...item,
    key_risk_sorting_order: item.key_risk_sorting_order == null ? 0 : item.key_risk_sorting_order,
    phase: item?.phase || '-',
  }))
  return [...normalized].sort((a, b) => {
    const ratingA = a.rating || 'Closed'
    const ratingB = b.rating || 'Closed'
    const ratingIndexA = RATING_ORDER.indexOf(ratingA)
    const ratingIndexB = RATING_ORDER.indexOf(ratingB)

    if (ratingIndexA !== ratingIndexB) {
      return ratingIndexA - ratingIndexB
    }
    return a.key_risk_sorting_order - b.key_risk_sorting_order
  })
}
