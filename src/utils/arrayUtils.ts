import { naturalSort } from './sortingUtils'
import { multiSelectOption } from '../constant/enum'
import { MULTI_SELECT_SEPARATOR } from '../constant/stageStatus'
import { convertValuesToCommaSeparated } from '../helpers/helpers'

export const populateDropdownOptions = <T>(arr: T[], property: keyof T): Array<T[keyof T]> => {
  return arr
    ?.map((obj) => obj[property])
    ?.sort((a, b) => {
      // If both are strings, use localeCompare
      if (typeof a === 'string' && typeof b === 'string') {
        return a.localeCompare(b)
      }
      // If both are numbers, sort numerically
      if (typeof a === 'number' && typeof b === 'number') {
        return a - b
      }
      // If one is string and one is number, numbers come first
      if (typeof a === 'number' && typeof b === 'string') {
        return -1
      }
      if (typeof a === 'string' && typeof b === 'number') {
        return 1
      }
      // Otherwise, keep original order
      return 0
    })
}

export const arrayToStringForTable = <T>(arr: T[], property: keyof T): string => {
  return arr
    ?.map((obj) => obj[property])
    ?.map((val: any) => val?.replace(/\d+/g, '')?.trim())
    .filter((val) => val)
    .join(', ')

  // .split(';') // Split by semicolons
  // .map((val: string) => val.replace(/\d+/g, '').trim())
  // .filter((val) => val) // Remove empty strings
  // .join(', ')
}

export const prepareDropdownOptions = <T>(arr: T[], property: string, isAvatar?: boolean) => {
  return arr
    ?.map((obj: any) => {
      return isAvatar
        ? { label: obj?.[property], value: obj?.id, avatar: isAvatar ? obj?.avatar.url : '' }
        : { label: obj?.[property], value: obj?.id }
    })
    ?.filter((item) => !!item?.label && !!item?.value?.toString())
}

export const prepareMultiPhaseCategoryDropdownOptions = <T>(
  arr: T[],
  masterProperty: string,
  property: string,
  isId?: boolean,
) => {
  return arr
    ?.map((obj: any) => {
      const masterObj = obj?.[masterProperty]
      return { label: masterObj?.[property], value: isId ? masterObj?.id : obj?.id }
      // return { label: masterObj?.[property], value: masterObj?.id }
    })
    ?.filter((item) => !!item.label && !!item.value)
}

export const prepareDropdownOptionsFromObject = <T>(
  arr: T[],
  fieldName: string,
  property: string,
  isAvatar?: boolean,
) => {
  return arr
    ?.map((obj: any) => {
      return obj[fieldName]
        ? isAvatar
          ? { label: obj[fieldName][property], value: obj[fieldName]?.id, avatar: isAvatar ? obj?.avatar?.url : '' }
          : { label: obj[fieldName][property], value: obj[fieldName]?.id }
        : null
    })
    ?.filter((item) => !!item?.label && !!item?.value?.toString())
}

export const getValue = (options: any, id: any) => {
  return options?.find((item: any) => item?.value === id)
}

export const getOptionsValue = (options: any, id: string) => {
  for (const group of options) {
    const found = group.options.find((item: any) => item.value === id)
    if (found) return found
  }
  return null
}

export const getEditedValue = (options: any, id: any) => {
  let editedValue = options?.find((item: any) => {
    return item?.id === id
  })

  return editedValue
}

export const getValueWithConvertValuesToCommaSeparated = (options: any, id: any) => {
  let option = options?.find((item: any) => item?.id === id)

  return { label: convertValuesToCommaSeparated(option?.label), value: option?.value }
}

export const getValueByLabel = (options: any, lable: string) => {
  return options?.find((item: any) => item?.label === lable)
}

export const getMultiValues = (
  options: { label: string; value: number | number[]; tooltip: string }[],
  ids: number[],
) => {
  for (const item of options) {
    if (Array.isArray(item.value)) {
      if (ids.length > 1 && item.value.length > 1 && item.value.every((val) => ids.includes(val))) {
        return [item]
      }
    } else {
      if (ids.length === 1 && ids.includes(item.value)) {
        return [item]
      }
    }
  }
  return []
}

export const getValues = (options: { label: string; value: string }[], ids: string) => {
  return options?.filter((item) => ids?.includes(item.value))?.map((item) => item.label)
}

export const getValueForMultiSelectOption = (options: { id: number; name: string }[], ids: number[]) => {
  return options?.filter((item) => ids?.includes(item?.id))?.map((item) => item?.id)
}

export const dateNonRequiredFields = (key: string, columnName?: string): boolean => {
  let dateNonRequiredFieldsList = [
    'SPA_date',
    'milestone_date',
    'revised_spa_communication',
    'eot',
    'contract_date',
    'actual_forecast_milestone_collection',
    'actual_forecast_milestone_completion',
    'baselinePlanFinish',
    'forecastFinish',
    'latest_communication_date',
  ]

  if (key === columnName) {
    return dateNonRequiredFieldsList.includes(key)
  }

  return false
}

export function sortArrayByKeyWithTypeConversion(array: any[], key: string, convertNumber?: boolean) {
  if (!Array.isArray(array) || array.length === 0 || !key) {
    return array // Return the original array if it's invalid
  }

  return Array.from(array).sort((a, b) => {
    const valueA = a[key]
    const valueB = b[key]

    // Handle null values: prioritize non-null over null
    if (valueA === null && valueB !== null) return 1
    if (valueA !== null && valueB === null) return -1

    // Convert field data to numbers if convertNumber is true
    let compA = valueA
    let compB = valueB

    if (convertNumber) {
      if (typeof valueA === 'string') compA = parseFloat(valueA)
      if (typeof valueB === 'string') compB = parseFloat(valueB)
    }

    // Sort based on numeric values if both are numbers
    if (typeof compA === 'number' && typeof compB === 'number') {
      return compA - compB // Numeric comparison
    }

    // Sort based on string values if both are strings
    if (typeof compA === 'string' && typeof compB === 'string') {
      return naturalSort(compA, compB) // String comparison
    }

    // Handle case where one value is a number and the other is a string
    if (typeof compA === 'number' && typeof compB === 'string') {
      return -1 // Numbers come before strings
    }
    if (typeof compA === 'string' && typeof compB === 'number') {
      return 1 // Strings come after numbers
    }

    // Handle other data types
    return 0 // If both are of the same type and non-comparable, keep original order
  })
}

export function convertMultiSelectOption(
  array: any[],
  subStageName?: any,
  subStageOption?: any[],
  isAvatar?: boolean,
): any[] {
  const subOption = subStageOption?.filter((item) => item?.length && item !== null)
  const data = array?.map((item) => {
    const val = typeof item === 'string' ? item?.trim() || '' : item?.value || ''
    const label = typeof item === 'string' ? item?.trim() || '' : item?.label || ''

    let option: any = {
      id: val,
      name: convertValuesToCommaSeparated(label ?? ''),
      subOptions:
        subStageName && val === subStageName
          ? subOption?.map((subItem) => {
              if (!subItem.length) return
              return {
                id: subItem?.trim(),
                name: subItem?.trim(),
              }
            })
          : [],
    }
    option = isAvatar ? { ...option, avatar: isAvatar ? val?.avatar : null } : option
    return option
  })
  return data
}
export function getUniqueValuesById(array: any[]) {
  const seen = new Set()
  const uniqueValues: any = []

  array.forEach((item) => {
    if (item && !seen.has(item.id)) {
      seen.add(item.id)
      uniqueValues.push(item)
    }
  })

  return uniqueValues
}

export function getUniqueValues(array: any) {
  const uniqueValues: any = []
  array.forEach((value: any) => {
    if (value !== null && !uniqueValues.includes(value)) {
      uniqueValues.push(value)
    }
  })
  return uniqueValues
}

export function getUniqueValuesFromArray(arr: string[]): string[] {
  const seen: Set<string> = new Set()
  const result: string[] = []

  arr.forEach((value) => {
    if (!seen.has(value)) {
      seen.add(value)
      result.push(value)
    }
  })

  return result.filter((item) => item !== null && item !== undefined)
}

/**
 * Filters and sorts an array of objects, optionally including a "Select All" option.
 *
 * This function performs the following steps:
 * 1. Defines the "Select All" option.
 * 2. Filters out objects with empty `id` or `name` properties.
 * 3. Checks if the "Select All" option is included in the data.
 * 4. Filters out the "Select All" option from the data if it is present.
 * 5. Sorts the remaining data by the `name` property in alphabetical order.
 * 6. Returns an array with the "Select All" option prepended if it was included, otherwise returns only the sorted data.
 *
 * @param data - An array of objects with `id` and `name` properties.
 * @returns A sorted array of objects, optionally including the "Select All" option at the beginning.
 */
export const convertAndSortData = (data: { id: string; name: string }[]) => {
  // Define the "Select All" option
  const selectAllOption = { id: multiSelectOption.SELECT_ALL, name: multiSelectOption.SELECT_ALL }
  // Filter out objects with empty id or name
  const filteredData = data?.filter((item) => item.id && item.name)

  // Separate the "Select All" option if available
  const selectAllIncluded = filteredData?.some((item) => item.id === selectAllOption.id)
  const filteredWithoutSelectAll = filteredData?.filter((item) => item.id !== selectAllOption.id)

  // Sort the filtered data by name in alphabetical order
  const sortedData = filteredWithoutSelectAll?.sort((a, b) => a?.name?.localeCompare(b.name))

  // Combine "Select All" option with the sorted data
  return selectAllIncluded ? [selectAllOption, ...sortedData] : sortedData
}

export const getDifferedState = async (setState: any) => {
  let differedState
  await new Promise((resolve: any) =>
    setState((data: any) => {
      differedState = data
      resolve()
      return data
    }),
  )
  return differedState
}

export const flattenMixedArray = (input: any) => {
  const result = []

  for (const item of input) {
    if (typeof item === 'string') {
      result.push(item)
    } else if (Array.isArray(item)) {
      for (const inner of item) {
        if (typeof inner === 'string') {
          result.push(inner)
        }
      }
    }
  }

  return result
}

export const getMultiSelectedValue = (value: string) => {
  if (value?.includes(MULTI_SELECT_SEPARATOR)) {
    return value.split(MULTI_SELECT_SEPARATOR)
  }
  return typeof value === 'string' ? [value] : value
}

export const setMultiSelectedValueToWildCard = (value: string[] | number[]): string => {
  return typeof value === 'string' || typeof value === 'number' ? value : value?.join(MULTI_SELECT_SEPARATOR)
}

/**
 * Extracts unique values from an array of objects based on a specified key.
 *
 * @param array - Array of objects to extract unique values from
 * @param key - The key/property name to extract unique values for
 * @returns Array of unique values for the specified key
 */
export function getUniqueArrayByKey(array: any[], key: string) {
  const uniqueValues: any[] = []
  const seenKeys = new Set()
  const cloneArray = [...array]
  cloneArray.forEach((item) => {
    if (item[key] !== null && !seenKeys.has(item[key])) {
      seenKeys.add(item[key])
      uniqueValues.push(item)
    }
  })

  return uniqueValues
}
