/**
 * SORTING EXPLANATION - How the sorting works step by step:
 *
 * • STEP 1: Sort by Stage Status (Primary Sort)
 *   - Uses predefined order: Initiation → LDC Procurement → Design → Contractor Procurement → Construction → DLP and Project Closeout → Transfer In Progress
 *   - Items with no stage status appear first
 *   - Any stage status not in the predefined list appears last
 *
 * • STEP 2: Sort by Project Phase Category (Secondary Sort)
 *   - Alphabetical order with smart number handling
 *   - Example: "Park" comes before "School", "Phase 1" comes before "Phase 10"
 *   - Items with no project phase category appear first
 *
 * • STEP 3: Sort by Phase (Tertiary Sort)
 *   - Alphabetical order with smart number handling
 *   - Example: "Phase 1" → "Phase 2" → "Phase 10" → "Overall"
 *   - Items with no phase appear first
 *
 * • STEP 4: Sort by Sub Stage (Final Sort)
 *   - Alphabetical order with smart number handling
 *   - Example: "Authority Approvals" comes before "Preliminary Design"
 *   - Items with no sub stage appear first
 *
 * RESULT: All items are organized in a logical hierarchy that makes sense for project management workflows
 */

import { predefinedProjectStatusOrder } from '../component/customCells/headerCell/generateOptions'
import { IStatus } from '../redux/status/interface'

//* Helper function for natural alphanumeric sorting
const naturalSort = (a: string | null | undefined, b: string | null | undefined): number => {
  // Handle null, undefined, and empty string cases
  if ((!a || a.trim() === '') && (!b || b.trim() === '')) return 0
  if (!a || a.trim() === '') return -1 // null/undefined/empty goes first
  if (!b || b.trim() === '') return 1

  return a.localeCompare(b, undefined, {
    numeric: true,
    sensitivity: 'base',
  })
}

//* Helper function to get stage order index
const getStageOrderIndex = (stageStatus: string | null | undefined, predefinedStageOrder: string[]): number => {
  if (!stageStatus || stageStatus?.trim() === '') return -1 // Put null/undefined/empty at beginning
  const index = predefinedStageOrder.indexOf(stageStatus)
  return index === -1 ? predefinedStageOrder.length : index
}

//* Helper function to check if a value is empty (null, undefined, or empty string)
const isEmpty = (value: string | null | undefined): boolean => {
  return !value || value.trim() === ''
}

const sortStatusesPredecessorSuccessor = (statuses: IStatus[]): IStatus[] => {
  return [...statuses].sort((a, b) => {
    // 1. First sort by stage_status using predefined order
    const stageOrderA = getStageOrderIndex(a.MasterProjectStageStatus?.project_stage_status, predefinedStageOrder)
    const stageOrderB = getStageOrderIndex(b.MasterProjectStageStatus?.project_stage_status, predefinedStageOrder)

    if (stageOrderA !== stageOrderB) {
      return stageOrderA - stageOrderB
    }

    // 2. Then sort by project_phase_category (alphanumeric)
    const aCategories = a?.LookupProjectToProjectPhaseCategory?.map(
      (item: any) => item?.MasterProjectPhaseCategory?.project_phase_category,
    )?.join(', ')
    const bCategories = b?.LookupProjectToProjectPhaseCategory?.map(
      (item: any) => item?.MasterProjectPhaseCategory?.project_phase_category,
    )?.join(', ')

    const categoryCompare = naturalSort(aCategories, bCategories)
    if (categoryCompare !== 0) {
      return categoryCompare
    }

    // 3. Then sort by phase (alphanumeric)
    const aPhase = a?.LookupProjectToPhase?.map((item: any) => item?.phase)?.join(', ')
    const bPhase = b?.LookupProjectToPhase?.map((item: any) => item?.phase)?.join(', ')
    const phaseCompare = naturalSort(aPhase, bPhase)
    if (phaseCompare !== 0) {
      return phaseCompare
    }

    // 4. Finally sort by sub_stage (alphanumeric)
    return naturalSort(a.sub_stage, b.sub_stage)
  })
}

const predecessorSuccessorOptionMapping = (sortedStatuses: IStatus[]) => {
  const optionsMap = new Map()
  sortedStatuses.forEach((item) => {
    const projectPhaseCategories = item.LookupProjectToProjectPhaseCategory?.map(
      (item) => item.MasterProjectPhaseCategory?.project_phase_category,
    )?.join(', ')
    const phase = item?.LookupProjectToPhase ? item?.LookupProjectToPhase.map((item) => item.phase)?.join(', ') : ''
    const stageStatus = item.MasterProjectStageStatus?.project_stage_status
    const subStage = item.MasterProjectSubStage?.project_sub_stage

    const id = item.id
    const name = `${projectPhaseCategories}${phase ? ` / ${phase}` : ''}${stageStatus ? ` / ${stageStatus}` : ''}${subStage ? ` / ${subStage}` : ''}`

    if (id) {
      optionsMap.set(id, { id, name })
    }
  })
  return optionsMap
}

//* Predefined stage order
export const predefinedStageOrder: string[] = [
  'Initiation',
  'LDC Procurement',
  'Design',
  'Contractor Procurement',
  'Construction',
  'DLP and Project Closeout',
  'Transfer In Progress',
]

export const sortPredecessorSuccessorFilterOption = (data: { label: string; subItem: any[]; value: string }[]) => {
  const stageRank = Object.fromEntries(predefinedProjectStatusOrder.map((stage, idx) => [stage.toLowerCase(), idx]))

  const getStageParts = (str: any) => {
    const parts = str.split('/').map((s: any) => s.trim())
    // Search from right to left for the stage
    for (let i = parts.length - 1; i >= 0; i--) {
      const partLower = parts[i].toLowerCase()
      if (stageRank.hasOwnProperty(partLower)) {
        const stage = parts[i]
        const subStage = parts[i + 1] || ''
        return { stage, subStage }
      }
    }
    return { stage: null, subStage: '' }
  }

  // Group data by stage
  const stageBuckets: any = {}
  for (let item of data) {
    const { stage, subStage } = getStageParts(item?.label)
    if (stage) {
      if (!stageBuckets[stage]) {
        stageBuckets[stage] = []
      }
      stageBuckets[stage].push({ item, subStage })
    }
  }

  const result = []

  for (let stage of predefinedProjectStatusOrder) {
    const group = stageBuckets[stage] || []
    group.sort((a: any, b: any) => naturalSort(a.item.label, b.item.label))
    result.push(...group.map((e: any) => e.item))
  }

  return result
}

// Export everything for use in other modules
export { naturalSort, getStageOrderIndex, isEmpty, sortStatusesPredecessorSuccessor, predecessorSuccessorOptionMapping }
