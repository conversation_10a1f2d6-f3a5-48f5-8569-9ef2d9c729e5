import { NextRouter } from 'next/router' // Assuming Next.js router
import { setMultiSelectedValueToWildCard } from '../arrayUtils'
import { Routes } from '@/src/constant/enum'
import { IStatus } from '@/src/redux/status/interface'

export const redirectToStatus = (
  phaseId: number[],
  stageStatusId: number,
  subStageId: number | null,
  statuses: IStatus[],
  router: NextRouter,
): void => {
  return
  let redirectStatus: IStatus | undefined
  // If subStageId is provided, filter by subStageId as well
  const currentPhaseIDs = phaseId?.length > 0 ? setMultiSelectedValueToWildCard(phaseId) : ''
  const modifyRecord = statuses.map((item) => ({
    ...item,
    phaseIDs:
      item?.LookupProjectToPhase && item?.LookupProjectToPhase?.length > 0
        ? setMultiSelectedValueToWildCard(item.LookupProjectToPhase.map((phase) => phase.id))
        : '',
  }))

  if (subStageId) {
    redirectStatus = modifyRecord.find(
      (item) =>
        (currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
          ? item?.phaseIDs === '' || item?.phaseIDs === null
          : item.phaseIDs === currentPhaseIDs) &&
        item?.MasterProjectStageStatus?.id === stageStatusId &&
        item?.MasterProjectSubStage?.id === subStageId,
    )
  } else {
    redirectStatus = modifyRecord.find(
      (item) =>
        (currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
          ? item?.phaseIDs === '' || item?.phaseIDs === null
          : item.phaseIDs === currentPhaseIDs) && item?.MasterProjectStageStatus?.id === stageStatusId,
    )
  }

  if (!redirectStatus) return

  const searchParam = router?.query?.search ? `?search=${router.query.search}` : ''
  router.push(`${Routes.EDIT_STATUS}/${redirectStatus?.id}${searchParam}`)
}

export const redirectStatusWhenPhaseChange = (phaseId: number[], statuses: IStatus[], router: NextRouter): void => {
  console.group('start-->')
  // If subStageId is provided, filter by subStageId as well
  const currentPhaseIDs = phaseId?.length > 0 ? setMultiSelectedValueToWildCard(phaseId) : ''
  const modifyRecord = statuses.map((item) => ({
    ...item,
    phaseIDs:
      item?.LookupProjectToPhase && item?.LookupProjectToPhase?.length > 0
        ? setMultiSelectedValueToWildCard(item.LookupProjectToPhase.map((phase) => phase.id))
        : '',
  }))

  const redirectStatus = modifyRecord.find((item) =>
    currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
      ? item?.phaseIDs === '' || item?.phaseIDs === null
      : item.phaseIDs === currentPhaseIDs,
  )
  if (!redirectStatus) return

  const searchParam = router?.query?.search ? `?search=${router.query.search}` : ''
  router.push(`${Routes.EDIT_STATUS}/${redirectStatus.id}${searchParam}`)
}

export const redirectStatusWhenStageChange = (
  phaseId: number[],
  stageStatusId: number,
  statuses: IStatus[],
  router: NextRouter,
) => {
  const currentPhaseIDs = phaseId?.length > 0 ? setMultiSelectedValueToWildCard(phaseId) : ''
  const modifyRecord = statuses.map((item) => ({
    ...item,
    phaseIDs:
      item?.LookupProjectToPhase && item?.LookupProjectToPhase?.length > 0
        ? setMultiSelectedValueToWildCard(item.LookupProjectToPhase.map((phase) => phase.id))
        : '',
  }))

  const redirectStatus = modifyRecord.find((item) =>
    currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
      ? (item?.phaseIDs === '' || item?.phaseIDs === null) && item?.MasterProjectStageStatus?.id === stageStatusId
      : item.phaseIDs === currentPhaseIDs && item?.MasterProjectStageStatus?.id === stageStatusId,
  )

  if (!redirectStatus) return

  const searchParam = router?.query?.search ? `?search=${router.query.search}` : ''
  router.push(`${Routes.EDIT_STATUS}/${redirectStatus.id}${searchParam}`)
}

export const redirectStatusWhenSubStageChange = (
  phaseId: number[],
  stageStatusId: number,
  subStageId: number,
  statuses: IStatus[],
  router: NextRouter,
) => {
  const currentPhaseIDs = phaseId?.length > 0 ? setMultiSelectedValueToWildCard(phaseId) : ''
  const modifyRecord = statuses.map((item) => ({
    ...item,
    phaseIDs:
      item?.LookupProjectToPhase && item?.LookupProjectToPhase?.length > 0
        ? setMultiSelectedValueToWildCard(item.LookupProjectToPhase.map((phase) => phase.id))
        : '',
  }))

  const redirectStatus = modifyRecord.find(
    (item) =>
      (currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
        ? (item?.phaseIDs === '' || item?.phaseIDs === null) &&
          item?.MasterProjectStageStatus?.id === stageStatusId &&
          item?.MasterProjectSubStage?.id === subStageId
        : item.phaseIDs === currentPhaseIDs) &&
      item?.MasterProjectStageStatus?.id === stageStatusId &&
      item?.MasterProjectSubStage?.id === subStageId,
  )

  if (!redirectStatus) return

  const searchParam = router?.query?.search ? `?search=${router.query.search}` : ''
  router.push(`${Routes.EDIT_STATUS}/${redirectStatus.id}${searchParam}`)
}
