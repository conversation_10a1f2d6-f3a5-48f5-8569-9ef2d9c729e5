import React, { ReactElement, forwardRef } from 'react'

interface AvatarIconProps {
  className?: string
  onClick?: (e: React.MouseEvent<SVGSVGElement, MouseEvent>) => void
  height?: string
  width?: string
  color?: any
}

const AvatarIcon: React.FC<AvatarIconProps> = forwardRef<SVGSVGElement, AvatarIconProps>((props, ref) => {
  const { height, width } = props

  return (
    <svg
      width={width || '40'}
      height={height || '40'}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      ref={ref}
      {...props}
    >
      <g clipPath="url(#clip0_1999_14314)">
        <path
          d="M26.4412 29.4023H13.5099C7.71401 29.4023 3 34.1035 3 39.8994V44.9998H36.9383V39.8994C36.9511 34.1035 32.25 29.4023 26.4412 29.4023ZM24.1357 43.1966H22.5902H17.3739H15.8283L13.4198 30.5486C13.6516 30.5358 13.8963 30.51 14.1281 30.51H25.8488C26.0935 30.51 26.3253 30.5229 26.5571 30.5486L24.1357 43.1966Z"
          fill={props.color ? props.color : '#808080'}
          fillOpacity="0.2"
        />
        <path
          d="M21.2887 34.6957L22.5895 43.1964H24.1351L26.5436 30.5484C26.3118 30.5355 26.08 30.5098 25.8352 30.5098H14.1146C13.8699 30.5098 13.6381 30.5226 13.4062 30.5484L15.8148 43.1964H17.3603L18.6483 34.6957H18.6998L17.4247 32.3645H22.5123L21.2372 34.6957H21.2887Z"
          fill={props.color ? props.color : '#808080'}
        />
        <path
          d="M17.375 43.1963H22.5913L21.2905 34.6957H21.2518L22.5269 32.3516H17.4394L18.7145 34.6957H18.663L17.375 43.1963Z"
          fill={props.color ? props.color : '#808080'}
        />
        <path
          d="M31.1558 17.1797C31.1558 11.0102 26.1585 6 19.989 6C13.8196 6 8.82227 10.9974 8.82227 17.1797C8.82227 23.3491 13.8196 28.3464 19.989 28.3464C26.1456 28.3464 31.1558 23.3491 31.1558 17.1797Z"
          fill={props.color ? props.color : '#808080'}
          fillOpacity="0.2"
        />
      </g>
      <defs>
        <clipPath id="clip0_1999_14314">
          <rect width="40" height="40" rx="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
})

AvatarIcon.displayName = 'AvatarIcon'

export default AvatarIcon
