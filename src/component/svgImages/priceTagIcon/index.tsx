import React, { ReactElement, forwardRef } from 'react'

interface PriceTagIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: any
}

const PriceTagIcon: React.FC<PriceTagIconProps> = forwardRef<SVGSVGElement, PriceTagIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="16"
        height="17"
        viewBox="0 0 16 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        ref={ref}
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.49285 2.36584C7.08349 2.41994 6.578 2.53605 5.85892 2.70199L5.04013 2.89094C4.43278 3.0311 4.01453 3.12826 3.69506 3.23791C3.38665 3.34376 3.21217 3.44811 3.0805 3.57977C2.94884 3.71143 2.84449 3.88591 2.73864 4.19433C2.62899 4.5138 2.53183 4.93205 2.39167 5.5394L2.20272 6.35819C2.03678 7.07727 1.92067 7.58276 1.86657 7.99212C1.81387 8.39093 1.82602 8.66076 1.89637 8.90606C1.96672 9.15136 2.09943 9.38662 2.35549 9.69688C2.61833 10.0153 2.9847 10.3825 3.50653 10.9043L4.72629 12.124C5.63261 13.0304 6.27754 13.6738 6.83199 14.0968C7.37491 14.5111 7.77191 14.6666 8.17553 14.6666C8.57916 14.6666 8.97616 14.5111 9.51908 14.0968C10.0735 13.6738 10.7185 13.0304 11.6248 12.124C12.5311 11.2177 13.1745 10.5728 13.5976 10.0183C14.0118 9.47543 14.1673 9.07843 14.1673 8.6748C14.1673 8.27117 14.0118 7.87417 13.5976 7.33126C13.1745 6.7768 12.5311 6.13188 11.6248 5.22555L10.405 4.0058C9.88319 3.48397 9.51608 3.11759 9.19761 2.85476C8.88736 2.5987 8.6521 2.46599 8.4068 2.39564C8.16149 2.32529 7.89166 2.31313 7.49285 2.36584ZM7.36184 1.37446C7.84005 1.31126 8.25937 1.31304 8.68248 1.43439C9.10558 1.55573 9.46212 1.77646 9.83414 2.08351C10.1938 2.38032 10.5943 2.78084 11.0946 3.28121L12.3585 4.54508C13.2323 5.41882 13.924 6.11057 14.3926 6.72468C14.8747 7.35654 15.1673 7.96461 15.1673 8.6748C15.1673 9.38499 14.8747 9.99306 14.3926 10.6249C13.924 11.239 13.2323 11.9308 12.3585 12.8045L12.3053 12.8578C11.4315 13.7315 10.7398 14.4233 10.1257 14.8919C9.49379 15.374 8.88572 15.6666 8.17553 15.6666C7.46535 15.6666 6.85728 15.3739 6.22541 14.8919C5.6113 14.4233 4.91954 13.7315 4.04579 12.8578L2.78195 11.5939C2.28158 11.0936 1.88106 10.693 1.58424 10.3334C1.2772 9.96138 1.05647 9.60485 0.935121 9.18175C0.813776 8.75864 0.811993 8.33931 0.875191 7.86111C0.936284 7.39882 1.06366 6.8469 1.22278 6.1574L1.42322 5.28881C1.556 4.71341 1.66427 4.24418 1.79279 3.86971C1.92702 3.47863 2.09607 3.14999 2.3734 2.87266C2.65072 2.59534 2.97936 2.42628 3.37044 2.29206C3.74491 2.16354 4.21414 2.05526 4.78955 1.92249L5.65813 1.72205C6.34763 1.56292 6.89955 1.43555 7.36184 1.37446ZM6.32793 5.83006C6.00249 5.50463 5.47485 5.50463 5.14942 5.83006C4.82398 6.1555 4.82398 6.68314 5.14942 7.00858C5.47485 7.33401 6.00249 7.33401 6.32793 7.00858C6.65337 6.68314 6.65337 6.1555 6.32793 5.83006ZM4.44231 5.12296C5.15827 4.407 6.31907 4.407 7.03504 5.12296C7.751 5.83892 7.751 6.99972 7.03504 7.71568C6.31907 8.43164 5.15827 8.43164 4.44231 7.71568C3.72635 6.99972 3.72635 5.83892 4.44231 5.12296ZM8.66258 8.57122C8.54465 8.57011 8.38964 8.62304 8.25671 8.75597C7.99835 9.01433 8.0663 9.27267 8.13885 9.34523C8.21141 9.41778 8.46975 9.48573 8.72811 9.22738C9.2508 8.70469 10.1533 8.53121 10.7316 9.10953C11.1802 9.55817 11.1764 10.2019 10.9081 10.712C11.0422 10.9068 11.0226 11.1755 10.8494 11.3487C10.6768 11.5213 10.4094 11.5413 10.2148 11.4088C9.91019 11.5706 9.56368 11.6313 9.23332 11.5634C8.96283 11.5078 8.78861 11.2435 8.8442 10.973C8.89979 10.7025 9.16413 10.5283 9.43461 10.5839C9.55272 10.6081 9.74312 10.5694 9.90662 10.4059C10.165 10.1475 10.097 9.88919 10.0245 9.81663C9.95192 9.74408 9.69357 9.67613 9.43522 9.93448C8.91253 10.4572 8.01007 10.6307 7.43175 10.0523C6.9831 9.60369 6.98691 8.95997 7.25521 8.44981C7.12113 8.25504 7.14069 7.98637 7.3139 7.81316C7.48647 7.64058 7.75382 7.62054 7.94842 7.75302C8.17499 7.6326 8.42394 7.56893 8.67201 7.57127C8.94814 7.57387 9.16988 7.79983 9.16728 8.07596C9.16467 8.35209 8.93871 8.57383 8.66258 8.57122Z"
          fill={props.color ? props.color : '#808080'}
        />
      </svg>
    )
  },
)
PriceTagIcon.displayName = 'AccedingIcon'

export default PriceTagIcon
