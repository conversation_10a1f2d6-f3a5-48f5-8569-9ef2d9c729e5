import React, { ReactElement, forwardRef } from 'react'

interface PasswordIconProps {
  className?: string
  onClick?: (e: any) => void
}

const PasswordIcon: React.FC<PasswordIconProps> = forwardRef<SVGSVGElement, PasswordIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg" {...props} ref={ref}>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.37496 6.75263V5.66699C3.37496 2.56039 5.89336 0.0419922 8.99996 0.0419922C12.1066 0.0419922 14.625 2.56039 14.625 5.66699V6.75263C14.814 6.76592 14.9921 6.78323 15.1598 6.80576C15.9098 6.90661 16.5414 7.12235 17.043 7.62395C17.5446 8.12555 17.7603 8.75711 17.8612 9.5072C17.9583 10.2297 17.9583 11.1483 17.9583 12.2879V12.3794C17.9583 13.519 17.9583 14.4376 17.8612 15.1601C17.7603 15.9102 17.5446 16.5418 17.043 17.0434C16.5414 17.545 15.9098 17.7607 15.1598 17.8616C14.4373 17.9587 13.5187 17.9587 12.379 17.9587H5.6209C4.48124 17.9587 3.56264 17.9587 2.84017 17.8616C2.09008 17.7607 1.45852 17.545 0.956918 17.0434C0.455321 16.5418 0.239577 15.9102 0.13873 15.1601C0.041596 14.4376 0.0416096 13.5191 0.0416264 12.3794V12.2879C0.0416096 11.1483 0.041596 10.2297 0.13873 9.5072C0.239577 8.75711 0.455321 8.12555 0.956918 7.62395C1.45852 7.12235 2.09008 6.90661 2.84017 6.80576C3.00777 6.78323 3.18593 6.76592 3.37496 6.75263ZM4.62496 5.66699C4.62496 3.25075 6.58372 1.29199 8.99996 1.29199C11.4162 1.29199 13.375 3.25075 13.375 5.66699V6.71153C13.0637 6.70865 12.732 6.70865 12.379 6.70866H5.6209C5.26797 6.70865 4.93623 6.70865 4.62496 6.71153V5.66699ZM1.8408 8.50783C2.07144 8.2772 2.39525 8.12683 3.00673 8.04462C3.63619 7.95999 4.47045 7.95866 5.66663 7.95866H12.3333C13.5295 7.95866 14.3637 7.95999 14.9932 8.04462C15.6047 8.12683 15.9285 8.2772 16.1591 8.50783C16.3898 8.73847 16.5401 9.06228 16.6223 9.67376C16.707 10.3032 16.7083 11.1375 16.7083 12.3337C16.7083 13.5298 16.707 14.3641 16.6223 14.9936C16.5401 15.605 16.3898 15.9288 16.1591 16.1595C15.9285 16.3901 15.6047 16.5405 14.9932 16.6227C14.3637 16.7073 13.5295 16.7087 12.3333 16.7087H5.66663C4.47045 16.7087 3.63619 16.7073 3.00673 16.6227C2.39525 16.5405 2.07144 16.3901 1.8408 16.1595C1.61017 15.9288 1.45979 15.605 1.37758 14.9936C1.29295 14.3641 1.29163 13.5298 1.29163 12.3337C1.29163 11.1375 1.29295 10.3032 1.37758 9.67376C1.45979 9.06228 1.61017 8.73847 1.8408 8.50783Z"
          fill="#444444"
        />
      </svg>
    )
  },
)
PasswordIcon.displayName = 'PasswordIcon'

export default PasswordIcon
