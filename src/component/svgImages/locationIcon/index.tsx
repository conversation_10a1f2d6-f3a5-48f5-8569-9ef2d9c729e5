import React, { ReactElement, forwardRef } from 'react'

interface LocationIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: any
}

const LocationIcon: React.FC<LocationIconProps> = forwardRef<SVGSVGElement, LocationIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="12"
        height="15"
        viewBox="0 0 12 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        ref={ref}
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.166016 6.26211C0.166016 2.9961 2.76936 0.333252 5.99935 0.333252C9.22934 0.333252 11.8327 2.9961 11.8327 6.26211C11.8327 7.83885 11.3833 9.53186 10.589 10.9945C9.7956 12.4553 8.63643 13.7247 7.18636 14.4025C6.43306 14.7546 5.56564 14.7546 4.81234 14.4025C3.36226 13.7247 2.20309 12.4553 1.40972 10.9945C0.615368 9.53186 0.166016 7.83885 0.166016 6.26211ZM5.99935 1.33325C3.3383 1.33325 1.16602 3.53157 1.16602 6.26211C1.16602 7.66016 1.56777 9.19019 2.28848 10.5172C3.01018 11.8461 4.03003 12.933 5.23578 13.4966C5.72072 13.7233 6.27798 13.7233 6.76292 13.4966C7.96867 12.933 8.98852 11.8461 9.71021 10.5172C10.4309 9.19019 10.8327 7.66016 10.8327 6.26211C10.8327 3.53157 8.66039 1.33325 5.99935 1.33325ZM5.99935 4.66659C5.17092 4.66659 4.49935 5.33816 4.49935 6.16659C4.49935 6.99501 5.17092 7.66659 5.99935 7.66659C6.82778 7.66659 7.49935 6.99501 7.49935 6.16659C7.49935 5.33816 6.82778 4.66659 5.99935 4.66659ZM3.49935 6.16659C3.49935 4.78587 4.61864 3.66659 5.99935 3.66659C7.38006 3.66659 8.49935 4.78587 8.49935 6.16659C8.49935 7.5473 7.38006 8.66659 5.99935 8.66659C4.61864 8.66659 3.49935 7.5473 3.49935 6.16659Z"
          fill={props.color ? props.color : '#808080'}
        />
      </svg>
    )
  },
)
LocationIcon.displayName = 'AccedingIcon'

export default LocationIcon
