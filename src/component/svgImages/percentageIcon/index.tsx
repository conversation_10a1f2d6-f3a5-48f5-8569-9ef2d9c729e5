import React, { ReactElement, forwardRef } from 'react'

interface PercentageIconProps {
  className?: string
  onClick?: (e: any) => void
}

const PercentageIcon: React.FC<PercentageIconProps> = forwardRef<SVGSVGElement, PercentageIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
        <path
          d="M2.672 5.04C2.672 4.31467 2.89067 3.73867 3.328 3.312C3.76533 2.87467 4.32533 2.656 5.008 2.656C5.69067 2.656 6.25067 2.87467 6.688 3.312C7.12533 3.73867 7.344 4.31467 7.344 5.04C7.344 5.776 7.12533 6.36267 6.688 6.8C6.25067 7.22667 5.69067 7.44 5.008 7.44C4.32533 7.44 3.76533 7.22667 3.328 6.8C2.89067 6.36267 2.672 5.776 2.672 5.04ZM12.016 2.832L5.536 14H4.096L10.576 2.832H12.016ZM5.008 3.552C4.64533 3.552 4.35733 3.68 4.144 3.936C3.94133 4.18133 3.84 4.54933 3.84 5.04C3.84 5.53067 3.94133 5.904 4.144 6.16C4.35733 6.416 4.64533 6.544 5.008 6.544C5.37067 6.544 5.65867 6.416 5.872 6.16C6.08533 5.89333 6.192 5.52 6.192 5.04C6.192 4.54933 6.08533 4.18133 5.872 3.936C5.65867 3.68 5.37067 3.552 5.008 3.552ZM8.8 11.792C8.8 11.056 9.01867 10.4747 9.456 10.048C9.89333 9.61067 10.4533 9.392 11.136 9.392C11.8187 9.392 12.3733 9.61067 12.8 10.048C13.2373 10.4747 13.456 11.056 13.456 11.792C13.456 12.5173 13.2373 13.0987 12.8 13.536C12.3733 13.9733 11.8187 14.192 11.136 14.192C10.4533 14.192 9.89333 13.9787 9.456 13.552C9.01867 13.1147 8.8 12.528 8.8 11.792ZM11.12 10.304C10.7573 10.304 10.4693 10.432 10.256 10.688C10.0427 10.9333 9.936 11.3013 9.936 11.792C9.936 12.272 10.0427 12.64 10.256 12.896C10.4693 13.1413 10.7573 13.264 11.12 13.264C11.4827 13.264 11.7707 13.1413 11.984 12.896C12.1973 12.64 12.304 12.272 12.304 11.792C12.304 11.3013 12.1973 10.9333 11.984 10.688C11.7707 10.432 11.4827 10.304 11.12 10.304Z"
          fill="black"
        />
      </svg>
    )
  },
)
PercentageIcon.displayName = 'PercentageIcon'

export default PercentageIcon
