import React, { ReactElement, forwardRef } from 'react'

interface ProjectIconProps {
  className?: string
  onClick?: (e: any) => void
}

const MinusSquareIcon: React.FC<ProjectIconProps> = forwardRef<SVGSVGElement, ProjectIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.484 9.14V10.064H7.696V9.14H12.484Z" fill="#23C470" />

        <rect x="0.5" y="0.5" width="19" height="19" rx="1.5" stroke="#23C470" />
      </svg>
    )
  },
)
MinusSquareIcon.displayName = 'MinusSquareIcon'

export default MinusSquareIcon
