import React, { ReactElement, forwardRef } from 'react'

interface AddWidgetIcon {
  className?: string
  onClick?: (e: any) => void
  fill?: string
}

const AddWidgetIcon: React.FC<AddWidgetIcon> = forwardRef<SVGSVGElement, AddWidgetIcon>((props, ref): ReactElement => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props} ref={ref}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.29802 1.16602C3.69904 1.166 3.19955 1.16598 2.80303 1.21929C2.3845 1.27556 2.00664 1.39934 1.70299 1.70299C1.39934 2.00664 1.27556 2.3845 1.21929 2.80303C1.16598 3.19955 1.166 3.69902 1.16602 4.298V4.36735C1.166 4.96633 1.16598 5.46582 1.21929 5.86234C1.27556 6.28087 1.39934 6.65873 1.70299 6.96238C2.00664 7.26603 2.3845 7.38981 2.80303 7.44608C3.19955 7.49939 3.69902 7.49937 4.298 7.49935H4.36735C4.96633 7.49937 5.46582 7.49939 5.86234 7.44608C6.28087 7.38981 6.65873 7.26603 6.96238 6.96238C7.26603 6.65873 7.38981 6.28087 7.44608 5.86234C7.49939 5.46582 7.49937 4.96635 7.49935 4.36737V4.29802C7.49937 3.69904 7.49939 3.19955 7.44608 2.80303C7.38981 2.3845 7.26603 2.00664 6.96238 1.70299C6.65873 1.39934 6.28087 1.27556 5.86234 1.21929C5.46582 1.16598 4.96635 1.166 4.36737 1.16602H4.29802ZM2.4101 2.4101C2.49697 2.32322 2.62864 2.25174 2.93628 2.21037C3.2583 2.16708 3.69001 2.16602 4.33268 2.16602C4.97536 2.16602 5.40706 2.16708 5.72909 2.21037C6.03673 2.25174 6.1684 2.32322 6.25527 2.4101C6.34215 2.49697 6.41363 2.62864 6.45499 2.93628C6.49829 3.2583 6.49935 3.69001 6.49935 4.33268C6.49935 4.97536 6.49829 5.40706 6.45499 5.72909C6.41363 6.03673 6.34215 6.1684 6.25527 6.25527C6.1684 6.34215 6.03673 6.41363 5.72909 6.45499C5.40706 6.49829 4.97536 6.49935 4.33268 6.49935C3.69001 6.49935 3.2583 6.49829 2.93628 6.45499C2.62864 6.41363 2.49697 6.34215 2.4101 6.25527C2.32322 6.1684 2.25174 6.03673 2.21037 5.72909C2.16708 5.40706 2.16602 4.97536 2.16602 4.33268C2.16602 3.69001 2.16708 3.2583 2.21037 2.93628C2.25174 2.62864 2.32322 2.49697 2.4101 2.4101Z"
        fill={props.fill || '#FAFAFA'}
      />
      <path
        d="M12.166 2.33268C12.166 2.05654 11.9422 1.83268 11.666 1.83268C11.3899 1.83268 11.166 2.05654 11.166 2.33268V3.83268H9.66602C9.38987 3.83268 9.16602 4.05654 9.16602 4.33268C9.16602 4.60883 9.38987 4.83268 9.66602 4.83268H11.166V6.33268C11.166 6.60883 11.3899 6.83268 11.666 6.83268C11.9422 6.83268 12.166 6.60883 12.166 6.33268V4.83268H13.666C13.9422 4.83268 14.166 4.60883 14.166 4.33268C14.166 4.05654 13.9422 3.83268 13.666 3.83268H12.166V2.33268Z"
        fill={props.fill || '#FAFAFA'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.6314 8.49935H11.7007C12.2997 8.49933 12.7991 8.49931 13.1957 8.55262C13.6142 8.60889 13.9921 8.73267 14.2957 9.03632C14.5994 9.33997 14.7231 9.71783 14.7794 10.1364C14.8327 10.5329 14.8327 11.0323 14.8327 11.6313V11.7007C14.8327 12.2997 14.8327 12.7992 14.7794 13.1957C14.7231 13.6142 14.5994 13.9921 14.2957 14.2957C13.9921 14.5994 13.6142 14.7231 13.1957 14.7794C12.7992 14.8327 12.2997 14.8327 11.7007 14.8327H11.6314C11.0324 14.8327 10.5329 14.8327 10.1364 14.7794C9.71783 14.7231 9.33997 14.5994 9.03632 14.2957C8.73267 13.9921 8.60889 13.6142 8.55262 13.1957C8.49931 12.7991 8.49933 12.2997 8.49935 11.7007V11.6314C8.49933 11.0324 8.49931 10.5329 8.55262 10.1364C8.60889 9.71783 8.73267 9.33997 9.03632 9.03632C9.33997 8.73267 9.71783 8.60889 10.1364 8.55262C10.5329 8.49931 11.0324 8.49933 11.6314 8.49935ZM10.2696 9.54371C9.96197 9.58507 9.83031 9.65655 9.74343 9.74343C9.65655 9.83031 9.58507 9.96197 9.54371 10.2696C9.50041 10.5916 9.49935 11.0233 9.49935 11.666C9.49935 12.3087 9.50041 12.7404 9.54371 13.0624C9.58507 13.3701 9.65655 13.5017 9.74343 13.5886C9.83031 13.6755 9.96197 13.747 10.2696 13.7883C10.5916 13.8316 11.0233 13.8327 11.666 13.8327C12.3087 13.8327 12.7404 13.8316 13.0624 13.7883C13.3701 13.747 13.5017 13.6755 13.5886 13.5886C13.6755 13.5017 13.747 13.3701 13.7883 13.0624C13.8316 12.7404 13.8327 12.3087 13.8327 11.666C13.8327 11.0233 13.8316 10.5916 13.7883 10.2696C13.747 9.96197 13.6755 9.83031 13.5886 9.74343C13.5017 9.65655 13.3701 9.58507 13.0624 9.54371C12.7404 9.50041 12.3087 9.49935 11.666 9.49935C11.0233 9.49935 10.5916 9.50041 10.2696 9.54371Z"
        fill={props.fill || '#FAFAFA'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.29802 8.49935C3.69903 8.49933 3.19955 8.49931 2.80303 8.55262C2.3845 8.60889 2.00664 8.73267 1.70299 9.03632C1.39934 9.33997 1.27556 9.71783 1.21929 10.1364C1.16598 10.5329 1.166 11.0324 1.16602 11.6313V11.7007C1.166 12.2997 1.16598 12.7992 1.21929 13.1957C1.27556 13.6142 1.39934 13.9921 1.70299 14.2957C2.00664 14.5994 2.3845 14.7231 2.80303 14.7794C3.19955 14.8327 3.69902 14.8327 4.298 14.8327H4.36735C4.96633 14.8327 5.46582 14.8327 5.86234 14.7794C6.28087 14.7231 6.65873 14.5994 6.96238 14.2957C7.26603 13.9921 7.38981 13.6142 7.44608 13.1957C7.49939 12.7992 7.49937 12.2997 7.49935 11.7007V11.6314C7.49937 11.0324 7.49939 10.5329 7.44608 10.1364C7.38981 9.71783 7.26603 9.33997 6.96238 9.03632C6.65873 8.73267 6.28087 8.60889 5.86234 8.55262C5.46582 8.49931 4.96634 8.49933 4.36735 8.49935H4.29802ZM2.4101 9.74343C2.49697 9.65655 2.62864 9.58507 2.93628 9.54371C3.2583 9.50041 3.69001 9.49935 4.33268 9.49935C4.97536 9.49935 5.40706 9.50041 5.72909 9.54371C6.03673 9.58507 6.1684 9.65655 6.25527 9.74343C6.34215 9.83031 6.41363 9.96197 6.45499 10.2696C6.49829 10.5916 6.49935 11.0233 6.49935 11.666C6.49935 12.3087 6.49829 12.7404 6.45499 13.0624C6.41363 13.3701 6.34215 13.5017 6.25527 13.5886C6.1684 13.6755 6.03673 13.747 5.72909 13.7883C5.40706 13.8316 4.97536 13.8327 4.33268 13.8327C3.69001 13.8327 3.2583 13.8316 2.93628 13.7883C2.62864 13.747 2.49697 13.6755 2.4101 13.5886C2.32322 13.5017 2.25174 13.3701 2.21037 13.0624C2.16708 12.7404 2.16602 12.3087 2.16602 11.666C2.16602 11.0233 2.16708 10.5916 2.21037 10.2696C2.25174 9.96197 2.32322 9.83031 2.4101 9.74343Z"
        fill={props.fill || '#FAFAFA'}
      />
    </svg>
  )
})
AddWidgetIcon.displayName = 'AddWidgetIcon'

export default AddWidgetIcon
