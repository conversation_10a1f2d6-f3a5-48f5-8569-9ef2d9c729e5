import React, { ReactElement, forwardRef } from 'react'

interface CorrectIconProps {
  className?: string
  color?: string
  onClick?: (e: any) => void
  fill?: string
}

const CorrectIcon: React.FC<CorrectIconProps> = forwardRef<SVGSVGElement, CorrectIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
        ref={ref}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.3282 4.62335C12.5362 4.80496 12.5576 5.12081 12.376 5.32884L7.13792 11.3288C7.04297 11.4376 6.90564 11.5 6.76126 11.5C6.61688 11.5 6.47955 11.4376 6.3846 11.3288L4.28936 8.92884C4.10776 8.72081 4.12917 8.40496 4.33719 8.22335C4.54522 8.04174 4.86107 8.06316 5.04268 8.27118L6.76126 10.2397L11.6227 4.67118C11.8043 4.46316 12.1202 4.44174 12.3282 4.62335Z"
          fill={props.color ? props.color : '#EAEAEA'}
        />
      </svg>
    )
  },
)
CorrectIcon.displayName = 'CorrectIcon'

export default CorrectIcon
