import React, { ReactElement, forwardRef } from 'react'

interface UserShieldIconProps {
  className?: string
  onClick?: (e: any) => void
}
// {...props}
//       ref={ref}
const GovernanceIcon: React.FC<UserShieldIconProps> = forwardRef<SVGSVGElement, UserShieldIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 26 23"
        overflow="hidden"
        {...props}
        ref={ref}
      >
        <defs>
          <clipPath id="clip0">
            <rect x="104" y="714" width="26" height="23" />
          </clipPath>
          <clipPath id="clip1">
            <rect x="104" y="714" width="26" height="23" />
          </clipPath>
          <clipPath id="clip2">
            <rect x="104" y="715" width="26" height="22" />
          </clipPath>
          <clipPath id="clip3">
            <rect x="104" y="715" width="26" height="22" />
          </clipPath>
          <clipPath id="clip4">
            <rect x="104" y="715" width="26" height="22" />
          </clipPath>
        </defs>
        <g transform="translate(-104 -714)">
          <g>
            <g>
              <g>
                <g>
                  <path
                    d="M10.1948 21.3486C7.42484 21.3486 4.65487 21.3486 1.8849 21.3486 0.845882 21.3486 0.00895705 20.529 0.00447853 19.4917-0.00167945 18.0205-0.00111963 16.5498 0.00447853 15.0786 0.00839724 14.0413 0.844202 13.2184 1.88098 13.2167 2.09259 13.2167 2.30476 13.2139 2.51637 13.2178 2.61266 13.2195 2.65185 13.1865 2.65129 13.0863 2.64849 12.5068 2.64849 11.9269 2.65129 11.3475 2.65129 11.2534 2.61434 11.2226 2.52309 11.2215 2.25158 11.2187 1.98007 11.2344 1.70856 11.2092 0.741756 11.1196 0.00615797 10.3241 0.00391871 9.35116 0 7.88837 0.000559816 6.42501 0.00391871 4.96221 0.00615797 3.91087 0.836925 3.08627 1.8905 3.08627 5.95588 3.08571 10.0207 3.08571 14.0861 3.0885 14.2176 3.0885 14.3223 3.05715 14.4259 2.97486 14.7338 2.73134 15.0227 2.46767 15.3031 2.19392 15.887 1.62515 16.4396 1.0239 17.0598 0.493198 17.23 0.347646 17.408 0.21217 17.5917 0.0845322 17.7148-0.00111963 17.8425-0.0223926 17.9886 0.0268712 18.6335 0.24408 19.298 0.349885 19.9776 0.352124 20.6617 0.354363 21.3307 0.250238 21.9795 0.0324693 22.1441-0.0229525 22.284 0.00279908 22.4206 0.0979678 22.8517 0.398029 23.2374 0.751833 23.6181 1.11123 24.202 1.66209 24.745 2.25662 25.3608 2.77445 25.4985 2.89033 25.6396 3.00173 25.7857 3.10642 25.9357 3.2139 26.0012 3.35218 25.9967 3.53636 25.9749 4.43094 25.7605 5.28354 25.4319 6.10871 24.8698 7.52057 24.0676 8.78127 23.0286 9.89083 22.3423 10.6231 21.5764 11.2495 20.6639 11.6828 20.4758 11.7718 20.281 11.8435 20.0845 11.9118 20.005 11.9392 19.9272 11.9224 19.85 11.8961 19.4206 11.7483 19.0169 11.5479 18.6368 11.301 18.5523 11.2461 18.4678 11.2198 18.3676 11.2232 18.1996 11.2282 18.0317 11.2271 17.8637 11.2237 17.7809 11.2221 17.7389 11.2461 17.7394 11.3385 17.7422 11.9263 17.7422 12.5136 17.7394 13.1014 17.7394 13.1921 17.7775 13.2178 17.8615 13.2189 18.1571 13.2234 18.4532 13.2021 18.7482 13.2329 19.5381 13.3152 20.2122 13.94 20.3616 14.7237 20.3874 14.8581 20.403 14.9919 20.403 15.1279 20.403 16.5627 20.4047 17.9981 20.4025 19.4329 20.4014 20.3096 19.8763 21.0216 19.0472 21.2741 18.8792 21.3256 18.7051 21.3508 18.5277 21.3508 15.7499 21.3497 12.9715 21.3503 10.1937 21.3503ZM10.1909 20.5536C12.9206 20.5536 15.6502 20.5536 18.3799 20.5536 18.4398 20.5536 18.4997 20.5536 18.5596 20.5503 19.1659 20.5173 19.6025 20.0649 19.6036 19.4564 19.6064 18.0098 19.6048 16.5627 19.6048 15.1161 19.6048 14.9958 19.5919 14.8765 19.5521 14.7623 19.3831 14.2781 18.9918 14.0133 18.4443 14.0133 12.9452 14.0133 7.44611 14.0156 1.94704 14.01 1.31837 14.0094 0.79046 14.4852 0.799417 15.1542 0.817331 16.5526 0.805015 17.9516 0.805015 19.3506 0.805015 19.4144 0.803336 19.4788 0.807814 19.5426 0.836925 19.9737 1.05861 20.2737 1.44153 20.4596 1.61843 20.5458 1.80988 20.5548 2.00358 20.5548 4.73324 20.5536 7.46291 20.5542 10.1926 20.5542ZM17.4483 10.4232C17.3207 10.2855 17.2003 10.1523 17.0761 10.0224 16.3304 9.23976 15.6961 8.37597 15.1783 7.42708 14.6039 6.3735 14.1785 5.26843 14.0391 4.06706 14.0183 3.8868 14.0156 3.8868 13.8358 3.8868 9.8634 3.8868 5.89094 3.89072 1.91849 3.88232 1.31221 3.8812 0.79158 4.36936 0.798297 5.00195 0.813412 6.43676 0.802776 7.87157 0.803336 9.30638 0.803336 9.40603 0.801656 9.50567 0.829647 9.60364 0.976879 10.1164 1.38275 10.4227 1.92017 10.4227 7.05984 10.4227 12.1989 10.4227 17.3386 10.4227L17.4478 10.4227ZM25.1928 3.86049C25.1861 3.76084 25.2259 3.68135 25.1352 3.61249 24.5586 3.17248 24.0558 2.65129 23.5414 2.14354 23.1176 1.72591 22.6905 1.31165 22.2264 0.937132 22.1362 0.864356 22.0551 0.847561 21.9425 0.88115 21.4006 1.04238 20.8436 1.12467 20.2799 1.14818 19.5325 1.17897 18.7975 1.08772 18.0776 0.88283 17.9701 0.85204 17.8872 0.858198 17.7937 0.934893 17.4998 1.17561 17.2188 1.43033 16.9473 1.69568 16.2682 2.36074 15.6256 3.06443 14.8676 3.64496 14.8273 3.67575 14.8189 3.71214 14.8217 3.75972 14.8575 4.30946 14.9717 4.84353 15.1492 5.36304 15.714 7.01841 16.6265 8.45602 17.876 9.67866 18.39 10.1814 18.9503 10.6264 19.6048 10.9332 20.084 11.1577 19.9227 11.1599 20.3986 10.9332 21.2041 10.5492 21.8709 9.98096 22.4738 9.33269 23.4955 8.23321 24.2753 6.98426 24.7914 5.57353 24.9963 5.01315 25.1419 4.43766 25.194 3.86217ZM10.1814 13.2167C12.3798 13.2167 14.5782 13.2167 16.7766 13.2167 16.9423 13.2167 16.9462 13.2128 16.9462 13.0499 16.9462 12.5024 16.9462 11.9549 16.9462 11.4074 16.9462 11.2849 16.8835 11.2237 16.7581 11.2237 14.9835 11.2237 13.2089 11.2237 11.4342 11.2237 8.8283 11.2237 6.22235 11.2237 3.61641 11.2237 3.45126 11.2237 3.45071 11.2243 3.45071 11.39 3.45071 11.9336 3.45071 12.4772 3.45071 13.0208 3.45071 13.1518 3.51602 13.2173 3.64664 13.2173 5.82488 13.2173 8.00313 13.2173 10.1814 13.2173Z"
                    fill="#444444"
                    fillRule="nonzero"
                    fill-opacity="1"
                    transform="matrix(1 0 0 1.03049 104.001 715)"
                  />
                  <path
                    d="M14.8043 17.2742C14.8082 16.4127 15.522 15.7006 16.3853 15.7096 17.2658 15.7191 17.9567 16.4116 17.9499 17.2877 17.9432 18.2114 17.2043 18.8529 16.3718 18.8512 15.5047 18.8496 14.8004 18.1375 14.8043 17.2742ZM16.3797 18.0552C16.8079 18.0546 17.1539 17.7115 17.1528 17.2888 17.1516 16.8656 16.8057 16.5151 16.3841 16.5106 15.9609 16.5062 15.6049 16.8633 15.6054 17.2905 15.6065 17.7154 15.9508 18.0557 16.3797 18.0546Z"
                    fill="#444444"
                    fillRule="nonzero"
                    fill-opacity="1"
                    transform="matrix(1 0 0 1.03049 104.001 715)"
                  />
                  <path
                    d="M5.58472 17.2882C5.58416 18.1537 4.88887 18.8507 4.02452 18.8524 3.14672 18.854 2.45143 18.1504 2.45199 17.2619 2.45311 16.4127 3.15904 15.7073 4.015 15.7096 4.96165 15.7124 5.61943 16.5079 5.58472 17.2882ZM4.02284 18.0552C4.44774 18.0546 4.80378 17.7031 4.7993 17.2877 4.79482 16.879 4.43598 16.5129 4.03571 16.509 3.60298 16.5051 3.24749 16.8594 3.24973 17.2927 3.25197 17.7221 3.59066 18.0557 4.02284 18.0552Z"
                    fill="#444444"
                    fillRule="nonzero"
                    fill-opacity="1"
                    transform="matrix(1 0 0 1.03049 104.001 715)"
                  />
                  <path
                    d="M10.1998 17.684C9.52471 17.684 8.84957 17.684 8.17443 17.684 8.13077 17.684 8.08598 17.6857 8.04287 17.6807 7.84078 17.6572 7.69523 17.5049 7.68515 17.3084 7.67452 17.1085 7.81055 16.9355 8.0132 16.8969 8.06359 16.8874 8.11621 16.8846 8.16771 16.8846 9.52583 16.884 10.8845 16.8846 12.2426 16.8835 12.3798 16.8835 12.508 16.9081 12.6015 17.0156 12.7095 17.1399 12.742 17.286 12.6765 17.4399 12.6138 17.5877 12.4968 17.6723 12.3339 17.6823 12.2505 17.6874 12.1659 17.6846 12.0825 17.6846 11.4555 17.6846 10.828 17.6846 10.201 17.6846Z"
                    fill="#444444"
                    fillRule="nonzero"
                    fill-opacity="1"
                    transform="matrix(1 0 0 1.03049 104.001 715)"
                  />
                  <path
                    d="M5.58472 7.15053C5.61495 7.97682 4.88271 8.72921 4.01836 8.71689 3.13777 8.70402 2.45143 8.02272 2.45199 7.13597 2.45255 6.26994 3.15344 5.58192 4.03291 5.58192 4.95269 5.58192 5.61775 6.37294 5.58472 7.15053ZM4.02172 6.3819C3.59122 6.3819 3.25309 6.71611 3.25029 7.14437 3.24749 7.57151 3.59906 7.92923 4.0206 7.92811 4.43878 7.92699 4.8021 7.56255 4.79986 7.14605 4.79762 6.72843 4.44438 6.3819 4.02172 6.38134Z"
                    fill="#444444"
                    fillRule="nonzero"
                    fill-opacity="1"
                    transform="matrix(1 0 0 1.03049 104.001 715)"
                  />
                  <path
                    d="M10.206 6.75586C10.9091 6.75586 11.6123 6.75474 12.3148 6.75642 12.5108 6.75642 12.6619 6.88462 12.6994 7.07047 12.7364 7.25521 12.6429 7.43771 12.471 7.51329 12.3926 7.548 12.3081 7.55192 12.2241 7.55192 10.8699 7.55248 9.51575 7.55304 8.16212 7.55192 8.09886 7.55192 8.03336 7.5452 7.97234 7.52896 7.7876 7.47914 7.665 7.29552 7.68627 7.10966 7.70922 6.90645 7.86765 6.75698 8.0731 6.75642 8.78407 6.75418 9.49504 6.75586 10.206 6.75586Z"
                    fill="#444444"
                    fillRule="nonzero"
                    fill-opacity="1"
                    transform="matrix(1 0 0 1.03049 104.001 715)"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    )
  },
)
GovernanceIcon.displayName = 'GovernanceIcon'

export default GovernanceIcon
