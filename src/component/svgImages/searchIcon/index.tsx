import React, { ReactElement, forwardRef } from 'react'

interface SearchIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: string
}

const SearchIcon: React.FC<SearchIconProps> = forwardRef<SVGSVGElement, SearchIconProps>((props, ref): ReactElement => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      ref={ref}
      {...props}
      overflow="hidden"
    >
      <defs>
        <clipPath id="clip0">
          <rect x="33" y="116" width="27" height="20" />
        </clipPath>
        <clipPath id="clip1">
          <rect x="33" y="116" width="27" height="20" />
        </clipPath>
        <clipPath id="clip2">
          <rect x="34" y="116" width="26" height="20" />
        </clipPath>
        <clipPath id="clip3">
          <rect x="34" y="116" width="26" height="20" />
        </clipPath>
        <clipPath id="clip4">
          <rect x="34" y="116" width="26" height="20" />
        </clipPath>
      </defs>
      <g clipPath="url(#clip01)" transform="translate(-33 -116)">
        <g clipPath="url(#clip11)">
          <g clipPath="url(#clip21)">
            <g clipPath="url(#clip31)">
              <g clipPath="url(#clip41)">
                <path
                  d="M17.5479 8.78467C17.5422 13.6319 13.6041 17.5485 8.74297 17.5422 3.9109 17.5359-0.0151634 13.5826 0 8.73918 0.0154793 3.88468 3.94312-0.0072658 8.82006 0 13.6351 0.0069499 17.5536 3.95133 17.5479 8.78467ZM0.89243 8.84438C0.952768 13.2244 4.51522 16.7085 8.87976 16.6431 13.3564 16.5761 16.6854 12.9047 16.652 8.74203 16.6156 4.19995 12.929 0.868106 8.76161 0.893062 4.16994 0.920546 0.84694 4.66433 0.89243 8.8447Z"
                  stroke={props.color}
                  strokeWidth="0.51187"
                  strokeLinecap="butt"
                  strokeLinejoin="miter"
                  strokeMiterlimit="4"
                  strokeOpacity="1"
                  fill={props.color}
                  fillRule="nonzero"
                  fillOpacity="1"
                  transform="matrix(1.02606 0 0 1 34 116.005)"
                />
                <path
                  d="M25.3397 18.2198C25.3175 18.8702 25.0234 19.4127 24.4147 19.7447 23.7886 20.0862 23.1406 20.076 22.5401 19.6846 21.346 18.9063 20.1667 18.1051 18.9814 17.3131 18.518 17.0036 18.0533 16.6952 17.5918 16.3825 16.7398 15.8053 16.5161 14.6719 17.0866 13.836 17.6442 13.0191 18.7805 12.8112 19.6208 13.3726 21.2392 14.4536 22.8579 15.5346 24.4757 16.6166 25.0313 16.9881 25.3242 17.5071 25.3393 18.2198ZM18.5821 13.9516C18.186 13.9532 17.8385 14.1996 17.714 14.5676 17.5874 14.9423 17.7194 15.3665 18.0511 15.6076 18.2678 15.7652 18.4959 15.9074 18.7189 16.0565 20.1402 17.007 21.5608 17.9582 22.983 18.9078 23.4341 19.2089 24.0062 19.1056 24.2943 18.6763 24.5793 18.2514 24.4472 17.6758 23.9986 17.3773 23.5125 17.0538 23.0279 16.7278 22.5426 16.403 21.4142 15.6486 20.2855 14.8949 19.1574 14.1399 18.9827 14.023 18.7973 13.944 18.5828 13.9513Z"
                  stroke={props.color}
                  strokeWidth="0.51187"
                  strokeLinecap="butt"
                  strokeLinejoin="miter"
                  strokeMiterlimit="4"
                  strokeOpacity="1"
                  fill={props.color}
                  fillRule="nonzero"
                  fillOpacity="1"
                  transform="matrix(1.02606 0 0 1 34 116.005)"
                />
                <path
                  d="M8.77836 2.76448C9.65499 2.7629 10.4893 2.95244 11.286 3.31415 11.5337 3.42662 11.6376 3.67839 11.5337 3.91406 11.4342 4.13961 11.1802 4.2407 10.9357 4.13266 10.3923 3.89289 9.83063 3.73115 9.23705 3.67966 8.06883 3.57857 6.99381 3.85056 6.00377 4.47226 5.68502 4.67223 5.32899 4.53481 5.27687 4.187 5.24749 3.99019 5.33057 3.84077 5.49705 3.73368 6.49658 3.09018 7.59119 2.7708 8.77867 2.76385Z"
                  stroke={props.color}
                  strokeWidth="0.51187"
                  strokeLinecap="butt"
                  strokeLinejoin="miter"
                  strokeMiterlimit="4"
                  strokeOpacity="1"
                  fill={props.color}
                  fillRule="nonzero"
                  fillOpacity="1"
                  transform="matrix(1.02606 0 0 1 34 116.005)"
                />
                <path
                  d="M12.5711 5.19884C12.592 5.0093 12.6962 4.85324 12.8639 4.78753 13.0421 4.71772 13.2301 4.7313 13.3419 4.87946 13.6003 5.22159 13.8439 5.57666 14.066 5.94311 14.1873 6.14308 14.078 6.40339 13.8767 6.50858 13.6679 6.61788 13.4234 6.55976 13.2911 6.36674 13.0807 6.06 12.869 5.75389 12.6649 5.44304 12.6182 5.37164 12.6014 5.28098 12.5708 5.19884Z"
                  stroke={props.color}
                  strokeWidth="0.51187"
                  strokeLinecap="butt"
                  strokeLinejoin="miter"
                  strokeMiterlimit="4"
                  strokeOpacity="1"
                  fill={props.color}
                  fillRule="nonzero"
                  fillOpacity="1"
                  transform="matrix(1.02606 0 0 1 34 116.005)"
                />
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
})
SearchIcon.displayName = 'SearchIcon'

export default SearchIcon
