import React, { ReactElement, forwardRef } from 'react'

interface LogoutIconProps {
  className?: string
  onClick?: (e: any) => void
  color?: string
}

const LogoutIcon: React.FC<LogoutIconProps> = forwardRef<SVGSVGElement, LogoutIconProps>((props, ref): ReactElement => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="22"
      viewBox="0 0 40 40"
      overflow="hidden"
      ref={ref}
      {...props}
    >
      <defs>
        <clipPath id="clip0">
          <rect x="36" y="190" width="20" height="22" />
        </clipPath>
        <clipPath id="clip1">
          <rect x="36" y="190" width="20" height="22" />
        </clipPath>
        <clipPath id="clip2">
          <rect x="37" y="191" width="19" height="20" />
        </clipPath>
        <clipPath id="clip3">
          <rect x="37" y="191" width="19" height="20" />
        </clipPath>
        <clipPath id="clip4">
          <rect x="37" y="191" width="19" height="20" />
        </clipPath>
      </defs>
      <g clipPath="url(#clip0)" transform="translate(-36 -190)">
        <g clipPath="url(#clip1)">
          <g clipPath="url(#clip2)">
            <g clipPath="url(#clip3)">
              <g clipPath="url(#clip4)">
                <path
                  d="M6.09317 19.1527C4.94921 19.1527 3.80462 19.1565 2.66067 19.1514 1.11572 19.1444 0.00834065 18.0453 0.00192477 16.5061-0.00384953 14.9984 0.00449112 13.4907 0.00449112 11.9829 0.00449112 8.8629-0.00128318 5.7435 0.00320794 2.62346 0.00513271 1.46924 0.651212 0.53316 1.71112 0.162963 1.99855 0.0628757 2.31613 0.00962383 2.62025 0.00834065 4.98322-0.00384953 7.34683 0.00320794 9.7098 0 10.0319 0 10.3001 0.0866144 10.4662 0.383028 10.6042 0.629398 10.5933 0.877051 10.4496 1.11444 10.2821 1.39161 10.0184 1.46988 9.70852 1.46924 8.30793 1.46539 6.9067 1.46731 5.50611 1.46731 4.58672 1.46731 3.66668 1.46603 2.74728 1.46731 1.90359 1.46924 1.46924 1.9068 1.46795 2.75883 1.46475 4.60853 1.45961 6.45887 1.46346 8.30857 1.46539 9.33447 1.49298 10.3604 1.49233 11.3863 1.49105 13.0319 1.50196 14.6789 1.45512 16.3233 1.43203 17.1458 1.953 17.7066 2.83775 17.6924 5.13656 17.6565 7.43601 17.6815 9.73482 17.6796 10.0749 17.6796 10.3379 17.7983 10.4893 18.1165 10.6183 18.3866 10.601 18.6741 10.3886 18.8576 10.2122 19.0103 9.94655 19.1347 9.71686 19.1399 8.50939 19.1668 7.30063 19.1527 6.09188 19.1527Z"
                  fill="#818282"
                  fillRule="nonzero"
                  fillOpacity="0.75"
                  transform="matrix(1 0 0 1.04207 37 191.019)"
                />
                <path
                  d="M16.4625 8.78976C16.0339 8.36824 15.6605 8.0051 15.2923 7.63747 14.5519 6.89836 13.807 6.16438 13.0794 5.41244 12.8016 5.12501 12.8408 4.67012 13.1218 4.40065 13.4182 4.11579 13.8564 4.1299 14.1823 4.45198 15.0324 5.29439 15.8767 6.14257 16.7236 6.98882 17.3883 7.65351 18.0549 8.31563 18.7164 8.98352 19.1027 9.37361 19.0917 9.83234 18.701 10.2199 17.2234 11.6853 15.7452 13.1506 14.2715 14.6199 14.038 14.8528 13.7871 15.0132 13.4464 14.9291 12.8754 14.7886 12.7118 14.1034 13.1481 13.6581 13.7396 13.055 14.3408 12.4629 14.9387 11.8655 15.3988 11.4062 15.8588 10.9474 16.3182 10.4874 16.3522 10.4534 16.3772 10.4104 16.4503 10.3142 16.075 10.3142 15.7587 10.3142 15.443 10.3142 13.7691 10.3142 12.0952 10.3103 10.4213 10.3161 9.45188 10.3193 8.48244 10.3418 7.51364 10.3488 7.16783 10.3514 6.89387 10.2179 6.73026 9.89201 6.59425 9.6219 6.64108 9.37232 6.80148 9.13622 6.97535 8.88087 7.23904 8.80773 7.53032 8.81157 8.15972 8.81992 8.78912 8.8443 9.41788 8.84237 10.7505 8.83852 12.0824 8.8135 13.415 8.81286 14.3504 8.81286 15.2865 8.83788 16.2219 8.84943 16.2713 8.84943 16.3207 8.82569 16.4625 8.78848Z"
                  fill="#818282"
                  fillRule="nonzero"
                  fillOpacity="0.75"
                  transform="matrix(1 0 0 1.04207 37 191.019)"
                />
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
})
LogoutIcon.displayName = 'LogoutIcon'

export default LogoutIcon
