import React, { ReactElement, forwardRef } from 'react'

interface UserIconProps {
  className?: string
  onClick?: (e: any) => void
  fill?: any
}

const UserIcon: React.FC<UserIconProps> = forwardRef<SVGSVGElement, UserIconProps>((props, ref): ReactElement => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" {...props} ref={ref}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 1.04199C7.8139 1.04199 6.0417 2.8142 6.0417 5.00033C6.0417 7.18645 7.8139 8.95866 10 8.95866C12.1862 8.95866 13.9584 7.18645 13.9584 5.00033C13.9584 2.8142 12.1862 1.04199 10 1.04199ZM7.2917 5.00033C7.2917 3.50455 8.50426 2.29199 10 2.29199C11.4958 2.29199 12.7084 3.50455 12.7084 5.00033C12.7084 6.4961 11.4958 7.70866 10 7.70866C8.50426 7.70866 7.2917 6.4961 7.2917 5.00033Z"
        fill={props?.fill ? props?.fill : '#444444'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 10.2087C8.07207 10.2087 6.2958 10.6469 4.97957 11.3873C3.68293 12.1166 2.70836 13.222 2.70836 14.5837L2.70831 14.6686C2.70737 15.6368 2.70619 16.852 3.77204 17.7199C4.2966 18.1471 5.03043 18.4509 6.02187 18.6515C7.01608 18.8528 8.31188 18.9587 10 18.9587C11.6882 18.9587 12.984 18.8528 13.9782 18.6515C14.9696 18.4509 15.7035 18.1471 16.228 17.7199C17.2939 16.852 17.2927 15.6368 17.2917 14.6686L17.2917 14.5837C17.2917 13.222 16.3171 12.1166 15.0205 11.3873C13.7043 10.6469 11.928 10.2087 10 10.2087ZM3.95836 14.5837C3.95836 13.8742 4.47618 13.1046 5.5924 12.4767C6.68903 11.8599 8.24609 11.4587 10 11.4587C11.754 11.4587 13.311 11.8599 14.4077 12.4767C15.5239 13.1046 16.0417 13.8742 16.0417 14.5837C16.0417 15.6735 16.0081 16.287 15.4387 16.7507C15.1299 17.0021 14.6138 17.2475 13.7302 17.4264C12.8494 17.6047 11.6452 17.7087 10 17.7087C8.35484 17.7087 7.15065 17.6047 6.26986 17.4264C5.3863 17.2475 4.87013 17.0021 4.56135 16.7507C3.99196 16.287 3.95836 15.6735 3.95836 14.5837Z"
        fill={props?.fill ? props?.fill : '#444444'}
      />
    </svg>
  )
})
UserIcon.displayName = 'UserIcon'

export default UserIcon
