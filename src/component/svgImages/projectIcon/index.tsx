import React, { ReactElement, forwardRef } from 'react'

interface ProjectIconProps {
  className?: string
  onClick?: (e: any) => void
}

const ProjectIcon: React.FC<ProjectIconProps> = forwardRef<SVGSVGElement, ProjectIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
        <g clipPath="url(#clip0_1062_18658)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M4.66732 0.833496C4.94346 0.833496 5.16732 1.05735 5.16732 1.3335V2.50016H5.33398C5.34396 2.50016 5.3539 2.50016 5.36382 2.50016C5.65236 2.50013 5.91692 2.5001 6.13212 2.52903C6.36911 2.56089 6.61959 2.63586 6.82561 2.84187C7.03162 3.04789 7.10659 3.29837 7.13845 3.53536C7.16739 3.75057 7.16735 4.01512 7.16732 4.30367C7.16732 4.31358 7.16732 4.32353 7.16732 4.3335V5.25487C7.2119 5.29018 7.25516 5.32862 7.29701 5.37047C7.60066 5.67411 7.72444 6.05198 7.78071 6.47051C7.81956 6.75949 7.83009 7.10315 7.83294 7.50016H8.16732L8.16732 5.14016C8.1673 4.30126 8.16728 3.61549 8.2444 3.09223C8.32436 2.54964 8.50249 2.06519 8.95639 1.75063C9.41029 1.43608 9.92645 1.43939 10.4626 1.55503C10.9796 1.66656 11.6217 1.90736 12.4072 2.20194L12.4711 2.22591C12.8681 2.37477 13.204 2.50073 13.4679 2.63471C13.7486 2.77719 13.9912 2.94924 14.1742 3.21324C14.3571 3.47724 14.433 3.76483 14.4679 4.07766C14.5007 4.3718 14.5007 4.73056 14.5007 5.15455V14.1668H14.6673C14.9435 14.1668 15.1673 14.3907 15.1673 14.6668C15.1673 14.943 14.9435 15.1668 14.6673 15.1668H1.33398C1.05784 15.1668 0.833984 14.943 0.833984 14.6668C0.833984 14.3907 1.05784 14.1668 1.33398 14.1668H1.50065L1.50065 7.9655C1.50063 7.36651 1.50061 6.86703 1.55393 6.47051C1.6102 6.05198 1.73398 5.67411 2.03762 5.37047C2.07947 5.32862 2.12273 5.29018 2.16732 5.25487V4.3335C2.16732 4.32352 2.16732 4.31358 2.16732 4.30366C2.16728 4.01512 2.16725 3.75057 2.19618 3.53536C2.22805 3.29837 2.30301 3.04789 2.50903 2.84187C2.71504 2.63586 2.96553 2.56089 3.20251 2.52903C3.41772 2.5001 3.68227 2.50013 3.97082 2.50016C3.98073 2.50016 3.99068 2.50016 4.00065 2.50016H4.16732V1.3335C4.16732 1.05735 4.39118 0.833496 4.66732 0.833496ZM3.16732 4.88291C3.55899 4.83346 4.0485 4.83348 4.63265 4.8335H4.70198C5.28613 4.83348 5.77565 4.83346 6.16732 4.88291V4.3335C6.16732 4.00509 6.16626 3.80909 6.14737 3.66861C6.13871 3.60423 6.12852 3.57199 6.12255 3.55747C6.12116 3.55411 6.12008 3.5519 6.11941 3.55061L6.11849 3.54899L6.11687 3.54807C6.11558 3.5474 6.11337 3.54632 6.11001 3.54494C6.09549 3.53896 6.06325 3.52877 5.99888 3.52011C5.85839 3.50122 5.66239 3.50016 5.33398 3.50016H4.00065C3.67225 3.50016 3.47624 3.50122 3.33576 3.52011C3.27139 3.52877 3.23915 3.53896 3.22463 3.54494C3.22126 3.54632 3.21905 3.5474 3.21777 3.54807L3.21614 3.54899L3.21522 3.55061C3.21455 3.5519 3.21347 3.55411 3.21209 3.55747C3.20612 3.57199 3.19592 3.60423 3.18727 3.66861C3.16838 3.80909 3.16732 4.00509 3.16732 4.3335V4.88291ZM2.50065 14.1668H4.16732L4.16732 10.6322C4.1673 10.0332 4.16728 9.5337 4.22059 9.13718C4.27686 8.71865 4.40064 8.34078 4.70429 8.03713C5.00794 7.73349 5.3858 7.60971 5.80433 7.55344C6.09302 7.51462 6.43628 7.50408 6.83278 7.50122C6.82997 7.12107 6.82062 6.8343 6.78963 6.60376C6.74827 6.29612 6.67678 6.16445 6.58991 6.07757C6.50303 5.9907 6.37136 5.91921 6.06372 5.87785C5.7417 5.83456 5.30999 5.8335 4.66732 5.8335C4.02464 5.8335 3.59294 5.83456 3.27091 5.87785C2.96327 5.91921 2.83161 5.9907 2.74473 6.07757C2.65785 6.16445 2.58637 6.29612 2.54501 6.60376C2.50171 6.92578 2.50065 7.35749 2.50065 8.00016V14.1668ZM5.16732 14.1668H10.834V10.6668C10.834 10.0242 10.8329 9.59245 10.7896 9.27042C10.7483 8.96278 10.6768 8.83112 10.5899 8.74424C10.503 8.65736 10.3714 8.58588 10.0637 8.54452C9.7417 8.50122 9.30999 8.50016 8.66732 8.50016H7.33398C6.69131 8.50016 6.2596 8.50122 5.93758 8.54452C5.62994 8.58588 5.49827 8.65736 5.4114 8.74424C5.32452 8.83112 5.25304 8.96278 5.21167 9.27042C5.16838 9.59245 5.16732 10.0242 5.16732 10.6668V14.1668ZM11.834 14.1668H13.5007V5.1815C13.5007 4.72336 13.4999 4.42066 13.474 4.18842C13.4496 3.96864 13.4067 3.86145 13.3523 3.78283C13.2978 3.70422 13.2124 3.62649 13.0153 3.52639C12.8069 3.4206 12.5237 3.31365 12.0948 3.15278C11.261 2.84014 10.6881 2.62668 10.2517 2.53255C9.82595 2.44071 9.64596 2.48942 9.52599 2.57256C9.40602 2.6557 9.29721 2.80713 9.23371 3.23803C9.16862 3.6797 9.16732 4.29109 9.16732 5.1815V7.50121C9.56433 7.50406 9.90799 7.51459 10.197 7.55344C10.6155 7.60971 10.9934 7.73349 11.297 8.03713C11.6007 8.34078 11.7244 8.71865 11.7807 9.13718C11.834 9.5337 11.834 10.0332 11.834 10.6322L11.834 14.1668ZM3.21614 3.54899C3.21636 3.54881 3.21641 3.54872 3.21614 3.54899C3.21584 3.54929 3.21596 3.54922 3.21614 3.54899ZM6.16732 10.0002C6.16732 9.72402 6.39118 9.50016 6.66732 9.50016H9.33398C9.61013 9.50016 9.83398 9.72402 9.83398 10.0002C9.83398 10.2763 9.61013 10.5002 9.33398 10.5002H6.66732C6.39118 10.5002 6.16732 10.2763 6.16732 10.0002ZM6.16732 12.0002C6.16732 11.724 6.39118 11.5002 6.66732 11.5002H9.33398C9.61013 11.5002 9.83398 11.724 9.83398 12.0002C9.83398 12.2763 9.61013 12.5002 9.33398 12.5002H6.66732C6.39118 12.5002 6.16732 12.2763 6.16732 12.0002Z"
            fill="#EAEAEA"
          />
        </g>
        <defs>
          <clipPath id="clip0_1062_18658">
            <rect width="16" height="16" rx="3" fill="white" />
          </clipPath>
        </defs>
      </svg>
    )
  },
)
ProjectIcon.displayName = 'ProjectIcon'

export default ProjectIcon
