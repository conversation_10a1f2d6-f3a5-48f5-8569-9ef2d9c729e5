import React, { ReactElement, forwardRef } from 'react'

interface CalendarProps {
  className?: string
  onClick?: (e: any) => void
  color?: any
}

const CalendarIcon: React.FC<CalendarProps> = forwardRef<SVGSVGElement, CalendarProps>((props, ref): ReactElement => {
  return (
    <svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
      <path
        d="M11.334 8.33317C11.7022 8.33317 12.0007 8.03469 12.0007 7.6665C12.0007 7.29831 11.7022 6.99984 11.334 6.99984C10.9658 6.99984 10.6673 7.29831 10.6673 7.6665C10.6673 8.03469 10.9658 8.33317 11.334 8.33317Z"
        fill={props.color ? props.color : '#404040'}
      />
      <path
        d="M11.334 10.9998C11.7022 10.9998 12.0007 10.7014 12.0007 10.3332C12.0007 9.96498 11.7022 9.6665 11.334 9.6665C10.9658 9.6665 10.6673 9.96498 10.6673 10.3332C10.6673 10.7014 10.9658 10.9998 11.334 10.9998Z"
        fill={props.color ? props.color : '#404040'}
      />
      <path
        d="M8.66732 7.6665C8.66732 8.03469 8.36884 8.33317 8.00065 8.33317C7.63246 8.33317 7.33398 8.03469 7.33398 7.6665C7.33398 7.29831 7.63246 6.99984 8.00065 6.99984C8.36884 6.99984 8.66732 7.29831 8.66732 7.6665Z"
        fill={props.color ? props.color : '#404040'}
      />
      <path
        d="M8.66732 10.3332C8.66732 10.7014 8.36884 10.9998 8.00065 10.9998C7.63246 10.9998 7.33398 10.7014 7.33398 10.3332C7.33398 9.96498 7.63246 9.6665 8.00065 9.6665C8.36884 9.6665 8.66732 9.96498 8.66732 10.3332Z"
        fill={props.color ? props.color : '#404040'}
      />
      <path
        d="M4.66732 8.33317C5.03551 8.33317 5.33398 8.03469 5.33398 7.6665C5.33398 7.29831 5.03551 6.99984 4.66732 6.99984C4.29913 6.99984 4.00065 7.29831 4.00065 7.6665C4.00065 8.03469 4.29913 8.33317 4.66732 8.33317Z"
        fill={props.color ? props.color : '#404040'}
      />
      <path
        d="M4.66732 10.9998C5.03551 10.9998 5.33398 10.7014 5.33398 10.3332C5.33398 9.96498 5.03551 9.6665 4.66732 9.6665C4.29913 9.6665 4.00065 9.96498 4.00065 10.3332C4.00065 10.7014 4.29913 10.9998 4.66732 10.9998Z"
        fill={props.color ? props.color : '#404040'}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.66732 0.166504C4.94346 0.166504 5.16732 0.390361 5.16732 0.666504V1.17498C5.60865 1.1665 6.09487 1.1665 6.62962 1.1665H9.37159C9.90636 1.1665 10.3927 1.1665 10.834 1.17498V0.666504C10.834 0.390361 11.0578 0.166504 11.334 0.166504C11.6101 0.166504 11.834 0.390361 11.834 0.666504V1.2179C12.0073 1.23111 12.1714 1.24772 12.3267 1.26859C13.1083 1.37368 13.7409 1.59509 14.2398 2.094C14.7387 2.59291 14.9601 3.22554 15.0652 4.00716C15.1673 4.76663 15.1673 5.73704 15.1673 6.9622V8.37078C15.1673 9.59594 15.1673 10.5664 15.0652 11.3259C14.9601 12.1075 14.7387 12.7401 14.2398 13.239C13.7409 13.7379 13.1083 13.9593 12.3267 14.0644C11.5672 14.1665 10.5968 14.1665 9.37162 14.1665H6.62971C5.40455 14.1665 4.43411 14.1665 3.67464 14.0644C2.89302 13.9593 2.26039 13.7379 1.76148 13.239C1.26257 12.7401 1.04116 12.1075 0.936074 11.3259C0.833966 10.5664 0.833974 9.59595 0.833985 8.37078V6.96223C0.833974 5.73705 0.833966 4.76663 0.936074 4.00716C1.04116 3.22554 1.26257 2.59291 1.76148 2.094C2.26039 1.59509 2.89302 1.37368 3.67464 1.26859C3.82992 1.24772 3.99402 1.23111 4.16732 1.2179V0.666504C4.16732 0.390361 4.39118 0.166504 4.66732 0.166504ZM3.80788 2.25968C3.13716 2.34985 2.75073 2.51897 2.46859 2.80111C2.18645 3.08325 2.01733 3.46968 1.92716 4.1404C1.91189 4.25399 1.89912 4.37358 1.88844 4.49984H14.1129C14.1022 4.37358 14.0894 4.25399 14.0741 4.1404C13.984 3.46968 13.8149 3.08325 13.5327 2.80111C13.2506 2.51897 12.8641 2.34985 12.1934 2.25968C11.5083 2.16757 10.6052 2.1665 9.33398 2.1665H6.66732C5.3961 2.1665 4.49299 2.16757 3.80788 2.25968ZM1.83398 6.99984C1.83398 6.43049 1.8342 5.93499 1.84271 5.49984H14.1586C14.1671 5.93499 14.1673 6.43049 14.1673 6.99984V8.33317C14.1673 9.60438 14.1663 10.5075 14.0741 11.1926C13.984 11.8633 13.8149 12.2498 13.5327 12.5319C13.2506 12.814 12.8641 12.9832 12.1934 13.0733C11.5083 13.1654 10.6052 13.1665 9.33398 13.1665H6.66732C5.3961 13.1665 4.49299 13.1654 3.80788 13.0733C3.13716 12.9832 2.75073 12.814 2.46859 12.5319C2.18645 12.2498 2.01733 11.8633 1.92716 11.1926C1.83505 10.5075 1.83398 9.60438 1.83398 8.33317V6.99984Z"
        fill={props.color ? props.color : '#404040'}
      />
    </svg>
  )
})
CalendarIcon.displayName = 'CalendarIcon'

export default CalendarIcon
