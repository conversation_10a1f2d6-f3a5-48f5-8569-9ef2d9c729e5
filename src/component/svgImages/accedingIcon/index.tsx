import React, { ReactElement, forwardRef } from 'react'

interface AccedingIconProps {
  className?: string
  onClick?: (e: any) => void
}

const AccedingIcon: React.FC<AccedingIconProps> = forwardRef<SVGSVGElement, AccedingIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.3327 2.16699C11.6088 2.16699 11.8327 2.39085 11.8327 2.66699V11.8337L12.9327 10.367C13.0984 10.1461 13.4118 10.1013 13.6327 10.267C13.8536 10.4327 13.8984 10.7461 13.7327 10.967L11.7327 13.6337C11.6036 13.8058 11.3787 13.8761 11.1746 13.808C10.9704 13.7399 10.8327 13.5489 10.8327 13.3337V2.66699C10.8327 2.39085 11.0565 2.16699 11.3327 2.16699ZM4.83268 4.00033C4.83268 3.72418 5.05654 3.50033 5.33268 3.50033H8.66602C8.94216 3.50033 9.16602 3.72418 9.16602 4.00033C9.16602 4.27647 8.94216 4.50033 8.66602 4.50033H5.33268C5.05654 4.50033 4.83268 4.27647 4.83268 4.00033ZM3.49935 7.33366C3.49935 7.05752 3.72321 6.83366 3.99935 6.83366H8.66602C8.94216 6.83366 9.16602 7.05752 9.16602 7.33366C9.16602 7.6098 8.94216 7.83366 8.66602 7.83366H3.99935C3.72321 7.83366 3.49935 7.6098 3.49935 7.33366ZM2.16602 10.667C2.16602 10.3909 2.38987 10.167 2.66602 10.167H8.66602C8.94216 10.167 9.16602 10.3909 9.16602 10.667C9.16602 10.9431 8.94216 11.167 8.66602 11.167H2.66602C2.38987 11.167 2.16602 10.9431 2.16602 10.667Z"
          fill="#FAFAFA"
        />
      </svg>
    )
  },
)
AccedingIcon.displayName = 'AccedingIcon'

export default AccedingIcon
