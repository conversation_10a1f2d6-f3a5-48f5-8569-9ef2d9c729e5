import React, { ReactElement, forwardRef } from 'react'

interface CheckboxSuccessIconProps {
  className?: string
  onClick?: (e: any) => void
}

const CheckBoxSuccessIcon: React.FC<CheckboxSuccessIconProps> = forwardRef<SVGSVGElement, CheckboxSuccessIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
        <rect x="0.5" y="0.5" width="19" height="19" rx="1.5" fill="#23C470" stroke="#23C470" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.4117 5.77919C15.6717 6.00619 15.6985 6.40102 15.4715 6.66104L8.92386 14.161C8.80517 14.297 8.63351 14.375 8.45304 14.375C8.27257 14.375 8.10091 14.297 7.98222 14.161L5.36317 11.161C5.13616 10.901 5.16293 10.5062 5.42296 10.2792C5.68299 10.0522 6.07781 10.0789 6.30482 10.339L8.45304 12.7997L14.5298 5.83897C14.7568 5.57894 15.1517 5.55218 15.4117 5.77919Z"
          fill="#FAFAFA"
        />
      </svg>
    )
  },
)
CheckBoxSuccessIcon.displayName = 'CheckBoxSuccessIcon'

export default CheckBoxSuccessIcon
