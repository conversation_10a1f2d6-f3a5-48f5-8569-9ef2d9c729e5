import React, { ReactElement, forwardRef } from 'react'

interface ProjectDetailsProps {
  className?: string
  onClick?: (e: any) => void
}

const ProjectDetails: React.FC<ProjectDetailsProps> = forwardRef<SVGSVGElement, ProjectDetailsProps>(
  (props, ref): ReactElement => {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="24" overflow="hidden" ref={ref} {...props}>
        <g transform="translate(-104 -200)">
          <g>
            <g>
              <g>
                <g>
                  <path
                    d="M125.987 212.011C126.025 213.889 125.983 215.769 125.812 217.637 125.626 219.679 124.55 221.189 122.755 222.179 121.823 222.693 120.803 222.842 119.758 222.88 117.79 222.951 115.821 223.034 113.851 222.983 112.309 222.943 110.762 222.974 109.226 222.786 107.348 222.555 105.932 221.591 104.951 219.99 104.418 219.122 104.191 218.163 104.147 217.141 104.056 215.007 103.969 212.873 104.011 210.736 104.04 209.276 104.06 207.812 104.184 206.358 104.325 204.715 105.099 203.373 106.411 202.356 107.369 201.614 108.457 201.232 109.664 201.16 111.853 201.031 114.044 200.969 116.235 201.017 117.777 201.051 119.329 200.994 120.859 201.223 123.192 201.572 124.737 202.913 125.56 205.112 125.821 205.81 125.844 206.542 125.88 207.269 125.957 208.849 126.024 210.429 125.988 212.011ZM114.725 202.004C113.348 201.966 111.62 202.058 109.892 202.129 109.395 202.15 108.905 202.22 108.422 202.378 106.595 202.977 105.219 204.729 105.131 206.646 105.074 207.865 105.037 209.085 105.012 210.306 104.968 212.544 105.042 214.782 105.117 217.019 105.148 217.945 105.364 218.813 105.862 219.598 106.62 220.794 107.695 221.547 109.095 221.777 110.155 221.952 111.233 221.939 112.304 221.966 114.921 222.031 117.539 222.007 120.155 221.87 120.932 221.83 121.676 221.667 122.359 221.28 123.752 220.49 124.598 219.309 124.817 217.722 124.972 216.603 124.951 215.47 124.975 214.343 125.027 211.92 125.001 209.497 124.887 207.075 124.849 206.268 124.726 205.494 124.353 204.774 123.646 203.408 122.531 202.564 121.034 202.233 120.29 202.068 119.524 202.122 118.768 202.072 117.541 201.989 116.309 202.006 114.725 202.004Z"
                    stroke="#EEF3F9"
                    strokeWidth="0.293178"
                    strokeLinecap="butt"
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    strokeOpacity="1"
                    fill="#595959"
                    fillRule="nonzero"
                    fill-opacity="1"
                  />
                  <path
                    d="M116.43 208.999C115.282 208.999 114.136 209 112.989 208.999 112.51 208.998 112.394 208.885 112.392 208.413 112.391 208.061 112.384 207.709 112.406 207.359 112.428 207.006 112.547 206.899 112.897 206.898 115.256 206.895 117.614 206.895 119.973 206.898 120.342 206.898 120.442 207.011 120.445 207.389 120.447 207.75 120.446 208.11 120.445 208.471 120.443 208.901 120.352 208.997 119.925 208.998 118.759 209.001 117.594 208.999 116.428 208.999Z"
                    stroke="#EEF3F9"
                    strokeWidth="0.293178"
                    strokeLinecap="butt"
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    strokeOpacity="1"
                    fill="#595959"
                    fillRule="nonzero"
                    fill-opacity="1"
                  />
                  <path
                    d="M116.47 215C117.607 215 118.744 214.999 119.882 215 120.364 215 120.445 215.083 120.446 215.566 120.447 215.926 120.449 216.287 120.445 216.647 120.441 216.97 120.341 217.072 120.016 217.098 119.952 217.103 119.887 217.103 119.822 217.103 117.557 217.103 115.292 217.103 113.027 217.103 112.515 217.103 112.406 216.999 112.394 216.492 112.388 216.178 112.384 215.863 112.397 215.55 112.416 215.105 112.529 215.001 112.975 215 114.14 214.999 115.305 215 116.47 215Z"
                    stroke="#EEF3F9"
                    strokeWidth="0.293178"
                    strokeLinecap="butt"
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    strokeOpacity="1"
                    fill="#595959"
                    fillRule="nonzero"
                    fill-opacity="1"
                  />
                  <path
                    d="M116.443 213.015C115.27 213.015 114.096 213.015 112.922 213.015 112.499 213.015 112.401 212.923 112.395 212.496 112.39 212.126 112.386 211.755 112.41 211.387 112.431 211.061 112.525 210.986 112.848 210.985 115.242 210.984 117.636 210.984 120.029 210.985 120.36 210.985 120.443 211.069 120.446 211.393 120.45 211.782 120.447 212.17 120.446 212.558 120.444 212.938 120.37 213.015 119.993 213.015 118.81 213.017 117.627 213.016 116.445 213.015Z"
                    stroke="#EEF3F9"
                    strokeWidth="0.293178"
                    strokeLinecap="butt"
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    strokeOpacity="1"
                    fill="#595959"
                    fillRule="nonzero"
                    fill-opacity="1"
                  />
                  <path
                    d="M110.68 213.12C110.044 213.115 109.55 212.616 109.559 211.988 109.569 211.35 110.059 210.872 110.699 210.877 111.324 210.882 111.834 211.393 111.829 212.008 111.824 212.618 111.3 213.126 110.68 213.12Z"
                    stroke="#EEF3F9"
                    strokeWidth="0.293178"
                    strokeLinecap="butt"
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    strokeOpacity="1"
                    fill="#595959"
                    fillRule="nonzero"
                    fill-opacity="1"
                  />
                  <path
                    d="M110.689 209.067C110.057 209.068 109.56 208.575 109.56 207.947 109.56 207.319 110.058 206.826 110.693 206.827 111.313 206.827 111.832 207.342 111.829 207.951 111.826 208.566 111.317 209.065 110.689 209.067Z"
                    stroke="#EEF3F9"
                    strokeWidth="0.293178"
                    strokeLinecap="butt"
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    strokeOpacity="1"
                    fill="#595959"
                    fillRule="nonzero"
                    fill-opacity="1"
                  />
                  <path
                    d="M110.696 214.93C111.322 214.935 111.832 215.442 111.829 216.055 111.826 216.664 111.306 217.173 110.686 217.171 110.066 217.17 109.553 216.656 109.56 216.043 109.568 215.413 110.065 214.925 110.696 214.93Z"
                    stroke="#EEF3F9"
                    strokeWidth="0.293178"
                    strokeLinecap="butt"
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    strokeOpacity="1"
                    fill="#595959"
                    fillRule="nonzero"
                    fill-opacity="1"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    )
  },
)
ProjectDetails.displayName = 'ProjectDetails'

export default ProjectDetails
