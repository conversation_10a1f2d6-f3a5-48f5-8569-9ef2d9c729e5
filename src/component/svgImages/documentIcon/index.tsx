import React, { ReactElement, forwardRef } from 'react'

interface DocumentIconProps {
  className?: string
  onClick?: (e: any) => void
}

const DocumentIcon: React.FC<DocumentIconProps> = forwardRef<SVGSVGElement, DocumentIconProps>(
  (props, ref): ReactElement => {
    return (
      <svg
        width="15"
        height="15"
        viewBox="0 0 15 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        ref={ref}
        {...props}
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.29572 0.333252H7.70428C8.92945 0.333242 9.89987 0.333233 10.6593 0.435342C11.441 0.540427 12.0736 0.761839 12.5725 1.26075C12.7678 1.45601 12.7678 1.77259 12.5725 1.96785C12.3772 2.16312 12.0607 2.16312 11.8654 1.96785C11.5833 1.68571 11.1968 1.5166 10.5261 1.42642C9.84099 1.33431 8.93788 1.33325 7.66667 1.33325H6.33333C5.06212 1.33325 4.15901 1.33431 3.4739 1.42642C2.80317 1.5166 2.41674 1.68571 2.1346 1.96785C1.85246 2.24999 1.68335 2.63643 1.59317 3.30715C1.50106 3.99226 1.5 4.89537 1.5 6.16659V8.83325C1.5 10.1045 1.50106 11.0076 1.59317 11.6927C1.68335 12.3634 1.85246 12.7498 2.1346 13.032C2.41674 13.3141 2.80317 13.4832 3.4739 13.5734C4.15901 13.6655 5.06212 13.6666 6.33333 13.6666H7.66667C8.93788 13.6666 9.84099 13.6655 10.5261 13.5734C11.1968 13.4832 11.5833 13.3141 11.8654 13.032C12.3294 12.568 12.4698 11.8471 12.4943 10.1594C12.4983 9.88324 12.7253 9.66264 13.0014 9.66664C13.2776 9.67063 13.4981 9.89771 13.4942 10.1738C13.4707 11.7921 13.366 12.9456 12.5725 13.7391C12.0736 14.238 11.441 14.4594 10.6593 14.5645C9.89987 14.6666 8.92945 14.6666 7.70427 14.6666H6.29573C5.07055 14.6666 4.10013 14.6666 3.34065 14.5645C2.55904 14.4594 1.9264 14.238 1.4275 13.7391C0.928587 13.2402 0.707176 12.6075 0.60209 11.8259C0.499981 11.0665 0.49999 10.096 0.5 8.87086V6.12897C0.49999 4.9038 0.499981 3.93338 0.60209 3.1739C0.707176 2.39229 0.928587 1.75966 1.4275 1.26075C1.9264 0.761839 2.55904 0.540427 3.34065 0.435342C4.10013 0.333233 5.07055 0.333242 6.29572 0.333252ZM11.0754 4.19696C11.7826 3.48979 12.9291 3.48979 13.6363 4.19696C14.3435 4.90412 14.3435 6.05066 13.6363 6.75782L10.4661 9.92804C10.2936 10.1006 10.1769 10.2173 10.0462 10.3192C9.89248 10.4391 9.72612 10.5419 9.5501 10.6258C9.40048 10.6971 9.24391 10.7493 9.01248 10.8264L7.62339 11.2894C7.31671 11.3917 6.97858 11.3119 6.74999 11.0833C6.5214 10.8547 6.44158 10.5165 6.54381 10.2099L6.99751 8.84875C7.00066 8.83931 7.00376 8.82999 7.00683 8.8208C7.08394 8.58936 7.13611 8.43278 7.20742 8.28315C7.29131 8.10713 7.39412 7.94077 7.51404 7.78702C7.61598 7.65633 7.73269 7.53965 7.90519 7.3672C7.91206 7.36033 7.91901 7.35338 7.92606 7.34633L11.0754 4.19696ZM12.9292 4.90407C12.6125 4.58743 12.0992 4.58743 11.7825 4.90407L11.6613 5.02525C11.6679 5.04758 11.6755 5.07132 11.6841 5.09627C11.7468 5.27702 11.8658 5.5161 12.0915 5.74176C12.3172 5.96742 12.5562 6.08642 12.737 6.14913C12.7619 6.15779 12.7857 6.16533 12.808 6.17191L12.9292 6.05072C13.2458 5.73408 13.2458 5.2207 12.9292 4.90407ZM12.0443 6.93562C11.8324 6.8243 11.6035 6.66801 11.3844 6.44886C11.1652 6.22972 11.009 6.00089 10.8976 5.78897L8.63316 8.05344C8.43244 8.25416 8.36202 8.3258 8.30256 8.40204C8.2273 8.49852 8.16278 8.60291 8.11014 8.71337C8.06855 8.80065 8.03596 8.89568 7.9462 9.16497L7.67933 9.96557L7.86769 10.1539L8.66828 9.88706C8.93758 9.79729 9.0326 9.76471 9.11988 9.72311C9.23034 9.67047 9.33473 9.60595 9.43121 9.5307C9.50745 9.47123 9.57909 9.40081 9.77982 9.20009L12.0443 6.93562ZM3.83333 5.49992C3.83333 5.22378 4.05719 4.99992 4.33333 4.99992H8.66667C8.94281 4.99992 9.16667 5.22378 9.16667 5.49992C9.16667 5.77606 8.94281 5.99992 8.66667 5.99992H4.33333C4.05719 5.99992 3.83333 5.77606 3.83333 5.49992ZM3.83333 8.16659C3.83333 7.89044 4.05719 7.66659 4.33333 7.66659H6C6.27614 7.66659 6.5 7.89044 6.5 8.16659C6.5 8.44273 6.27614 8.66659 6 8.66659H4.33333C4.05719 8.66659 3.83333 8.44273 3.83333 8.16659ZM3.83333 10.8333C3.83333 10.5571 4.05719 10.3333 4.33333 10.3333H5.33333C5.60948 10.3333 5.83333 10.5571 5.83333 10.8333C5.83333 11.1094 5.60948 11.3333 5.33333 11.3333H4.33333C4.05719 11.3333 3.83333 11.1094 3.83333 10.8333Z"
          fill="#808080"
        />
      </svg>
    )
  },
)
DocumentIcon.displayName = 'AccedingIcon'

export default DocumentIcon
