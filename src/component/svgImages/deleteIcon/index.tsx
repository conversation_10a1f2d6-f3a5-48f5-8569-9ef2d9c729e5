import React, { ReactElement, forwardRef } from 'react'

interface DeleteIconProps {
  className?: string
  onClick?: (e: any) => void
  fill?: string
}

const DeleteIcon: React.FC<DeleteIconProps> = forwardRef<SVGSVGElement, DeleteIconProps>((props, ref): ReactElement => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props} ref={ref}>
      <path
        d="M8.00067 1.83398C7.34834 1.83398 6.79205 2.25067 6.5859 2.83394C6.49387 3.0943 6.20821 3.23076 5.94785 3.13874C5.68749 3.04671 5.55103 2.76105 5.64306 2.50069C5.98601 1.53038 6.91138 0.833984 8.00067 0.833984C9.08997 0.833984 10.0153 1.53038 10.3583 2.50069C10.4503 2.76105 10.3138 3.04671 10.0535 3.13874C9.79313 3.23076 9.50747 3.0943 9.41545 2.83394C9.20929 2.25067 8.653 1.83398 8.00067 1.83398Z"
        fill={`${props.fill ? props.fill : '#EA3323'}`}
      />
      <path
        d="M1.83398 4.00065C1.83398 3.72451 2.05784 3.50065 2.33398 3.50065H13.6674C13.9435 3.50065 14.1674 3.72451 14.1674 4.00065C14.1674 4.27679 13.9435 4.50065 13.6674 4.50065H2.33398C2.05784 4.50065 1.83398 4.27679 1.83398 4.00065Z"
        fill={`${props.fill ? props.fill : '#EA3323'}`}
      />
      <path
        d="M3.94404 5.63406C3.92567 5.35853 3.68742 5.15005 3.41189 5.16842C3.13636 5.18679 2.92789 5.42505 2.94626 5.70058L3.25522 10.3351C3.31222 11.1902 3.35826 11.881 3.46624 12.423C3.57851 12.9866 3.76945 13.4573 4.16385 13.8263C4.55825 14.1952 5.04061 14.3545 5.61038 14.429C6.15841 14.5007 6.8507 14.5007 7.70776 14.5007H8.29365C9.15071 14.5007 9.843 14.5007 10.391 14.429C10.9608 14.3545 11.4432 14.1952 11.8376 13.8263C12.232 13.4573 12.4229 12.9866 12.5352 12.423C12.6431 11.881 12.6892 11.1902 12.7462 10.3351L13.0552 5.70058C13.0735 5.42505 12.865 5.18679 12.5895 5.16842C12.314 5.15005 12.0757 5.35853 12.0574 5.63406L11.7507 10.2335C11.6908 11.132 11.6481 11.7572 11.5544 12.2277C11.4635 12.684 11.3366 12.9255 11.1544 13.096C10.9721 13.2665 10.7227 13.3771 10.2613 13.4374C9.78572 13.4996 9.15905 13.5007 8.25849 13.5007H7.74292C6.84235 13.5007 6.21568 13.4996 5.74008 13.4374C5.27875 13.3771 5.02931 13.2665 4.84703 13.096C4.66476 12.9255 4.53787 12.684 4.44697 12.2277C4.35326 11.7572 4.31057 11.132 4.25067 10.2335L3.94404 5.63406Z"
        fill={`${props.fill ? props.fill : '#EA3323'}`}
      />
      <path
        d="M6.28429 6.83647C6.55906 6.80899 6.80408 7.00946 6.83156 7.28423L7.16489 10.6176C7.19237 10.8923 6.9919 11.1374 6.71713 11.1648C6.44236 11.1923 6.19733 10.9918 6.16986 10.7171L5.83652 7.38374C5.80905 7.10896 6.00952 6.86394 6.28429 6.83647Z"
        fill={`${props.fill ? props.fill : '#EA3323'}`}
      />
      <path
        d="M10.1649 7.38374C10.1924 7.10896 9.9919 6.86394 9.71713 6.83647C9.44236 6.80899 9.19733 7.00946 9.16986 7.28423L8.83652 10.6176C8.80905 10.8923 9.00952 11.1374 9.28429 11.1648C9.55906 11.1923 9.80408 10.9918 9.83156 10.7171L10.1649 7.38374Z"
        fill={`${props.fill ? props.fill : '#EA3323'}`}
      />
    </svg>
  )
})
DeleteIcon.displayName = 'DeleteIcon'

export default DeleteIcon
