import React, { ReactElement, forwardRef } from 'react'

interface PlusIconProps {
  className?: string
  fill?: string
  color?: string
  onClick?: (e: any) => void
}

const PlusIcon: React.FC<PlusIconProps> = forwardRef<SVGSVGElement, PlusIconProps>((props, ref): ReactElement => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" ref={ref} {...props}>
      <path
        d="M7.49935 8.49993H4.16602C4.02435 8.49993 3.9056 8.45199 3.80977 8.35613C3.71393 8.26025 3.66602 8.14145 3.66602 7.99973C3.66602 7.85799 3.71393 7.73926 3.80977 7.64354C3.9056 7.54782 4.02435 7.49996 4.16602 7.49996H7.49935V4.16663C7.49935 4.02496 7.54728 3.90621 7.64315 3.81038C7.73903 3.71454 7.85783 3.66663 7.99955 3.66663C8.14128 3.66663 8.26001 3.71454 8.35573 3.81038C8.45145 3.90621 8.49932 4.02496 8.49932 4.16663V7.49996H11.8326C11.9743 7.49996 12.0931 7.54789 12.1889 7.64376C12.2847 7.73964 12.3327 7.85844 12.3327 8.00016C12.3327 8.14189 12.2847 8.26062 12.1889 8.35634C12.0931 8.45206 11.9743 8.49993 11.8326 8.49993H8.49932V11.8333C8.49932 11.9749 8.45138 12.0937 8.35552 12.1895C8.25964 12.2853 8.14084 12.3333 7.99912 12.3333C7.85738 12.3333 7.73865 12.2853 7.64293 12.1895C7.54721 12.0937 7.49935 11.9749 7.49935 11.8333V8.49993Z"
        fill={props.color ? props.color : '#444444'}
      />
    </svg>
  )
})
PlusIcon.displayName = 'PlusIcon'

export default PlusIcon
