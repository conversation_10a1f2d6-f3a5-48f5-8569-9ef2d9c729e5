@import '/styles/color.scss';

.heighLightFields {
  margin-top: 7px;
  border: 1px solid $LIGHT_200;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.addButton {
  background-color: #495e69 !important;
  border-radius: 4px 18px 18px 4px !important;
  gap: 6px;
  padding: 0 0 0 9px !important;
  color: #fff !important;
}

.textEditor {
  margin-bottom: 10px;
  > div {
    border-radius: 4px;
    padding-top: 2px;
  }
}
