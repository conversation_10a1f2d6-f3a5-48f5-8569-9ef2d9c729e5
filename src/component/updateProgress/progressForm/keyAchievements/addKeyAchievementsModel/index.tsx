import React, { useEffect, useMemo, useState } from 'react'
import { Modal, Box } from '@mui/material'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './AddKeyAchievementsModel.module.scss'
import { createPostAndUpdateKeyAchievementPayload } from '../helper'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import Textarea from '@/src/component/shared/textArea'
import TypographyField from '@/src/component/shared/typography'
import { GRAY_500 } from '@/src/constant/color'
import { useGetProjectToPhase } from '@/src/hooks/useMasterProjectToPhase'
import { useOptions } from '@/src/hooks/useOptions'
import { StageStatus, SubStage } from '@/src/redux/areaOfConcern/interface'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import { <PERSON><PERSON>eyAchievement } from '@/src/redux/keyAchievement/interface'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IStatus } from '@/src/redux/status/interface'
import { IProjects } from '@/src/services/projects/interface'
import { sortArrayByKeyWithTypeConversion, getValue, getUniqueArrayByKey } from '@/src/utils/arrayUtils'
import { predefinedStageOrder, getStageOrderIndex } from '@/src/utils/predecessorSuccessor'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'

import { errorToast, successToast } from '@/src/utils/toastUtils'

interface ModalProps {
  open: boolean
  onClose?: () => void
  onConfirmOpen?: () => void
  project?: IProjects
  editKeyAchievementIndex?: number
  keyAchievement?: any
  statuses: IStatus[]
}

const AddKeyAchievementsModel: React.FC<ModalProps> = ({
  open,
  onClose,
  project,
  editKeyAchievementIndex,
  keyAchievement,
  statuses,
}) => {
  //react-hook
  const [disable, setDisable] = useState(false)
  const [isStatusChanged, setIsStatusChanged] = useState(false)
  //custom-hook
  const { currentUser } = useAuthorization()
  const { keyAchievements, getKeyAchievementsApi, addKeyAchievementsApi, updateKeyAchievementsApi } =
    useKeyAchievement()
  const { currentPeriod } = useMasterPeriod()

  const router = useRouter()
  const currentStatus = statuses.find((item: IStatus) => `${item.id}` === router.query.slug)

  const phaseNotPresent =
    !currentStatus?.LookupProjectToPhase?.length || !currentStatus?.LookupProjectToPhase?.some((item) => item?.phase)

  const keyAchievementData: any = useMemo(() => {
    return keyAchievements.find((item: any) => item.id === editKeyAchievementIndex)
  }, [keyAchievements, editKeyAchievementIndex])

  const initialValues = useMemo(() => {
    return {
      // description: (editKeyAchievementIndex && keyAchievementData.phase) || '',
      key_achievements_last_period: (editKeyAchievementIndex && keyAchievementData.key_achievements_last_period) || '',
      plans_next_period: (editKeyAchievementIndex && keyAchievementData.plans_next_period) || '',
      lookup_project_to_phase_id: (editKeyAchievementIndex && keyAchievementData?.LookupProjectToPhase?.id) || '',
      stage_status:
        (editKeyAchievementIndex && keyAchievementData?.MasterProjectStageStatus?.project_stage_status) || '',
      sub_stage: (editKeyAchievementIndex && keyAchievementData?.MasterProjectSubStage?.project_sub_stage) || '',
      master_project_stage_status_id:
        (editKeyAchievementIndex && keyAchievementData.master_project_stage_status_id) || 0,
      master_project_sub_stage_id: (editKeyAchievementIndex && keyAchievementData.master_project_sub_stage_id) || 0,
    }
  }, [editKeyAchievementIndex, keyAchievementData])

  useEffect(() => {
    formik.resetForm({ values: initialValues })
  }, [initialValues])

  const onSubmit = async (values: any) => {
    setDisable(true)
    try {
      const todayDate = new Date()
      const payload: IKeyAchievement = createPostAndUpdateKeyAchievementPayload(
        values,
        project?.project_name,
        todayDate,
        null,
        currentPeriod,
      )

      let response: Record<string, any>

      if (editKeyAchievementIndex) {
        response = await updateKeyAchievementsApi({
          id: editKeyAchievementIndex,
          data: payload,
        })
      } else {
        const keyAchievementSort = keyAchievement
          ?.slice()
          .sort((a: any, b: any) => a.key_achievement_sorting_order - b.key_achievement_sorting_order)

        const sort = keyAchievementSort.length
          ? Number(keyAchievementSort[keyAchievementSort.length - 1].key_achievement_sorting_order) + 1
          : 1
        const newPayload: IKeyAchievement = createPostAndUpdateKeyAchievementPayload(
          values,
          project?.project_name,
          todayDate,
          sort,
          currentPeriod,
        )
        response = await addKeyAchievementsApi(newPayload)
      }

      if (response?.payload?.success) {
        if (currentPeriod) {
          await getKeyAchievementsApi({ period: currentPeriod })
        }
        formik.resetForm()
        onClose && onClose()
        successToast(response?.payload?.message || 'Key Achievement added successfully')
      } else {
        errorToast(response?.payload?.response?.data?.message || 'An unexpected error occurred. Please try again.')
      }
    } catch (error) {
      console.error('Error occurred:', error)
      errorToast('An unexpected error occurred. Please try again.')
    } finally {
      setDisable(false)
    }
  }

  const formik: any = useFormik({
    initialValues,
    enableReinitialize: true,
    onSubmit,
  })

  // Update form values and handle status change if `isStatusChanged` is true
  const handleStatusUpdate = (phaseStatusOptions: any[]) => {
    // formik.setFieldValue('stage_status', phaseStatusOptions[0]?.stage_status)
    // formik.setFieldValue('sub_stage', phaseStatusOptions[0]?.sub_stage)
    setIsStatusChanged(false)
  }

  // TODO: Comment old logic
  // const { phases, stage_status, sub_stage } = useOptions(
  //   statuses,
  //   formik?.values?.phase,
  //   currentUser,
  //   handleStatusUpdate,
  //   isStatusChanged,
  // )

  const getPhaseStageAssociations = (records: any[]) => {
    const phaseStageMap = new Map()

    records.forEach((record: any) => {
      const phaseArray = record.phases // now an array of objects
      const stageStatusValue = record.stage_status

      const subStageValue =
        typeof record.sub_stage?.project_sub_stage === 'string' && record.sub_stage?.project_sub_stage.trim() !== ''
          ? record.sub_stage
          : null

      // Skip invalid stage_status
      if (
        !stageStatusValue ||
        typeof stageStatusValue?.project_stage_status !== 'string' ||
        stageStatusValue?.project_stage_status.trim() === ''
      ) {
        return
      }

      const stageInfo = {
        stageStatus: stageStatusValue,
        subStage: subStageValue,
      }

      const stageInfoString = JSON.stringify(stageInfo)

      // Process phase array of objects
      if (Array.isArray(phaseArray) && phaseArray.length > 0) {
        phaseArray.forEach((phaseObj: any) => {
          // if (!phaseObj?.phase) return // skip if missing phase name

          const phaseKey = JSON.stringify(phaseObj) // use entire object as key

          if (!phaseStageMap.has(phaseKey)) {
            phaseStageMap.set(phaseKey, new Set())
          }

          phaseStageMap.get(phaseKey).add(stageInfoString)
        })
      }
    })

    // Convert Map to desired result
    const result: Array<{ phases: any; stages: any[] }> = []
    for (const [phaseKey, stagesSet] of phaseStageMap.entries()) {
      result.push({
        phases: JSON.parse(phaseKey),
        stages: Array.from(stagesSet).map((s: any) => JSON.parse(s)),
      })
    }

    return result
  }

  const { phaseWithStageStatus, phaseOptions } = useMemo(() => {
    const statusesByPermission = statuses.filter((item: IStatus) =>
      getStageStatusByPermission(currentUser.role).includes(item.stage_status),
    )
    const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
      statusesByPermission.map((status) => ({
        ...status,
        project_status_sorting_order: Number(status.project_status_sorting_order),
      })),
      'project_status_sorting_order',
      true,
    )

    const phaseWithStageStatuses = sortedStatusesByPermission.map((item: any) => {
      return {
        phases: item.LookupProjectToPhase,
        stage_status: item.MasterProjectStageStatus,
        sub_stage: item.MasterProjectSubStage,
      }
    })

    const phaseStageAssociations: any = getPhaseStageAssociations(phaseWithStageStatuses)

    const uniquePhases =
      phaseStageAssociations?.length > 0
        ? phaseStageAssociations?.map((item: any) => {
            return item?.phases
          })
        : []

    const phaseOptions =
      uniquePhases?.length > 0
        ? uniquePhases
            ?.map((phase: any) => {
              return {
                label: phase?.phase,
                value: phase?.id,
              }
            })
            ?.filter((item: any) => item.value !== null && item.label !== null)
        : []

    return { phaseWithStageStatus: phaseStageAssociations, phaseOptions }
  }, [formik.values?.lookup_project_to_phase_id])

  // useMemo(() => {
  //   formik.setFieldValue(
  //     'lookup_project_to_phase_id',
  //     phaseOptions?.filter((item: any) => item?.value === formik.values?.lookup_project_to_phase_id)?.length > 0
  //       ? formik.values?.lookup_project_to_phase_id
  //       : null,
  //   )

  //   if (formik.values?.lookup_project_to_phase_id || formik.values?.lookup_project_to_phase_id === null) {
  //     formik.setFieldValue('stage_status', null)
  //   }
  // }, [formik.values?.lookup_project_to_phase_id])

  const { stageStatusOptions, subStageOptions } = useMemo(() => {
    let phase = getValue(phaseOptions, formik.values.lookup_project_to_phase_id)

    if (phaseNotPresent) {
      const uniqueStageStatuses = new Set<any>()
      const uniqueSubStages = new Set<any>()
      statuses
        ?.filter((res) => res?.LookupProjectToPhase?.length === 0 || res?.LookupProjectToPhase?.find((fi) => !fi.phase))
        ?.forEach((item: IStatus) => {
          if (item.MasterProjectStageStatus) {
            uniqueStageStatuses.add(item.MasterProjectStageStatus)
          }
          if (item.MasterProjectSubStage) {
            uniqueSubStages.add(item.MasterProjectSubStage)
          }
        })

      const stageStatusOptionsArray = Array.from(uniqueStageStatuses)
        .map((status: any) => ({
          label: status.project_stage_status,
          value: status.id,
        }))
        .sort((a, b) => {
          const stageOrderA = getStageOrderIndex(a.label, predefinedStageOrder)
          const stageOrderB = getStageOrderIndex(b.label, predefinedStageOrder)
          return stageOrderA - stageOrderB
        })

      const subStageOptionsArray = Array.from(uniqueSubStages).map((subStage: any) => ({
        label: subStage.project_sub_stage,
        value: subStage.id,
      }))

      const stageStatusOptions = getUniqueArrayByKey(stageStatusOptionsArray, 'value')

      const subStageOptions = getUniqueArrayByKey(subStageOptionsArray, 'value')

      return {
        stageStatusOptions: stageStatusOptions,
        subStageOptions: subStageOptions,
      }
    }

    const filteredPhaseWithStageStatus =
      phaseWithStageStatus?.length > 0
        ? phaseWithStageStatus?.find((item: any) => {
            return item?.phases?.id === phase?.value
          })
        : undefined // Changed to undefined for clarity when not found

    const stageStatuses = new Set<StageStatus>()
    const subStages = new Set<SubStage>()

    if (filteredPhaseWithStageStatus?.stages) {
      filteredPhaseWithStageStatus.stages.forEach((item: any) => {
        if (item?.stageStatus !== null && item?.stageStatus !== undefined) {
          stageStatuses.add(item.stageStatus)
        }
        if (item?.subStage !== null && item?.subStage !== undefined) {
          subStages.add(item.subStage)
        }
      })
    }

    // Convert Sets back to arrays of objects with 'label' and 'value'
    const stageStatusOptionsArray = Array.from(stageStatuses).map((status) => {
      return {
        label: status.project_stage_status,
        value: status.id,
      }
    })

    const subStageOptionsArray = Array.from(subStages).map((subStage) => {
      return {
        label: subStage.project_sub_stage,
        value: subStage.id,
      }
    })

    const stageStatusOptions = getUniqueArrayByKey(stageStatusOptionsArray, 'value')

    const subStageOptions = getUniqueArrayByKey(subStageOptionsArray, 'value')

    return { stageStatusOptions, subStageOptions }
  }, [phaseWithStageStatus, formik.values?.lookup_project_to_phase_id])

  return (
    <Modal
      className={styles.model}
      open={open}
      onClose={() => {
        formik.resetForm()
        onClose && onClose()
      }}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'calc(100% - 4rem)',
          maxWidth: '1288px',
          bgcolor: 'background.paper',
          borderRadius: '12px',
          pt: '20px',
          px: '20px',
          pb: '20px',
          zIndex: 1,
        }}
      >
        <div className={styles.header}>
          <div className={styles.rightSection}>
            <TypographyField variant={'bodySemiBold'} text={'Add Key Achievements'} />
            {/* <div className={styles.divider}></div> */}
            {/* <TypographyField sx={"caption"} text={"Infrastructure"} /> */}
          </div>
          <TypographyField
            variant={'bodySemiBold'}
            style={{ color: GRAY_500 }}
            text={project?.project_name as string}
          />
        </div>
        <form onSubmit={formik.handleSubmit}>
          {/* <Textarea
            name="description"
            placeholder="Phase/Package/Stage"
            labelText={'Phase/Package/Stage'}
            onChange={formik.handleChange}
            value={formik.values.description}
            onBlur={formik.handleBlur}
          /> */}

          <div className={styles.comboBoxContainer}>
            <ComboBox
              options={phaseNotPresent ? [{ label: 'N/A', value: null }] : phaseOptions}
              labelText={`Phase/Package${!phaseNotPresent ? ' *' : ''}`}
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              placeholder="Type of search..."
              value={
                formik.values?.lookup_project_to_phase_id
                  ? getValue(phaseOptions, formik.values.lookup_project_to_phase_id)
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  lookup_project_to_phase_id: val?.value || '',
                  master_project_stage_status_id: null,
                  master_project_sub_stage_id: null,
                })
                setIsStatusChanged(true)
              }}
            />
            <ComboBox
              options={stageStatusOptions}
              labelText={'Stage Status *'}
              placeholder="Type of search..."
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              value={
                formik.values?.master_project_stage_status_id
                  ? !!getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)
                    ? getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)
                    : { label: formik.values?.stage_status, value: formik.values?.master_project_stage_status_id }
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  master_project_stage_status_id: val?.value || null,
                  master_project_sub_stage_id: null,
                })
              }}
              isCustomSorting={true}
            />

            {(getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)?.label ||
              formik.values?.stage_status) === 'Design' && (
              <ComboBox
                options={subStageOptions}
                labelText={'Sub Stage'}
                // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
                placeholder="Type of search..."
                value={
                  formik.values?.master_project_sub_stage_id
                    ? !!getValue(subStageOptions, formik.values?.master_project_sub_stage_id)
                      ? getValue(subStageOptions, formik.values?.master_project_sub_stage_id)
                      : { label: formik.values?.sub_stage, value: formik.values?.master_project_sub_stage_id }
                    : null
                }
                clearIcon={true}
                onChange={(val) => {
                  formik.setValues({
                    ...formik.values,
                    master_project_sub_stage_id: val?.value || '',
                  })
                }}
                isCustomSorting={true}
              />
            )}
          </div>

          <Textarea
            name="key_achievements_last_period"
            placeholder="Type Something..."
            labelText={'Key  Achievements (Last Period) *'}
            onChange={formik.handleChange}
            value={formik.values.key_achievements_last_period}
            onBlur={formik.handleBlur}
          />
          <Textarea
            name="plans_next_period"
            placeholder="Type Something..."
            labelText={'Plans (Next Period) *'}
            onChange={formik.handleChange}
            value={formik.values?.plans_next_period}
            onBlur={formik.handleBlur}
          />

          <div className={styles.buttons}>
            <Button
              color="secondary"
              onClick={() => {
                formik.resetForm()
                onClose && onClose()
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                disable ||
                !formik.dirty ||
                formik.isSubmitting ||
                !formik.values.key_achievements_last_period ||
                !formik.values.plans_next_period ||
                (!phaseNotPresent && !formik.values.lookup_project_to_phase_id) ||
                !formik.values.master_project_stage_status_id
              }
            >
              {`${editKeyAchievementIndex ? 'Edit Key Achievement' : 'Add Key Achievement'}`}
            </Button>
          </div>
        </form>
      </Box>
    </Modal>
  )
}

export default AddKeyAchievementsModel
