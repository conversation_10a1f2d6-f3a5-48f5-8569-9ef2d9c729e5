export class KeyAchievementValidation {
  static async StageStatus(values: any, isCell: boolean = false): Promise<boolean> {
    if (values?.stage_status === null || !values?.stage_status) {
      return true
    }
    return false
  }

  static async SubStageStatus(values: any, isCell: boolean = false): Promise<boolean> {
    if (values?.stage_status === 'Design' && (values?.sub_stage === null || !values?.stage_status)) {
      return true
    }
    return false
  }
}
