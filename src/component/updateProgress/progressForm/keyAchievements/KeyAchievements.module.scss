@import '/styles/color.scss';

.highlightFields {
  margin-top: 7px;
  margin-bottom: 7px;
  border: 1px solid $LIGHT_200;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.datePickers {
  display: flex;
  gap: 10px;
  width: 600px;
}
.addButton {
  background-color: #495e69 !important;
  border-radius: 4px 18px 18px 4px !important;
  gap: 6px;
  padding: 0 0 0 9px !important;
  color: #fff !important;
}
.actionButtons {
  display: flex;
  gap: 10px;
}
.editRowIcon {
  cursor: pointer;
}
.table {
  position: static;
  overflow: auto;
}
.addButton {
  background-color: #495e69 !important;
  border-radius: 6px 18px 18px 6px !important;
  gap: 6px;
  padding: 0 0 0 9px !important;
  color: #fff !important;
}
