export const fetchKeyAchievements = async (getKeyAchievementsApi: any, currentPeriod: any, projectName: any) => {
  const response = await getKeyAchievementsApi({ period: currentPeriod, project_name: projectName as string })
  return response.payload.success ? response : null
}

export const isValueBetween = (value: any, min: any, max: any) => {
  return Number(value) >= Number(min) && Number(value) <= Number(max)
}

export const reorderKeyAchievements = (keyAchievements: any, dragItem: any, dropItem: any) => {
  const min = Math.min(dragItem.key_achievement_sorting_order, dropItem.key_achievement_sorting_order)
  const max = Math.max(dragItem.key_achievement_sorting_order, dropItem.key_achievement_sorting_order)

  const filteredAndSortedData = keyAchievements
    .filter((item: any) => isValueBetween(Number(item.key_achievement_sorting_order), min, max))
    .sort((a: any, b: any) => Number(a.key_achievement_sorting_order) - Number(b.key_achievement_sorting_order))

  const findFirstValue = filteredAndSortedData.find(
    (item: any) => item.key_achievement_sorting_order === dragItem.key_achievement_sorting_order,
  )
  const findFirstPlace = filteredAndSortedData.findIndex(
    (item: any) => item.key_achievement_sorting_order === dropItem.key_achievement_sorting_order,
  )

  let arrangedData = [...filteredAndSortedData]

  if (findFirstValue && findFirstPlace !== -1) {
    arrangedData = arrangedData.filter(
      (item) => item?.key_achievement_sorting_order !== dragItem?.key_achievement_sorting_order,
    )
    arrangedData.splice(findFirstPlace, 0, findFirstValue)
  }

  return arrangedData.map((item, index) => ({
    ...item,
    key_achievement_sorting_order: min + index,
  }))
}

export const createPostAndUpdateKeyAchievementPayload = (
  values: any,
  projectName: any,
  todayDate: Date,
  sort: any = null,
  currentPeriod: any,
) => {
  return {
    // phase: values?.description || null,
    lookup_project_to_phase_id: values?.lookup_project_to_phase_id || null,
    key_achievements_last_period: values?.key_achievements_last_period || null,
    plans_next_period: values?.plans_next_period || null,
    period: currentPeriod,
    project_name: projectName,
    last_updated: todayDate,
    master_project_stage_status_id: values?.master_project_stage_status_id || null,
    master_project_sub_stage_id: values?.master_project_sub_stage_id || null,
    ...(sort !== null && { key_achievement_sorting_order: sort }), // Add key_achievement_sorting_order only if sort is provided
  }
}

export const getKeyAchievementsTableData = (array: any) => {
  return array
    ?.map((data: any) => {
      return {
        ...data,
        stage_status: data.MasterProjectStageStatus?.project_stage_status || null,
        sub_stage: data.MasterProjectSubStage?.project_sub_stage || null,
      }
    })
    .sort(
      (a: any, b: any) => Number(a.key_achievement_sorting_order ?? 0) - Number(b.key_achievement_sorting_order ?? 0),
    )
}
