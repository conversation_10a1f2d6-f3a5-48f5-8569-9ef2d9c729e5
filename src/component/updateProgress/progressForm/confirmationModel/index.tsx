import React from 'react'
import styles from './confirmationModel.module.scss'
import Button from '@/src/component/shared/button'

interface ConfirmationModelProps {
  message: string
  handleConfirm: () => void
  onClose: () => void
}

const ConfirmationModel: React.FC<ConfirmationModelProps> = ({ message, handleConfirm, onClose }) => {
  return (
    <>
      <div className={styles.title} id="confirmation-modal-title">
        {message || 'Are you sure you want to proceed?'}
      </div>
      <div className={styles.actionButtons}>
        <Button onClick={handleConfirm}>Yes</Button>
        <Button color="secondary" onClick={onClose}>
          No
        </Button>
      </div>
    </>
  )
}

export default ConfirmationModel
