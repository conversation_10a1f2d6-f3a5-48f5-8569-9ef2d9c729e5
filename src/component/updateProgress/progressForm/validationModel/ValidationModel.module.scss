@import '/styles/color.scss';

.container {
  border-radius: 12px;
  animation: fadeIn 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  max-width: 700px; /* Ensure modal doesn't stretch too wide */
  width: 100%;
  background: $WHITE;
  z-index: 10000; /* Ensure modal content appears above overlay */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Close button */
.closeIcon {
  cursor: pointer;
  position: absolute;
  top: 0px;
  right: 0px;
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: scale(1.1);
  }
}

/* Title */
.title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
  color: $DARK;
}

/* Message container */
.messageContainer {
  padding-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  max-height: 300px; /* Limit height for long lists */
}

/* Each message box */
.messageBox {
  display: flex;
  align-items: center;
  background: #fff6f6;
  border: 1px solid $ERROR;
  padding: 12px;
  border-radius: 8px;
  gap: 12px;
  word-wrap: break-word; /* Ensure long text doesn't overflow */
}

/* Warning Icon */
.warningIcon {
  color: $ERROR;
  font-size: 20px; /* Increased size for visibility */
}

/* Fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
