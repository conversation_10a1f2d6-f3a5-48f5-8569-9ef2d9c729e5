@import '/styles/color.scss';

.headerContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  .header {
    white-space: nowrap;
  }
  .divider {
    width: 100%;
    height: 1px;
    background-color: $LIGHT_200;
  }
}
.riskDetails {
  display: flex;
  align-items: center;
}
.avatarContainer {
  display: flex;
  align-items: center;
  gap: 2.5px;
}
.badge {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
  min-width: 74px;
  height: 20px;
  color: $BLACK !important;
}
.descriptionText {
  max-width: 273px;
  width: 100%;
}
.actionButtons {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.low {
  background-color: #23c370;
  color: $WHITE !important;
}

.medium {
  background-color: #ff8c00;
  color: #212121 !important;
}

.high {
  background-color: rgba(255, 27, 34, 0.75);
  color: $WHITE !important;
}
.closed {
  background-color: #b7b7b7ff;
  color: #212121 !important;
}

.addButton {
  background-color: #495e69 !important;
  border-radius: 4px 18px 18px 4px !important;
  gap: 6px;
  padding: 0 0 0 9px !important;
  color: #fff !important;
}

.actionButtons {
  display: flex;
  gap: 10px;
}
.table {
  position: static;
  overflow: auto;
}
