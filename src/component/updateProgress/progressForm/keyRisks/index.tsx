import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { arrayMove } from '@dnd-kit/sortable'
import { WarningAmberOutlined } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { toast } from 'sonner'
import AddKeyRiskModel from './addKeyRiskModel'
import { RATING } from './constant'
import { getKeyriskTableData } from './helper'
import styles from './KeyRisk.module.scss'
import { KeyRiskValidation } from './validation'
import ValidationModel from '../validationModel'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import DragCell from '@/src/component/customCells/dragCell'
import PulseButton from '@/src/component/shared/button/pulseButton'
import LineClampWithTooltip from '@/src/component/shared/lineClampWithTooltip'
import PulseModel from '@/src/component/shared/pulseModel'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { singleAssociationValueSorting } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { IAreaOfConcern } from '@/src/redux/areaOfConcern/interface'
import useAreaOfConcern from '@/src/redux/areaOfConcern/useAreaOfConcern'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IProjects } from '@/src/services/projects/interface'
import {
  convertAndSortData,
  convertMultiSelectOption,
  getValueByLabel,
  populateDropdownOptions,
  prepareDropdownOptions,
} from '@/src/utils/arrayUtils'
import { formatDMYDate, payloadDateFormate } from '@/src/utils/dateUtils'
import { sortAreaOfConcernsWithRating } from '@/src/utils/keyRisk/reorderAreaOfConcerns'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const KeyRisks = ({ project, edit }: { project?: IProjects; edit: boolean }) => {
  const { currentUser } = useAuthorization()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const [isModelOpen, setIsModelOpen] = useState(false)
  const [editKeyRisksIndex, setEditKeyRisksIndex] = useState<number | null>()
  const {
    areaOfConcerns,
    masterRating,
    getMasterRastingListApi,
    getAreaOfConcernsApi,
    deleteAreaOfConcernApi,
    updateAreaOfConcernApi,
    sortAreaOfConcernsApi,
  } = useAreaOfConcern()
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState<string[]>([])

  const handleOpenModel = useCallback(() => setIsModelOpen(true), [])
  const handleCloseModel = useCallback(() => {
    setIsModelOpen(false)
    setEditKeyRisksIndex(null)
  }, [])

  const fetchData = async () => {
    try {
      await getMasterRastingListApi()
    } catch (error) {
      console.error('Error fetching data:', error)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleDelete = async (id: number) => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, { icon: <WarningAmberOutlined /> })
      return
    }

    const res: Record<string, any> = await deleteAreaOfConcernApi(id)
    if (!res?.payload?.success) {
      errorToast(res?.payload?.response?.data?.message || 'Failed')
      return
    } else {
      successToast(res?.payload?.message)
    }

    const getResponse: Record<string, any> = await getAreaOfConcernsApi({ period: currentPeriod })
    if (getResponse?.payload?.success) setDeleteModel(null)
  }

  const [filteredAreaOfConcern, setFilteredAreaOfConcern] = useState<IAreaOfConcern[]>([])

  useEffect(() => {
    const filtered = sortAreaOfConcernsWithRating(
      getKeyriskTableData(areaOfConcerns.filter((item: IAreaOfConcern) => item.project_name === project?.project_name)),
      // ?.map((item) => {
      //   return { ...item, rating: item?.MasterRating?.rating }
      // }),
    )
    setFilteredAreaOfConcern(filtered)
  }, [areaOfConcerns, project?.project_name])

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const statuesKey: any = {
    rating: 'master_rating_id',
    risk_probability: 'master_risk_probability_id',
  }

  const onCellUpdate = async (cell: any, newValue: any, row: any) => {
    try {
      const { id, ...rowData } = row
      rowData[cell.columnId] = newValue
      const keyName =
        cell.columnId === 'rating' || cell.columnId === 'risk_probability' ? statuesKey[cell.columnId] : cell.columnId
      const forecasted_closure_date = payloadDateFormate(rowData.forecasted_closure_date) || null //TODO : Confirm
      const payload = {
        ...rowData,
        [keyName]: newValue,
        period: rowData?.period,
        last_updated: new Date(),
        forecasted_closure_date: forecasted_closure_date ? forecasted_closure_date : null,
        key_risk_sorting_order: rowData.key_risk_sorting_order,
      }

      // Validate Forecasted Closure Date

      const isStageStatusValidDate = await KeyRiskValidation.StageStatus(payload, true)
      if (isStageStatusValidDate) {
        setIsValidationModel(true)
        setValidationMessage(['Stage status must be filled for this record.'])
        return true
      }

      const isSubStageStatusValidDate = await KeyRiskValidation.SubStageStatus(payload, true)
      if (isSubStageStatusValidDate) {
        setIsValidationModel(true)
        setValidationMessage(['Sub Stage status must be filled for this record.'])
        return true
      }

      const isForecastedValidDate = await KeyRiskValidation.ForecastedClosureDate(payload, true)
      if (isForecastedValidDate) {
        setIsValidationModel(true)
        setValidationMessage(["Forecasted Closure Date must be greater than today if the risk rating is not 'Closed.'"])
        return true
      }

      delete payload.updated_by
      delete payload.LookupProjectToPhase
      delete payload.MasterProjectStageStatus
      delete payload.MasterProjectSubStage
      delete payload.sub_stage
      delete payload.stage_status
      delete payload.MasterRating
      delete payload.MasterRiskProbability
      delete payload.MasterRiskCategory
      delete payload.MasterRiskDiscipline
      delete payload.rating
      delete payload.risk_probability
      delete payload.MasterRiskOwner
      delete payload.phase

      // Update the area of concern
      const response: Record<string, any> = await updateAreaOfConcernApi({ id, data: payload })

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || 'Failed to update key risk')
        return false
      }

      // Fetch updated data
      const getResponse: Record<string, any> = await getAreaOfConcernsApi({ period: currentPeriod })
      return getResponse?.payload?.success ?? false
    } catch (error) {
      console.error('Error while updating cell of key achievement:', error)
      return false
    }
  }

  const ratingCell = (row: any) => {
    return (
      <div className={styles.riskDetails}>
        {row?.row.original?.MasterRating?.rating && (
          <TypographyField
            className={`${styles.badge} ${styles[row?.row.original.MasterRating.rating.toLowerCase()] || ''}`}
            variant="caption"
            text={row?.row.original.MasterRating.rating}
          />
        )}
      </div>
    )
  }

  const probabilityCell = (row: any) => {
    return (
      <div className={styles.riskDetails}>
        {row?.row.original?.MasterRiskProbability?.rating && (
          <TypographyField
            className={`${styles.badge} ${styles[row?.row.original.MasterRiskProbability.rating.toLowerCase()] || ''}`}
            variant="caption"
            text={row?.row.original.MasterRiskProbability.rating}
          />
        )}
      </div>
    )
  }

  const forecastedClosureDateCell = (row: any) => (
    <TypographyField
      variant={'captionSemiBold'}
      text={row?.row.original.forecasted_closure_date ? formatDMYDate(row?.row.original.forecasted_closure_date) : '-'}
    />
  )

  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    const cloneTableData: any[] = JSON.parse(JSON.stringify([...filteredAreaOfConcern]))

    // Find drag and drop indices
    const dragItem = cloneTableData.findIndex((item: any) => item.id.toString() === dragId.toString())
    const dropItem = cloneTableData.findIndex((item: any) => item.id.toString() === dropId.toString())

    // Move the item
    const newItems = arrayMove(cloneTableData, dragItem, dropItem)

    // Update sorting order in payload
    const newPayload = newItems.map((item: any, idx: number) => ({
      id: item.id.toString(),
      key_risk_sorting_order: idx,
    }))

    setFilteredAreaOfConcern(newItems)

    const res: any = await sortAreaOfConcernsApi({ period: currentPeriod, keyRisks: newPayload })
    // const res: any = await sortAreaOfConcernsApi({ period: currentPeriod, areaOfConcerns: newPayload })

    if (res?.payload.success) {
      const response: Record<string, any> = await getAreaOfConcernsApi({
        period: currentPeriod,
        project_name: project?.project_name as string,
      })
      return response.payload.success ? response : null
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'dragCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'left',
    },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }) => (
        <>
          {edit && (
            <div className={styles.actionButtons}>
              <EditIcon
                className={styles.editRowIcon}
                onClick={() => {
                  if (!isEditForUser) {
                    toast(`The current reporting period is locked`, {
                      icon: <WarningAmberOutlined />,
                    })
                  } else {
                    setIsModelOpen(true)
                    setEditKeyRisksIndex(row.id as any)
                  }
                }}
              />
              <DeleteIcon className={styles.editRowIcon} onClick={() => setDeleteModel(row.id as any)} />
            </div>
          )}
        </>
      ),
      size: 70,
    },
    { accessorKey: 'key_risk_sorting_order', header: 'ID', size: 90, visible: false },
    {
      accessorKey: 'phase',
      header: 'Phase',
      size: 150,
      childAccessorKey: ['LookupProjectToPhase'],
      filterType: 'wildcard-single-association',
      // isEditableCell: edit,
      // editableType: 'textArea',
      // TODO : error from backend when save for below field
      // editableType: 'richTextEditor',
      // cell: ({ row }: { row: any }) => {
      //   return <RichTextEditor value={row?.original?.phase} isEdit={false} isGrid={true} />
      // },
      // onEditCell: (cell, newValue, row) => {
      //   return onCellUpdate(cell, newValue, row)
      // },
      sortingFn: (...data) => singleAssociationValueSorting(...data, 'Single phase'),
      require: false,
      cell: ({ row }: { row: any }) => {
        return <div>{row.original?.LookupProjectToPhase?.phase || '-'}</div>
      },
    },
    {
      accessorKey: 'stage_status',
      header: 'Stage Status',
      filterType: 'projectStatus',
      size: 150,
      require: false,
      cell: ({ row }: { row: any }) => {
        return <div>{row.original?.MasterProjectStageStatus?.project_stage_status || '-'}</div>
      },
    },
    {
      accessorKey: 'sub_stage',
      header: 'Sub Stage',
      size: 150,
      require: false,
      cell: ({ row }: { row: any }) => {
        return <div>{row.original?.MasterProjectSubStage?.project_sub_stage || '-'}</div>
      },
    },
    {
      accessorKey: 'description',
      header: 'Description',
      size: 350,
      isEditableCell: edit,
      editableType: 'textArea',
      // editableType: 'richTextEditor',
      // cell: ({ row }: { row: any }) => {
      //   return <RichTextEditor value={row?.original?.description} isEdit={false} isGrid={true} />
      // },
      cell: ({ row }) => {
        const val = row.original.description
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
      onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
      require: true,
    },
    {
      accessorKey: 'rating',
      header: 'Rating',
      size: 140,
      filterType: 'list',
      align: 'center',
      listOption: convertAndSortData(
        convertMultiSelectOption(
          prepareDropdownOptions(masterRating, 'rating')?.map((item) => item?.label),
          'rating',
        ),
      ),
      isEditableCell: edit,
      isSaveConfirmationRequired: true,
      editableType: 'dropDown',
      editOption: populateDropdownOptions(masterRating, 'rating').filter(
        (option): option is string => typeof option === 'string',
      ),
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, getValueByLabel(prepareDropdownOptions(masterRating, 'rating'), newValue).value, row)
      },
      cell: ratingCell,
      require: true,
    },
    {
      accessorKey: 'risk_probability',
      header: 'Probability ',
      size: 140,
      filterType: 'list',
      listOption: convertAndSortData(
        convertMultiSelectOption(
          prepareDropdownOptions(masterRating, 'rating')?.map((item) => item?.label),
          'rating',
        ),
      ),
      // listOption: convertAndSortData(convertMultiSelectOption(['High', 'Low', 'Medium'])),
      isEditableCell: edit,
      isSaveConfirmationRequired: true,
      align: 'center',
      editableType: 'dropDown',
      editOption: ['Low', 'Medium', 'High', 'Closed'],
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, getValueByLabel(prepareDropdownOptions(masterRating, 'rating'), newValue).value, row)
      },
      cell: probabilityCell,
      require: true,
    },
    {
      accessorKey: 'mitigation_measures',
      header: 'Mitigation Measures',
      size: 350,
      isEditableCell: edit,
      editableType: 'textArea',
      // editableType: 'richTextEditor',
      // cell: ({ row }: { row: any }) => {
      //   return <RichTextEditor value={row?.original?.mitigation_measures} isEdit={false} isGrid={true} />
      // },
      cell: ({ row }) => {
        const val = row.original.mitigation_measures
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      require: true,
    },
    {
      accessorKey: 'forecasted_closure_date',
      header: 'Forecasted Closure Date',
      size: 150,
      isEditableCell: edit,
      isSaveConfirmationRequired: true,
      editableType: 'date',
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      cell: forecastedClosureDateCell,
      require: true,
    },
    {
      accessorKey: 'forecast_closure_date_note',
      header: 'Forecasted Closure Date Note',
      size: 350,
      isEditableCell: edit,
      editableType: 'text',
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      cell: ({ row }) => {
        const val = row.original.forecast_closure_date_note
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
      require: false,
    },

    {
      accessorKey: 'risk_category',
      header: 'Risk Category',
      visible: false,
      require: false,
    },
    {
      accessorKey: 'risk_discipline',
      header: 'Risk Discipline',
      visible: false,
      size: 150,
      require: false,
    },
    {
      accessorKey: 'risk_owner',
      header: 'Risk Owner',
      visible: false,
      size: 150,
      require: false,
    },
    {
      accessorKey: 'risk_impact',
      visible: false,
      header: 'Risk Impact',
      size: 150,
      require: false,
    },
  ]

  return (
    <div className={styles.container}>
      {filteredAreaOfConcern.length >= 1 && (
        <div className={styles.table}>
          <TanStackTable
            rows={filteredAreaOfConcern as any}
            columns={columns}
            onDragEnd={handleDragAndDrop}
            gridName={'Project Challenges/Risks'}
            enableSticky={false}
          />
        </div>
      )}

      <PulseButton
        disabled={!edit}
        onClick={() => handleOpenModel()}
        label="Add Key Risks"
        icon={<AddOutlinedIcon fontSize="large" />}
      />
      <AddKeyRiskModel
        edit={edit}
        open={isModelOpen}
        onClose={handleCloseModel}
        project={project}
        editKeyRisksIndex={editKeyRisksIndex}
        filteredAreaOfConcern={filteredAreaOfConcern}
      />
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDelete(deleteModel as number)}
      />
      <PulseModel
        closable={false}
        style={{ width: 'fitContent' }}
        open={isValidationModel}
        onClose={() => setIsValidationModel(false)}
        content={<ValidationModel messages={validationMessage} onClose={() => setIsValidationModel(false)} />}
      />
    </div>
  )
}

export default KeyRisks
