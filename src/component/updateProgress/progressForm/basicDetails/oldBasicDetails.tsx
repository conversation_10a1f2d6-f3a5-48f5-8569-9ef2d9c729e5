import React, { useEffect, useMemo, useState } from 'react'
import { Box, InputAdornment, Tooltip } from '@mui/material'
import { format, parseISO } from 'date-fns'
import { FormikValues } from 'formik'
import { toast } from 'sonner'
import styles from './BasicDetails.module.scss'
import Checkbox from '../../checkBox'
import { calculateDateDifference } from '../../helper'
import ComboBox from '@/src/component/shared/combobox'
import DatePicker from '@/src/component/shared/dataPicker'
import NumberInputField from '@/src/component/shared/numberInputField'
import TextInputField from '@/src/component/shared/textInputField'
import PercentageIcon from '@/src/component/svgImages/percentageIcon'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'
import { useGetConsultant } from '@/src/hooks/useConsultant'
import { useGetContractor } from '@/src/hooks/useContractor'
import { useGetPmcConsultant } from '@/src/hooks/usePmcConsultant'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'
import { convertDDMMYYYYToLongDate } from '@/src/utils/dateUtils'
import { hasDesignTeamRole, isUser } from '@/src/utils/userUtils'

interface IBasicDetailProps {
  formik: FormikValues
  edit: boolean
}

const BasicDetails: React.FC<IBasicDetailProps> = ({ formik, edit }) => {
  //react-hook
  const [isLdcDesignInitiation, setIsLdcDesignInitiation] = useState(false)
  const [isContractConstructionDlp, setIsContractConstructionDlp] = useState(false)
  // TODO
  const { consultants } = useGetConsultant()
  // TODO
  const { contractors } = useGetContractor()
  // TODO
  const { pmcConsultants } = useGetPmcConsultant()
  const { currentUser } = useAuthorization()

  const consultantsOptions = useMemo(() => {
    return populateDropdownOptions(consultants, 'consultant')
  }, [consultants])

  const contractorsOption = useMemo(() => {
    return populateDropdownOptions(contractors, 'contractor')
  }, [contractors])

  const pmcConsultantsOption = useMemo(() => {
    return populateDropdownOptions(pmcConsultants, 'pmc_consultant')
  }, [pmcConsultants])

  useEffect(() => {
    setIsLdcDesignInitiation(
      formik.values.stage_status === LDC_PROCUREMENT ||
        formik.values.stage_status === DESIGN ||
        formik.values.stage_status === INITIATION,
    )

    setIsContractConstructionDlp(
      formik.values.stage_status === CONTRACTOR_PROCUREMENT ||
        formik.values.stage_status === CONSTRUCTION ||
        formik.values.stage_status === DLP_PROJECT_CLOSEOUT,
    )
  }, [formik.values.stage_status])

  const REGEX = useMemo(() => /^[0-9]*(\.[0-9]*)?$/, [])

  return (
    <div>
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 1fr 1fr',
          gap: '24px',
          marginTop: '16px',
        }}
      >
        {/* <div>
          <ComboBox
            options={developersOption}
            labelText={"Executing Entity"}
            placeholder="Type of search..."
            value={
              formik.values?.executing_entity
                ? {
                    label: formik.values?.executing_entity,
                    value: formik.values?.executing_entity,
                  }
                : null
            }
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                executing_entity: val?.value || "",
              })
            }
          />
        </div> */}
        {/* Baseline Plan Finish */}
        {isLdcDesignInitiation && (
          <div>
            <DatePicker
              name="baseline_plan_finish"
              labelText="Baseline Plan Finish"
              placeholder="DD/MM/YY"
              disabled={(!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type)}
              className={`${(!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type) ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.baseline_plan_finish)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('baseline_plan_finish', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.forecast_finish) {
                  const variance = calculateDateDifference(date, formik.values.forecast_finish)
                  formik.setValues({ ...formik.values, baseline_plan_finish: date, variance_in_days: variance })
                } else {
                  formik.setFieldValue('baseline_plan_finish', date)
                }
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <DatePicker
              name="plan_end_date"
              labelText="Baseline Plan Finish"
              placeholder="DD/MM/YY"
              disabled={
                formik.values.stage_status === CONTRACTOR_PROCUREMENT || formik.values.stage_status === CONSTRUCTION
                  ? !edit || isUser(currentUser.user_type)
                  : (!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type)
              }
              className={`${
                (
                  formik.values.stage_status === CONTRACTOR_PROCUREMENT || formik.values.stage_status === CONSTRUCTION
                    ? !edit || isUser(currentUser.user_type)
                    : (!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type)
                )
                  ? ''
                  : styles.highlightField
              }  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.plan_end_date)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('plan_end_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.forecasted_end_date) {
                  const variance =
                    formik.values.stage_status === CONSTRUCTION
                      ? formik.values.variance_in_days
                      : calculateDateDifference(date, formik.values.forecasted_end_date)
                  formik.setValues({ ...formik.values, plan_end_date: date, variance_in_days: variance })
                } else {
                  formik.setFieldValue('plan_end_date', date)
                }
              }}
            />
          </div>
        )}
        {/* Forecast Finish */}
        {isLdcDesignInitiation && (
          <div>
            <DatePicker
              name="forecast_finish"
              labelText="Forecast/Actual Finish"
              placeholder="DD/MM/YY"
              disabled={!edit}
              className={`${!edit ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.forecast_finish)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('forecast_finish', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.baseline_plan_finish) {
                  const variance = calculateDateDifference(formik.values.baseline_plan_finish, date)
                  formik.setValues({ ...formik.values, forecast_finish: date, variance_in_days: variance })
                } else {
                  formik.setFieldValue('forecast_finish', date)
                }
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <DatePicker
              name="forecasted_end_date"
              labelText="Forecast/Actual Finish"
              placeholder="DD/MM/YY"
              disabled={!edit}
              value={convertDDMMYYYYToLongDate(formik.values.forecasted_end_date)}
              className={`${!edit ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('forecasted_end_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                const dateToCalculate =
                  formik.values.stage_status === CONSTRUCTION
                    ? formik.values.eot_to_contractor
                      ? formik.values.eot_to_contractor
                      : formik.values.contract_end_date
                    : formik.values.plan_end_date
                if (dateToCalculate) {
                  const variance = calculateDateDifference(dateToCalculate, date)
                  formik.setValues({ ...formik.values, forecasted_end_date: date, variance_in_days: variance })
                } else {
                  formik.setFieldValue('forecasted_end_date', date)
                }
              }}
            />
          </div>
        )}
        {/* Plan Progress */}
        {isLdcDesignInitiation && (
          <div>
            {/* BACKEND:AUTO_CALCULATED */}
            <TextInputField
              name="rev_plan_percentage"
              placeholder="1,2,3,..."
              variant={'outlined'}
              classes={{ root: styles.planPercentage }}
              disabled={!edit || isUser(currentUser.user_type)}
              // disabled={true}
              className={`${!edit || isUser(currentUser.user_type) ? '' : styles.highlightField} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
              // className={`${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
              labelText={'Plan Progress %'}
              onChange={(event) => {
                const inputValue = event.target.value

                if (Number(inputValue) <= 100) {
                  const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                  if (isValidInput || inputValue === '') {
                    return formik.setFieldValue('rev_plan_percentage', inputValue)
                  }
                }
              }}
              value={formik.values?.rev_plan_percentage}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            {/* BACKEND:AUTO_CALCULATED */}
            <TextInputField
              name="revised_plan_perc"
              // disabled={edit && formik.values.stage_status === CONSTRUCTION ? false : true}
              disabled={!edit || isUser(currentUser.user_type)}
              placeholder="1,2,3,..."
              variant={'outlined'}
              labelText={'Plan Progress %'}
              onChange={(event) => {
                const inputValue = event.target.value

                if (Number(inputValue) <= 100) {
                  const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                  if (isValidInput || inputValue === '') {
                    return formik.setFieldValue('revised_plan_perc', inputValue)
                  }
                }
              }}
              classes={{ root: styles.planPercentage }}
              // className={`${edit && formik.values.stage_status === CONSTRUCTION ? styles.highlightField : ''} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
              className={`${!edit || isUser(currentUser.user_type) ? '' : styles.highlightField} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
              value={formik.values?.revised_plan_perc}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {/* Actual Progress */}
        {isLdcDesignInitiation && (
          <div>
            <TextInputField
              name="actual_percentage"
              placeholder="1,2,3,..."
              variant={'outlined'}
              disabled={!edit}
              classes={{ root: styles.planPercentage }}
              className={`${!edit ? '' : styles.highlightField}  ${styles.inputFields}`}
              labelText={'Actual Progress %'}
              onChange={(event) => {
                const inputValue = event.target.value
                const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('actual_percentage', inputValue)
                }
              }}
              value={formik.values?.actual_percentage}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <TextInputField
              name="actual_plan_perc"
              placeholder="1,2,3,..."
              classes={{ root: styles.planPercentage }}
              disabled={!edit}
              className={`${!edit ? '' : styles.highlightField} ${formik.values.rev_plan_percentage == 100 ? styles.greenHighlight : ''} ${styles.dataPickerInput}`}
              variant={'outlined'}
              labelText={'Actual Progress %'}
              onChange={(event) => {
                const inputValue = event.target.value
                const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                if (isValidInput || inputValue === '') {
                  return formik.setFieldValue('actual_plan_perc', inputValue)
                }
              }}
              value={formik.values?.actual_plan_perc}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {/* Revised Plan Finish */}
        {isLdcDesignInitiation && (
          <div>
            <DatePicker
              name="revised_baseline_finish"
              labelText="Revised Plan Finish"
              placeholder="DD/MM/YY"
              disabled={(!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type)}
              className={`${(!edit && !hasDesignTeamRole(currentUser)) || isUser(currentUser.user_type) ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.revised_baseline_finish)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('revised_baseline_finish', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('revised_baseline_finish', date)
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <DatePicker
              name="revised_plan_end_date"
              labelText="Revised Plan Finish"
              placeholder="DD/MM/YY"
              // disabled={!edit && !hasDesignTeamRole(currentUser)}
              disabled={
                formik.values.stage_status === CONTRACTOR_PROCUREMENT || formik.values.stage_status === CONSTRUCTION
                  ? !edit || isUser(currentUser.user_type)
                  : !edit && !hasDesignTeamRole(currentUser)
              }
              className={`${
                (!edit || isUser(currentUser.user_type)) &&
                (formik.values.stage_status === CONTRACTOR_PROCUREMENT || formik.values.stage_status === CONSTRUCTION)
                  ? ''
                  : !edit && !hasDesignTeamRole(currentUser)
                    ? ''
                    : styles.highlightField
              }  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.revised_plan_end_date)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('revised_plan_end_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('revised_plan_end_date', date)
              }}
            />
          </div>
        )}
        {/* TODO: PROCUREMENT_START_DATE */}
        {formik.values.stage_status === CONTRACTOR_PROCUREMENT ? (
          <div>
            <DatePicker
              name="procurement_start_date"
              labelText="Procurement Start Date"
              className={`${!edit ? '' : styles.highlightField} ${styles.dataPickerInput}`}
              disabled={!edit}
              placeholder="DD/MM/YY"
              value={
                formik.values.procurement_start_date
                  ? convertDDMMYYYYToLongDate(formik.values.procurement_start_date)
                  : ''
              }
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('procurement_start_date', null)
                  return
                }
                const selectedDate = value?.toISOString()
                const formattedDate = format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('procurement_start_date', formattedDate)
              }}
            />
          </div>
        ) : null}

        {/* uncomment-it  */}
        {/* Procurement Start Date || Contract Start Date */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <DatePicker
              name="contract_start_date"
              labelText={'Contract Start Date'}
              // minDate={new Date()}
              className={`${
                !edit || (formik.values.stage_status === CONSTRUCTION && isUser(currentUser.user_type))
                  ? ''
                  : styles.highlightField
              }
                   ${styles.dataPickerInput}`}
              // disabled={!edit}
              disabled={formik.values.stage_status === CONSTRUCTION ? !edit || isUser(currentUser.user_type) : !edit}
              placeholder="DD/MM/YY"
              value={
                formik.values.contract_start_date ? convertDDMMYYYYToLongDate(formik.values.contract_start_date) : ''
              }
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('contract_start_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.contract_end_date) {
                  const firstDate = formik.values.eot_to_contractor
                    ? formik.values.eot_to_contractor
                    : formik.values.contract_end_date
                  const durationInDays = calculateDateDifference(firstDate, date)
                  formik.setValues({ ...formik.values, contract_start_date: date, duration_in_days: durationInDays })
                } else {
                  formik.setFieldValue('contract_start_date', date)
                }
              }}
            />
          </div>
        ) : null}
        {/* comment-it */}
        {/* {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <DatePicker
              name="contract_start_date"
              labelText={
                formik.values.stage_status === CONTRACTOR_PROCUREMENT ? 'Procurement Start Date' : 'Contract Start Date'
              }
              // minDate={new Date()}
              className={`${
                !edit || (formik.values.stage_status === CONSTRUCTION && isUser(currentUser.user_type))
                  ? ''
                  : styles.highlightField
              }
                   ${styles.dataPickerInput}`}
              // disabled={!edit}
              disabled={formik.values.stage_status === CONSTRUCTION ? !edit || isUser(currentUser.user_type) : !edit}
              placeholder="DD/MM/YY"
              value={convertDDMMYYYYToLongDate(formik.values.contract_start_date)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('contract_start_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('contract_start_date', date)
              }}
            />
          </div>
        ) : null} */}
        {/* Contract End Date */}
        {formik.values.stage_status === CONSTRUCTION ? (
          <div>
            <DatePicker
              name="contract_end_date"
              labelText="Contract End Date"
              placeholder="DD/MM/YY"
              // disabled={!edit}
              disabled={!edit || isUser(currentUser.user_type)}
              // minDate={new Date()}
              className={`${!edit || isUser(currentUser.user_type) ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.contract_end_date)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('contract_end_date', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                if (formik.values.forecasted_end_date || formik.values.contract_start_date) {
                  const variance =
                    formik.values.forecasted_end_date &&
                    !formik.values.eot_to_contractor &&
                    calculateDateDifference(date, formik.values.forecasted_end_date)
                  const durationInDays =
                    formik.values.contract_start_date &&
                    calculateDateDifference(date, formik.values.contract_start_date)
                  formik.setValues({
                    ...formik.values,
                    contract_end_date: date,
                    variance_in_days: formik.values.forecasted_end_date ? variance : '',
                    duration_in_days: formik.values.contract_start_date ? durationInDays : '',
                  })
                } else {
                  formik.setFieldValue('contract_end_date', date)
                }
              }}
            />
          </div>
        ) : null}
        {/* eot_to_entity */}
        {/* {formik.values.stage_status === CONSTRUCTION ? (
          <div>
            <TextInputField
            name="eot_to_entity"
            placeholder="Type here..."
            variant={"outlined"}
            className={styles.inputFields}
            labelText={"Eot To Entity"}
            onChange={formik.handleChange}
            value={formik.values?.eot_to_entity}
          />
            <DatePicker
              name="eot_to_entity"
              labelText="EOT To Entity"
              placeholder="DD/MM/YY"
              disabled={!edit}
              className={styles.dataPickerInput}
              value={convertDDMMYYYYToLongDate(formik.values.eot_to_entity)}
              onChange={(value) => {
                const selectedDate = value?.toISOString()
                const date = format(parseISO(selectedDate), 'dd-MM-yyyy')
                formik.setFieldValue('eot_to_entity', date)
              }}
            />
          </div>
        ) : null} */}
        {/* eot_to_contractor */}
        {formik.values.stage_status === CONSTRUCTION ? (
          <div>
            {/* <TextInputField
            name="eot_to_contractor"
            placeholder="Type here..."
            variant={"outlined"}
            className={styles.inputFields}
            labelText={"Eot To Contractor"}
            onChange={formik.handleChange}
            value={formik.values?.eot_to_contractor}
          /> */}
            <DatePicker
              name="eot_to_contractor"
              labelText="Potential EOT to Contractor"
              placeholder="DD/MM/YY"
              // disabled={!edit}
              disabled={!edit || isUser(currentUser.user_type)}
              className={`${!edit || isUser(currentUser.user_type) ? '' : styles.highlightField} ${styles.dataPickerInput}`}
              value={convertDDMMYYYYToLongDate(formik.values.eot_to_contractor)}
              onChange={(value) => {
                if (value == 'Invalid Date') {
                  formik.setFieldValue('eot_to_contractor', null)
                  return null
                }
                const selectedDate = value && value?.toISOString()
                const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                const variance = calculateDateDifference(date, formik.values.forecasted_end_date)
                formik.setValues({ ...formik.values, eot_to_contractor: date, variance_in_days: variance })
              }}
            />
          </div>
        ) : null}
        {/* {isLdcDesignInitiation && (
          <div>
            <TextInputField
              name="rev_plan_percentage"
              placeholder="1,2,3,..."
              variant={"outlined"}
              classes={{ root: styles.planPercentage }}
              className={`${
                formik.values.rev_plan_percentage == 100
                  ? styles.greenHighlight
                  : ""
              }`}
              labelText={"Plan Progress %"}
              onChange={formik.handleChange}
              value={formik.values?.rev_plan_percentage}
              InputProps={{
                endAdornment: (
                  <InputAdornment
                    position="start"
                    className={styles.endAdornment}
                  >
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <TextInputField
              name="revised_plan_perc"
              placeholder="1,2,3,..."
              variant={"outlined"}
              labelText={"Plan Progress %"}
              onChange={formik.handleChange}
              classes={{ root: styles.planPercentage }}
              className={`${
                formik.values.revised_plan_perc == 100
                  ? styles.greenHighlight
                  : ""
              }`}
              value={formik.values?.revised_plan_perc}
              InputProps={{
                endAdornment: (
                  <InputAdornment
                    position="start"
                    className={styles.endAdornment}
                  >
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )} */}
        {/* supervision_consultant  */}
        {/* {isLdcDesignInitiation && (
          <div>
            <TextInputField
              name="rev_plan_percentage"
              placeholder="1,2,3,..."
              variant={"outlined"}
              classes={{ root: styles.planPercentage }}
              className={`${
                formik.values.rev_plan_percentage == 100
                  ? styles.greenHighlight
                  : ""
              }`}
              labelText={"Plan Progress %"}
              onChange={formik.handleChange}
              value={formik.values?.rev_plan_percentage}
              InputProps={{
                endAdornment: (
                  <InputAdornment
                    position="start"
                    className={styles.endAdornment}
                  >
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )}
        {isContractConstructionDlp && (
          <div>
            <TextInputField
              name="revised_plan_perc"
              placeholder="1,2,3,..."
              variant={"outlined"}
              labelText={"Plan Progress %"}
              onChange={formik.handleChange}
              classes={{ root: styles.planPercentage }}
              className={`${
                formik.values.revised_plan_perc == 100
                  ? styles.greenHighlight
                  : ""
              }`}
              value={formik.values?.revised_plan_perc}
              InputProps={{
                endAdornment: (
                  <InputAdornment
                    position="start"
                    className={styles.endAdornment}
                  >
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        )} */}
        {/* Plan L Week */}
        <div>
          {/* BACKEND:AUTO_CALCULATED */}
          <Tooltip title={'Plan Progress % Last Period'} arrow>
            <div>
              <TextInputField
                name="plan_l_week"
                placeholder="Type here..."
                variant={'outlined'}
                disabled={true}
                classes={{ root: styles.planPercentage }}
                className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Plan Progress % Last Period'}
                onChange={formik.handleChange}
                value={formik.values?.plan_l_week}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <PercentageIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  ),
                }}
              />
            </div>
          </Tooltip>
        </div>
        {/* Actual L Week */}
        <div>
          {/* BACKEND:AUTO_CALCULATED */}
          <Tooltip title={'Actual Progress % Last Period'} arrow>
            <div>
              <TextInputField
                name="actual_l_week"
                placeholder="Type here..."
                variant={'outlined'}
                disabled={true}
                classes={{ root: styles.planPercentage }}
                className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Actual Progress % Last Period'}
                onChange={formik.handleChange}
                value={formik.values?.actual_l_week}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <PercentageIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  ),
                }}
              />
            </div>
          </Tooltip>
        </div>
        {/* supervision_consultant  */}
        {/* <div>
          <TextInputField
            name="supervision_consultant"
            placeholder="Type here..."
            variant={"outlined"}
            className={styles.inputFields}
            labelText={"Supervision Consultant"}
            onChange={formik.handleChange}
            value={formik.values?.supervision_consultant}
          />
        </div> */}
        {/* supervision_consultant */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <ComboBox
              clearIcon={true}
              options={pmcConsultantsOption}
              labelText={'Supervision Consultant '}
              placeholder="Type of search..."
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  `}
              disabled={!edit}
              value={
                formik.values?.supervision_consultant
                  ? {
                      label: formik.values?.supervision_consultant,
                      value: formik.values?.supervision_consultant,
                    }
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  supervision_consultant: val?.value || '',
                })
              }
            />
          </div>
        ) : null}
        {/* PMC consultant */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <ComboBox
              clearIcon={true}
              options={pmcConsultantsOption}
              labelText={'PMC Consultant '}
              disabled={!edit}
              placeholder="Type of search..."
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  }`}
              value={
                formik.values?.PMC_Consultant
                  ? {
                      label: formik.values?.PMC_Consultant,
                      value: formik.values?.PMC_Consultant,
                    }
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  PMC_Consultant: val?.value || '',
                })
              }
            />
          </div>
        ) : null}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== CONSTRUCTION &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT ? (
          <div>
            <ComboBox
              options={consultantsOptions}
              labelText={'Consultant'}
              clearIcon={true}
              disabled={!edit}
              placeholder="Type of search..."
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  }`}
              value={
                formik.values?.consultant
                  ? {
                      label: formik.values?.consultant,
                      value: formik.values?.consultant,
                    }
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  consultant: val?.value || '',
                })
              }
            />
          </div>
        ) : null}
        {/* Contractor */}
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== DESIGN &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <ComboBox
              options={contractorsOption}
              labelText={'Contractor'}
              clearIcon={true}
              className={`${!edit ? styles.comboBoxField : styles.comboHighlight}  `}
              placeholder="Type of search..."
              disabled={!edit}
              value={
                formik.values?.contractor
                  ? {
                      label: formik.values?.contractor,
                      value: formik.values?.contractor,
                    }
                  : null
              }
              onChange={(val) =>
                formik.setValues({
                  ...formik.values,
                  contractor: val?.value || '',
                })
              }
            />
          </div>
        ) : null}
        {/* Design Stage Weightage */}
        {/* {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== CONSTRUCTION &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT &&
        formik.values.stage_status !== LDC_PROCUREMENT ? (
          <div>
            <TextInputField
              name="design_stage_weightage"
              placeholder="1,2,3,..."
              variant={'outlined'}
              onChange={formik.handleChange}
              disabled={true}
              className={styles.inputFields}
              value={formik.values?.design_stage_weightage}
              labelText={'Design Stage Weightage'}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <PercentageIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                ),
              }}
            />
          </div>
        ) : null} */}
        {/* Phase Weightage  */}
        {/* <div>
          <TextInputField
            name="phase_weightage"
            placeholder="1,2,3,..."
            variant={'outlined'}
            labelText={'Phase Weightage'}
            classes={{ root: styles.planPercentage }}
            className={`${true ? '' : styles.comboHighlight}  ${styles.inputFields}`}
            disabled={true}
            onChange={formik.handleChange}
            value={formik.values?.phase_weightage}
            InputProps={{
              endAdornment: (
                <InputAdornment position="start" className={styles.endAdornment}>
                  <PercentageIcon className={styles.endAdornmentIcon} />
                </InputAdornment>
              ),
            }}
          />
        </div> */}
        {/* PMC consultant
        {formik.values.stage_status !== INITIATION &&
        formik.values.stage_status !== CONTRACTOR_PROCUREMENT &&
        formik.values.stage_status !== DLP_PROJECT_CLOSEOUT ? (
          <div>
            <MultiAutoSelect
              // className={styles.multiSelect}
              // className={styles.textField}
              className={styles.inputFields}
              isSx={false}
              labelText="PMC consultant"
              options={convertMultiSelectOption(consultantsOptions)}
              value={
                formik.values && formik.values["consultant"]
                  ? formik.values["consultant"].split(", ")
                  : []
              }
              handleSelectedOption={(selectedOptions) =>
                formik.setFieldValue(
                  "consultant",
                  selectedOptions.map((option) => option).join(", ")
                )
              }
            />
          </div>
        ) : null} */}
        {formik.values.stage_status === CONSTRUCTION ? (
          <>
            <div>
              <TextInputField
                name="plan_contractor_progress_percentage"
                placeholder="Type here..."
                variant={'outlined'}
                disabled={!edit}
                classes={{ root: styles.planPercentage }}
                className={`${!edit ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Contractor Plan Progress %'}
                onChange={(event) => {
                  const inputValue = event.target.value
                  const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                  if (isValidInput || inputValue === '') {
                    return formik.setFieldValue('plan_contractor_progress_percentage', inputValue)
                  }
                }}
                value={formik.values?.plan_contractor_progress_percentage}
              />
            </div>
            <div>
              <TextInputField
                name="duration_in_days"
                placeholder="Type here..."
                variant={'outlined'}
                disabled={true}
                classes={{ root: styles.planPercentage }}
                className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Contract Duration (In Days)'}
                onChange={(event) => {
                  const inputValue = event.target.value
                  const isValidInput = REGEX.test(inputValue) && parseFloat(inputValue) <= 100

                  if (isValidInput || inputValue === '') {
                    return formik.setFieldValue('duration_in_days', inputValue)
                  }
                }}
                value={formik.values?.duration_in_days}
              />
            </div>
            <div>
              {/* BACKEND:AUTO_CALCULATED */}
              <TextInputField
                name="buffer_in_days"
                variant={'outlined'}
                disabled={true}
                classes={{ root: styles.planPercentage }}
                className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
                labelText={'Internal Buffer (In Days)'}
                onChange={formik.handleChange}
                value={formik.values?.buffer_in_days}
              />
            </div>
            <div>
              <DatePicker
                name="kickoff_meeting_date"
                labelText="Kickoff Meeting date"
                placeholder="DD/MM/YY"
                disabled={!edit}
                className={`${!edit ? '' : styles.highlightField}  ${styles.dataPickerInput}`}
                value={convertDDMMYYYYToLongDate(formik.values.kickoff_meeting_date)}
                onChange={(value) => {
                  if (value == 'Invalid Date') {
                    formik.setFieldValue('kickoff_meeting_date', null)
                    return null
                  }
                  const selectedDate = value && value?.toISOString()
                  const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                  formik.setFieldValue('kickoff_meeting_date', date)
                }}
              />
            </div>
          </>
        ) : null}
        <div>
          {/* BACKEND:AUTO_CALCULATED */}
          <TextInputField
            name="variance_in_days"
            variant={'outlined'}
            disabled={true}
            classes={{ root: styles.planPercentage }}
            className={`${true ? '' : styles.highlightField} ${styles.inputFields}`}
            labelText={'variance (In Days)'}
            onChange={formik.handleChange}
            value={formik.values?.variance_in_days}
          />
        </div>

        {formik.values.stage_status === DESIGN ? (
          <>
            <NumberInputField
              name="pte"
              disabled={!edit}
              labelText={'PTE'}
              placeholder="1,2,3..."
              className={`${!edit ? '' : styles.highlightField} ${styles.inputFields}`}
              value={formik?.values?.pte}
              onChange={(value) => formik.setFieldValue('pte', value)}
            />
          </>
        ) : null}
      </div>
      <Box
        sx={{
          '& .pulse-checkbox': {
            flexDirection: 'row-reverse !important',
            gap: '5px',
            justifyContent: 'flex-end',
            alignItems: 'center',

            '& .pulse-checkbox-label': {
              // lineHeight: 1,
            },

            '& .MuiButtonBase-root.MuiCheckbox-root': {
              paddingTop: '0 !important',
            },
          },
        }}
        className={styles.checkboxContainer}
      >
        {formik.values.stage_status === CONSTRUCTION ? (
          <>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Contract signature received"
                checked={formik.values.is_contract_signature_received}
                onChange={() =>
                  formik.setFieldValue('is_contract_signature_received', !formik.values.is_contract_signature_received)
                }
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Advance payment bond received"
                checked={formik.values.is_advance_payment_bond_received}
                onChange={() =>
                  formik.setFieldValue(
                    'is_advance_payment_bond_received',
                    !formik.values.is_advance_payment_bond_received,
                  )
                }
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Insurances received"
                checked={formik.values.is_insurance_received}
                onChange={() => formik.setFieldValue('is_insurance_received', !formik.values.is_insurance_received)}
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Advance payment released"
                checked={formik.values.is_advance_payment_released}
                onChange={() =>
                  formik.setFieldValue('is_advance_payment_released', !formik.values.is_advance_payment_released)
                }
              />
            </div>
            <div>
              <Checkbox
                disabled={!edit}
                className={styles.checkbox}
                labelText="Performance bond received"
                checked={formik.values.is_performance_bond_received}
                onChange={() =>
                  formik.setFieldValue('is_performance_bond_received', !formik.values.is_performance_bond_received)
                }
              />
            </div>
          </>
        ) : null}
      </Box>
    </div>
  )
}

export default BasicDetails
