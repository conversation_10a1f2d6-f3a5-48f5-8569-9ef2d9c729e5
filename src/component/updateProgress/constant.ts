// USE_FOR: These fields help track changes. If data changes when returning to this screen, we ask whether to save the updated data.
export const updateProgressFormField = [
  'actual_progress_percentage_of_last_week',
  'actual_progress_percentage_for_last_week',
  'eot_to_entity',
  'eot_to_contractor',
  'kickoff_meeting_date',
  'supervision_consultant',
  'consultant',
  'key_highlights',
  'design_manager ',
  'procurement_manager',
  'svp ',
  'design_stage_weightage',
  'executing_entity',
  'phase_weightage',
  'contractor',
  'contract_start_date',
  'procurement_start_date',
  'contract_end_date',
  'decree_end_date',
  'pmc_consultant',
  'latitude',
  'longitude',
  'actual_plan_percentage',
  'revised_plan_percentage',
  'plan_end_date',
  'revised_plan_end_date',
  'forecasted_end_date',
  'actual_percentage',
  'rev_plan_percentage',
  'baseline_plan_finish',
  'revised_baseline_finish',
  'forecast_finish',
  'spi',
  'bua',
  'gfa',
  'no_plots',
  'no_units',
  'project_management_var',
  'forecast_completion_last_period',
  'cost_per_sqm',
  'delay_this_period',
  'cumulative_delay',
  'manpower_planned',
  'manpower_actual',
  'man_hours',
  'incidents',
  'fatalities',
  'lti_ratio',
  'time_elapsed_percentage',
  'procurement_package',
  'pte',
  'pte_attachment',
]

// Dynamically add properties if they exist in `values`
export const projectManagementFields = [
  'budget',
  'time_elapsed_percentage',
  'spi',
  'project_management_var',
  'forecast_completion_last_period',
  'delay_this_period',
  'cumulative_delay',
  'manpower_planned',
  'manpower_actual',
  'man_hours',
  'incidents',
  'fatalities',
  'lti_ratio',
  'procurement_package',
  'last_updated',
  'cost_per_sqm',
  'bua',
  'gfa',
  'no_units',
  'no_plots',
]

export const statusOrder = [
  'Initiation',
  'LDC Procurement',
  'Design',
  'Contractor Procurement',
  'Construction',
  'DLP and Project Closeout',
  'Project Closeout',
]
