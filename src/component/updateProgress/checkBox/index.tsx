import { ReactElement } from 'react'
import { Checkbox as MuiCheck<PERSON>, CheckboxProps as MuiCheckboxProps } from '@mui/material'
import styles from './CheckBox.module.scss'
import CheckboxIcon from '../../svgImages/checkBoxIcon'
import CheckBoxSuccessIcon from '../../svgImages/checkBoxSuccessIcon'

// Extend MUI CheckboxProps to include labelText
interface CheckboxProps extends MuiCheckboxProps {
  labelText?: string
}

const Checkbox: React.FC<CheckboxProps> = ({
  icon,
  checkedIcon,
  checked,
  labelText,
  onChange,
  ...rest
}: CheckboxProps): ReactElement => {
  return (
    <div className={`${styles.checkBoxContainer} pulse-checkbox`}>
      <label className={`${labelText?.length ? styles.label : ''} pulse-checkbox-label`}>{labelText}</label>
      <MuiCheckbox
        {...rest}
        checked={checked} // Pass checked state
        onChange={onChange} // Pass onChange handler
        icon={icon ? icon : <CheckboxIcon />}
        checkedIcon={checkedIcon ? checkedIcon : <CheckBoxSuccessIcon />}
        sx={{ '&:hover': { backgroundColor: 'white' }, '& .MuiTouchRipple-root': { display: 'none' } }}
      />
    </div>
  )
}

export default Checkbox
