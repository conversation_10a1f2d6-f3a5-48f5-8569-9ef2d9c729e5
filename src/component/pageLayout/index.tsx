import React, { useEffect, useState } from 'react'
import MenuIcon from '@mui/icons-material/Menu'
import { useRouter } from 'next/router'
import styles from './ProjectLayout.module.scss'
import Sidebar from './sidebar'
import Drawer from '../shared/drawer'
import { DESKTOP, LAPTOP, MOBILE, TABLET, WIDE_SCREEN } from '@/src/constant/breakpoint'
import { Routes, StorageKeys } from '@/src/constant/enum'
import { useBreakpoint } from '@/src/customeHook/useBreakPoint'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { getLocalStorageItem } from '@/src/utils/storageUtils'

interface ProjectLayoutProps {
  children: React.ReactNode
}

const PageLayout: React.FC<ProjectLayoutProps> = ({ children }) => {
  const router = useRouter()
  const [loader, setLoader] = useState(true)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [isSearchDrawerOpen, setSearchIsDrawerOpen] = useState(false)
  const { currentLogInUserApi } = useAuthorization()
  const { getMasterPeriodsApi, setPeriodApi, currentPeriod, isPeriodChangeFromProjectList } = useMasterPeriod()
  const loginStatus = getLocalStorageItem(StorageKeys.IS_LOGIN)
  const breakpoint = useBreakpoint()

  useEffect(() => {
    if (!loginStatus) {
      router.push(Routes.LOGIN)
    } else {
      fetchPeriod()
    }
  }, [])

  const fetchPeriod = async () => {
    setLoader(true)
    const resOfPeriod: Record<string, any> = await getMasterPeriodsApi()
    if (resOfPeriod.payload.success) {
      console.log('currentPeriod: ', currentPeriod)
      console.log('condition---: ', currentPeriod && currentPeriod !== resOfPeriod.payload.data?.period)
      console.log('resOfPeriod.payload.data.period: ', resOfPeriod.payload.data?.period)
      // if (
      //   !currentPeriod ||
      //   (!isPeriodChangeFromProjectList && currentPeriod && currentPeriod !== resOfPeriod.payload.data.period)
      // )
      !currentPeriod ? setPeriodApi(resOfPeriod.payload.data?.period) : setPeriodApi(currentPeriod)
      const res: Record<string, any> = await currentLogInUserApi(
        !currentPeriod ? resOfPeriod.payload.data?.period : currentPeriod,
      )
      if (res.payload?.success) setLoader(false)
    }
  }

  return (
    <div
      className={styles.container}
      style={{ backgroundColor: router.pathname.includes('summary') ? '#f1f1ef' : '#FFF' }}
    >
      <div className={styles.projectLayout}>
        <div
          className={styles.pageContent}
          style={{
            backgroundColor: router.pathname.includes('summary') ? '#f1f1ef' : '#0000',
          }}
        >
          {(breakpoint === WIDE_SCREEN || breakpoint === LAPTOP || breakpoint === DESKTOP) && (
            <Sidebar isSearchDrawerOpen={isSearchDrawerOpen} setSearchIsDrawerOpen={setSearchIsDrawerOpen} />
          )}

          {loader ? (
            <div className={styles.loader}></div>
          ) : (
            <div className={styles.children}>
              {(breakpoint === MOBILE || breakpoint === TABLET) && (
                <MenuIcon className={styles.menuIcon} onClick={() => setIsDrawerOpen(true)} />
              )}
              {children}
            </div>
          )}
        </div>
      </div>
      <Drawer
        anchor="left"
        open={isDrawerOpen}
        onClose={() => {
          // isSearchDrawerOpen && setIsDrawerOpen(false)
          setSearchIsDrawerOpen(false)
          setIsDrawerOpen(false)
        }}
        paperSx={{
          overflow: 'hidden',
          width: isSearchDrawerOpen ? '100%' : '100px',
        }}
      >
        <Sidebar isSearchDrawerOpen={isSearchDrawerOpen} setSearchIsDrawerOpen={setSearchIsDrawerOpen} />
      </Drawer>
    </div>
  )
}

export default PageLayout
