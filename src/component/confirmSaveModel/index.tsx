import React from 'react'
import styles from './ConfirmSaveModel.module.scss'
import CancelRoundedIcon from '../svgImages/cancelRoundedIcon'
import CloseCircleIcon from '../svgImages/closeCircleIcon'
import SaveIcon from '../svgImages/saveIcon'
import Button from '@/src/component/shared/button'

interface ConfirmSaveModalProps {
  handleConfirm: () => void
  onClose: () => void
}

const ConfirmSaveModal: React.FC<ConfirmSaveModalProps> = ({ handleConfirm, onClose }) => {
  return (
    <>
      <div className={styles.title} id="confirm-save-modal-title">
        <div>
          You have unsaved changes.
          <br />
          <span>Are you sure you want to leave ?</span>
        </div>
        <CloseCircleIcon className={styles.cursorPointer} onClick={() => onClose()} />
      </div>
      <div className={styles.actionButtons}>
        <Button color="secondary" startIcon={<CancelRoundedIcon />} onClick={onClose}>
          Discard
        </Button>
        <Button className={styles.btnOfAction} startIcon={<SaveIcon />} onClick={handleConfirm}>
          Save
        </Button>
      </div>
    </>
  )
}

export default ConfirmSaveModal
