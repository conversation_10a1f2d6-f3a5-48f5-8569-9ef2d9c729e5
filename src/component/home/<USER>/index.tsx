import React from 'react'
import Image from 'next/image'
import AddDistricts from './districts'
import AddMasterKpi from './kpis/addMasterKpi'
import AddLocations from './locations'
import AddProjectClassification from './projectClassification'
import styles from './ProjectDetails.module.scss'
import AddSubLocations from './subLocation'
import AddTypology from './typology'
import Drawer from '../../shared/drawer'
import Typography<PERSON>ield from '../../shared/typography'
import AddDevelopers from '../developers/addDevelopers'
import ImageCard from '../imageCard'
import { DrawerStates } from '../interface'
import useModuleCount from '@/src/redux/moduleCount/useModuleCount'

interface IProjectDetails {
  drawerStates: DrawerStates
  toggleDrawer: (drawer: keyof DrawerStates) => (open: boolean) => void
}

const drawerComponentsMap = {
  addKpiDrawer: AddMasterKpi,
  addLocation: AddLocations,
  addSubLocation: AddSubLocations,
  addDistrict: AddDistricts,
  addDevelopers: AddDevelopers,
  addTypologies: AddTypology,
  addProjectClassification: AddProjectClassification,
}

const ProjectDetails: React.FC<IProjectDetails> = ({ drawerStates, toggleDrawer }) => {
  const { moduleCounts } = useModuleCount()

  const imageCards = [
    { label: 'Location', value: moduleCounts.project_details?.locations, drawer: 'addLocation', img: 'location' },
    {
      label: 'Sub Location',
      value: moduleCounts.project_details?.sub_location,
      drawer: 'addSubLocation',
      img: 'location',
    },
    { label: 'District', value: moduleCounts.project_details?.districts, drawer: 'addDistrict', img: 'location' },
    { label: 'Type', value: moduleCounts.project_details?.typologies, drawer: 'addTypologies', img: 'typology' },
    {
      label: 'Project Classification',
      value: moduleCounts.project_details?.project_classification,
      drawer: 'addProjectClassification',
      img: 'classification',
    },
    {
      label: 'Project Overview',
      value: moduleCounts.project_phases?.project_overview,
      drawer: 'addKpiDrawer',
      img: 'classification',
    },
  ]

  return (
    <div className={styles.cardContainer}>
      <Image
        src="/svg/projectDetailBg.svg"
        alt="background"
        height={334}
        width={513}
        className={styles.cardBackground}
      />
      <div className={styles.details}>
        {/* <div className={styles.info}> */}
        <TypographyField variant="subheading" text="Project Details" />
        {/* </div> */}

        <div className={styles.cardsWrapper}>
          {imageCards.map(({ label, value, drawer, img }) => (
            <ImageCard
              key={drawer}
              label={label}
              value={value}
              imageSrc={`/svg/${img}.svg`}
              imageWidth={53}
              imageHeight={50}
              onAddButtonClick={() => toggleDrawer(drawer as keyof DrawerStates)(true)}
            />
          ))}
        </div>

        {Object.entries(drawerComponentsMap).map(([key, Component]) => (
          <Drawer
            key={key}
            anchor="right"
            open={drawerStates[key as keyof DrawerStates]}
            onClose={() => toggleDrawer(key as keyof DrawerStates)(false)}
          >
            <Component drawerStates={drawerStates} onClose={() => toggleDrawer(key as keyof DrawerStates)(false)} />
          </Drawer>
        ))}
      </div>
    </div>
  )
}

export default ProjectDetails
