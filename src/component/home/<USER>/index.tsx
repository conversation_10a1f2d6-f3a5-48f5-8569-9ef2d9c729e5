import React, { useEffect, useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useFormik } from 'formik'
import styles from './EntityKeyAchievements.module.scss'
import ComboBox from '../../shared/combobox'
import TanStackTable from '../../shared/tanStackTable'
import { sortData } from '../../shared/tanStackTable/helper'
import { CustomColumnDef } from '../../shared/tanStackTable/interface'
import { showCustomToast } from '../../toast/ToastManager'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useEntityKeyAchievement from '@/src/redux/entityKeyAchievement/useEntityKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import { getProjects } from '@/src/services/projects'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'

const AddEntityKeyAchievement: React.FC<any> = ({ setIsEntity, isEntity, drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const {
    getEntityKeyAchievementStatus,
    entityKeyAchievements,
    getEntityKeyAchievementApi,
    addEntityKeyAchievementApi,
    updateEntityKeyAchievementApi,
    deleteEntityKeyAchievementApi,
  } = useEntityKeyAchievement()
  const { addEntityKeyAchievement } = drawerStates
  const { currentPeriod } = useMasterPeriod()
  const { phases, getMasterPhaseApi } = usePhase()

  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    enabled: addEntityKeyAchievement,
    select: (response) => response.data, // This extracts 'data' directly
  })

  const projectOption = useMemo(() => {
    return populateDropdownOptions(projects, 'project_name')
  }, [projects])
  const phasesOption = useMemo(() => {
    return populateDropdownOptions(phases, 'project_phase')
  }, [phases])

  useEffect(() => {
    addEntityKeyAchievement && getEntityKeyAchievementApi({ period: currentPeriod })
    // currentPeriod && getMasterProjectsApi({ period: currentPeriod });
    addEntityKeyAchievement && getMasterPhaseApi()
  }, [addEntityKeyAchievement])

  const formik = useFormik({
    initialValues: {
      project_name: '',
      phase: '',
      value: '',
      description: '',
    },
    onSubmit: async (values) => {
      const { project_name, phase, value, description } = values
      if (editIndex !== null) {
        const response: Record<string, any> = await updateEntityKeyAchievementApi({
          id: editIndex,
          data: { project_name, phase, value, description, period: currentPeriod },
        })
        if (response.payload.success === true) {
          getEntityKeyAchievementApi({ period: currentPeriod })
          showCustomToast('Record successfully updated', 'success')
        } else {
          showCustomToast(response.payload.response.data.message, 'error')
        }
        setEditIndex(null)
      } else {
        const response: Record<string, any> = await addEntityKeyAchievementApi({
          project_name,
          phase,
          value,
          description,
          period: currentPeriod,
        })
        if (response.payload.success === true) {
          getEntityKeyAchievementApi({ period: currentPeriod })
          showCustomToast('Record successfully added', 'success')
        } else {
          showCustomToast(response.payload.response.data.message, 'error')
        }
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    const response: Record<string, any> = await deleteEntityKeyAchievementApi(id)
    if (response.payload.success === true) {
      setDeleteModel(null)
      getEntityKeyAchievementApi({ period: currentPeriod })
    }
  }

  const handleEditButtonClick = (id: number) => {
    const editContractor: any = entityKeyAchievements.find((entityKeyAchievement) => entityKeyAchievement.id === id)
    if (editContractor) {
      formik.setValues({
        project_name: editContractor.project_name,
        phase: editContractor.phase,
        value: editContractor.value,
        description: editContractor.description,
      })
      setEditIndex(id)
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'phase',
      header: 'Phase',
      cell: ({ row }) => {
        return <>{row.original.phase || '-'}</>
      },
    },
    { accessorKey: 'project_name', header: 'Project Name' },
    { accessorKey: 'value', header: 'Value' },
    { accessorKey: 'description', header: 'Description' },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField
          className={styles.headerTitle}
          variant="subheadingSemiBold"
          text={'Add Entity Key Achievements'}
        />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        {!isEntity && (
          <form className={styles.form} onSubmit={formik.handleSubmit}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
              <ComboBox
                options={projectOption}
                labelText={'Project Name'}
                placeholder="Type of search..."
                value={
                  formik.values.project_name
                    ? {
                        label: formik.values.project_name,
                        value: formik.values.project_name,
                      }
                    : null
                }
                clearIcon={true}
                onChange={(val) => formik.setValues({ ...formik.values, project_name: val?.value || '' })}
                onBlur={() => formik.setTouched({ ...formik.touched, project_name: true })}
              />
              {/* <ComboBox
                options={phasesOption}
                labelText={"Phase"}
                placeholder="Type of search..."
                value={
                  formik.values.phase
                    ? {
                        label: formik.values.phase,
                        value: formik.values.phase,
                      }
                    : null
                }
                onChange={(val) =>
                  formik.setValues({
                    ...formik.values,
                    phase: val?.value || "",
                  })
                }
                onBlur={() =>
                  formik.setTouched({ ...formik.touched, phase: true })
                }
              /> */}
              <TextInputField
                className={styles.entityField}
                name="phase"
                labelText={'Phase'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.phase}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                className={styles.entityField}
                name="value"
                labelText={'Value'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.value}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                className={styles.entityField}
                name="description"
                labelText={'Description'}
                placeholder="Type something ..."
                variant={'outlined'}
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
            </div>
            <div>
              {editIndex !== null ? (
                <Button className={styles.addProjectButton} type="submit">
                  {'Update'}
                </Button>
              ) : (
                <Button className={styles.addProjectButton} color="secondary" type="submit">
                  {'+ Add Entity Key Achievements'}
                </Button>
              )}
            </div>
          </form>
        )}
        {entityKeyAchievements?.length >= 1 && (
          // <Table data={entityKeyAchievements} columns={columns} />
          <TanStackTable rows={sortData(entityKeyAchievements, 'phase') as any} columns={columns} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddEntityKeyAchievement
