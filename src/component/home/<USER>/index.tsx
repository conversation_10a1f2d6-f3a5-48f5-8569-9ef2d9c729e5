import React from 'react'
import Image from 'next/image'
import styles from './ImageCard.module.scss'
import { ImageCardProps } from './interface'
import TypographyField from '@/src/component/shared/typography'

const ImageCard: React.FC<ImageCardProps> = ({
  label,
  value,
  imageSrc,
  imageHeight,
  imageWidth,
  className,
  subLabel,
  onAddButtonClick,
}) => {
  return (
    <div className={`${styles.card} ${className}`} onClick={() => onAddButtonClick && onAddButtonClick()}>
      <div className={styles.content}>
        <div>
          <TypographyField
            className={styles.label}
            variant="caption"
            text={label}
            style={{ color: '#808080', width: '170px' }}
          />
          {subLabel && (
            <TypographyField className={styles.label} variant="caption" text={subLabel} style={{ color: '#808080' }} />
          )}
        </div>
        <TypographyField className={styles.value} variant="h4" text={value} />
        <Image
          src={imageSrc}
          alt={label}
          height={imageHeight}
          width={imageWidth}
          className={label === 'News Feed' || label === 'Scoring' ? styles.periodCardImage : styles.cardImage}
        />
      </div>
      {/* <PlusIcon
        className={styles.addButton}
        onClick={(e) => {
          e.stopPropagation()
          onAddButtonClick && onAddButtonClick()
        }}
      /> */}
    </div>
  )
}

export default ImageCard
