import { OwningEntities } from './constant'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { IOwningEntity } from '@/src/services/owningEntity/interface'
import { IScoring, IUpdateScoring } from '@/src/services/scoring/interface'

export const generateYearQuarterOptions = (startYear: string, endYear: string) => {
  const result: { label: string; value: string }[] = []
  const quarters = [
    { label: 'Q1', month: '01' },
    { label: 'Q2', month: '04' },
    { label: 'Q3', month: '07' },
    { label: 'Q4', month: '10' },
  ]

  for (let year = parseInt(startYear); year <= parseInt(endYear); year++) {
    quarters.forEach((q) => {
      result.push({
        label: `${year} ${q.label}`,
        value: `${year}-${q.month}-01`,
      })
    })
  }

  return result
}

export const getOverallSore = (res: IScoring) => {
  const schedule = res?.schedule_score ?? 0
  const variation = res?.variation_score ?? 0
  const expenditure = res?.expenditure_score ?? 0
  const overall_score = Number(schedule) + Number(variation) + Number(expenditure)
  return overall_score ? Number(overall_score?.toFixed(2)) : null
}

export const getScoringTableData = (scoreData: IScoring[]) => {
  return scoreData?.map((res) => {
    return { ...res, overall_score: getOverallSore(res) }
  })
}

// export const synchronizeEntityScores = (owning_entity: IOwningEntity[], entity_score: any[]) => {
//   const labels = new Set(entity_score.map((e) => e.label))
//   owning_entity
//     .filter((e) => !labels.has(e.owning_entity))
//     .forEach((o) => {
//       const match = OwningEntities?.find((e) => o.owning_entity === e.owning_entity)
//       entity_score.push({
//         id: crypto.randomUUID(),
//         quarter: entity_score[0]?.quarter,
//         executive_entity: match ? match.label : null,
//         label: o.owning_entity,
//         schedule_score: null,
//         variation_score: null,
//         expenditure_score: null,
//         isNew: true,
//       })
//     })

//   return entity_score?.map((res) => {
//     return { ...res, overall_score: getOverallSore(res) }
//   })

//   // TODO : uncomment below code for remove deleted owning_entity from entity score table also
//   // const owningEntitySet = new Set(owning_entity.map((e) => e.owning_entity))
//   // // Keep only entities present in owning_entity
//   // let filteredScores = entity_score.filter((e) => owningEntitySet.has(e.label))
//   // const labels = new Set(filteredScores.map((e) => e.label))

//   // owning_entity
//   //   .filter((e) => !labels.has(e.owning_entity))
//   //   .forEach((e) =>
//   //     filteredScores.push({
//   //       id: '',
//   //       quarter: entity_score[0]?.quarter,
//   //       executive_entity: null,
//   //       label: e.owning_entity,
//   //       schedule_score: null,
//   //       variation_score: null,
//   //       expenditure_score: null,
//   //     }),
//   //   )
//   // return filteredScores?.map((res) => {
//   //   return { ...res, overall_score: getOverallSore(res) }
//   // })
// }

export const generateArrayBasedOnOwningEntity = (quarter: string, entityArray: IOwningEntity[]) => {
  return entityArray.map((entity) => {
    return {
      id: crypto.randomUUID(),
      quarter,
      MasterOwningEntity: { ...entity },
      schedule_score: null,
      variation_score: null,
      expenditure_score: null,
    }
  })
}

export const formattedTableData = (data: IScoring[]) => {
  const formattedData = data?.map((res) => {
    return {
      ...res,
      executive_entity: res?.MasterOwningEntity?.full_name,
      label: res?.MasterOwningEntity?.owning_entity,
    }
  })
  return sortData(formattedData, 'label')
}
