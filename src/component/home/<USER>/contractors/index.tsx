import React, { useState } from 'react'
import { useFormik } from 'formik'
import styles from './Contractor.module.scss'
import { IAddControlManagerProps } from './interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import NumberInputField from '@/src/component/shared/numberInputField'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  useCreateContractor,
  useDeleteContractor,
  useGetContractor,
  useUpdateContractor,
} from '@/src/hooks/useContractor'
import { removeCommasAndConvert } from '@/src/utils/numberUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddContractors: React.FC<IAddControlManagerProps> = ({ drawerStates, onClose }) => {
  const [deleteModel, setDeleteModel] = useState<number | null>(null)

  const [editIndex, setEditIndex] = useState<number | null>(null)

  const { addContractors } = drawerStates
  //REACT-QUERY
  const { contractors } = useGetContractor(addContractors)
  // MUTATIONS
  const { mutate: addContractor } = useCreateContractor()
  const { mutate: deleteContractor } = useDeleteContractor()
  const { mutate: updateContractor } = useUpdateContractor()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
      setEditIndex(null)
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
      formik.resetForm()
      setEditIndex(null)
    },
  })

  const formik = useFormik({
    initialValues: {
      contractor: '',
      icv_percentage: 0,
      long_name: '',
      worker_welfare_compliance_percentage: 0,
      evaluation_score: null,
      number_of_grievances: null,
      trade_license_number: '',
    },
    onSubmit: async (values) => {
      const payload = {
        contractor: values.contractor,
        icv_percentage: values.icv_percentage,
        long_name: values.long_name || null,
        worker_welfare_compliance_percentage: values.worker_welfare_compliance_percentage,
        evaluation_score: values.evaluation_score || null,
        number_of_grievances: Number(values.number_of_grievances) || null,
        trade_license_number: values.trade_license_number || null,
      }
      if (editIndex !== null) {
        updateContractor(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addContractor(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteContractor(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = contractors.find((contractor) => {
      return contractor.id === id
    })

    formik.setValues({
      contractor: editControlManagers?.contractor,
      icv_percentage: editControlManagers.icv_percentage,
      long_name: editControlManagers?.long_name,
      worker_welfare_compliance_percentage: editControlManagers.worker_welfare_compliance_percentage,
      evaluation_score: editControlManagers.evaluation_score,
      number_of_grievances: editControlManagers.number_of_grievances,
      trade_license_number: editControlManagers?.trade_license_number,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'contractor',
      header: 'Contractor',
      flex: 1,
    },
    {
      accessorKey: 'long_name',
      header: 'Long Name',
      flex: 1,
    },
    {
      accessorKey: 'icv_percentage',
      header: 'ICV(%)',
      size: 110,
    },
    {
      accessorKey: 'worker_welfare_compliance_percentage',
      header: 'WW Compliance %',
      size: 190,
    },
    {
      accessorKey: 'evaluation_score',
      header: 'Evaluation Score',
      size: 170,
    },
    {
      accessorKey: 'number_of_grievances',
      header: 'No. of Grievances',
      size: 180,
    },
    {
      accessorKey: 'trade_license_number',
      header: 'Trade License',
      size: 180,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  const handleFieldChange = (field: string, value: any, max: number) => {
    // Check if the input is empty or has a valid number with at most 2 decimal places
    const decimalRegEx = /^(\d+(\.\d{0,2})?)?$/
    // Allow clearing the field by setting an empty value
    if (value === '' || value === null) {
      formik.setFieldValue(field, '')
      return
    }
    // Convert the value to a number
    const numericValue = Number(value)
    // Validate if the value is between 0 and max, and matches the decimal pattern
    if (!isNaN(numericValue) && decimalRegEx.test(value) && numericValue >= 0 && numericValue <= max) {
      formik.setFieldValue(field, value)
    }
  }

  const handleNonDecimalChange = (fieldName: string, value: string | number | null, maxNumber: number) => {
    // If value is empty or null, set field to '' (or 0 if you prefer)
    if (value === '' || value === null) {
      formik.setFieldValue(fieldName, '')
      return
    }
    const validValue = removeCommasAndConvert(value)
    const numValue = Number(validValue?.toString())
    // Allow 0 as a valid value
    if (!isNaN(numValue) && numValue >= 0 && numValue <= maxNumber) {
      formik.setFieldValue(fieldName, numValue.toString())
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Contractor'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          {/* <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}> */}
          <div className={styles.gridContainer}>
            <TextInputField
              className={styles.entityField}
              name="contractor"
              labelText={'Contractor'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.contractor}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <TextInputField
              className={`${styles.entityField} ${styles.longNameField}`}
              name="long_name"
              labelText={'Long Name'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.long_name}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <NumberInputField
              className={styles.entityField}
              name="icv_percentage"
              labelText={'ICV(%)'}
              placeholder="1,2,3..."
              value={formik?.values?.icv_percentage}
              onChange={(value) => handleFieldChange('icv_percentage', String(value) || 0, 100)}
              isUpAndDowns={false}
            />
            <NumberInputField
              className={styles.entityField}
              name="worker_welfare_compliance_percentage"
              labelText={'WW Compliance %'}
              placeholder="1,2,3..."
              value={formik?.values?.worker_welfare_compliance_percentage}
              onChange={(value) => handleFieldChange('worker_welfare_compliance_percentage', String(value) || 0, 100)}
              isUpAndDowns={false}
            />
            <NumberInputField
              className={styles.entityField}
              name="evaluation_score"
              labelText={'Evaluation score'}
              placeholder="1,2,3..."
              value={formik?.values?.evaluation_score}
              onChange={(value) => handleFieldChange('evaluation_score', value, 100)}
              isUpAndDowns={false}
            />
            <NumberInputField
              className={styles.entityField}
              name="number_of_grievances"
              labelText={'No. of Grievances'}
              placeholder="1,2,3..."
              value={formik?.values?.number_of_grievances}
              onChange={(value) => handleNonDecimalChange('number_of_grievances', value, 999999999999999)}
              isUpAndDowns={false}
            />
            <TextInputField
              className={styles.entityField}
              name="trade_license_number"
              labelText={'Trade License Number'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.trade_license_number}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.contractor?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.contractor?.trim()}
              >
                {'+ Add Contractor'}
              </Button>
            )}
          </div>
        </form>
        {contractors?.length >= 1 && (
          // <Table data={contractors} columns={columns} />
          <TanStackTable rows={sortData(contractors, 'contractor') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddContractors
