import React, { useEffect, useMemo, useState } from 'react'
import { useFormik } from 'formik'
import { toast } from 'sonner'
import styles from './AddMasterKpi.module.scss'
import { getMasterKpiTableData } from './helper'
import ComboBox from '../../../shared/combobox'
import TanStackTable from '../../../shared/tanStackTable'
import { CustomColumnDef } from '../../../shared/tanStackTable/interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import { sortData, sortStringArray } from '@/src/component/shared/tanStackTable/helper'
import TextInputField from '@/src/component/shared/textInputField'
import Typography<PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { useCreateKpis, useDeleteKpis, useGetKpis, useUpdateKpis } from '@/src/hooks/useKpis'
import { useGetTypologies } from '@/src/hooks/useTypology'
import { getValue, populateDropdownOptions, prepareDropdownOptions } from '@/src/utils/arrayUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddMasterKpi: React.FC<any> = ({ drawerStates, onClose }) => {
  const [tableData, setTableData] = useState([])
  const [selectedTopology, setSelectedTopology] = useState('')
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const { addKpiDrawer } = drawerStates
  const { typologies } = useGetTypologies(addKpiDrawer)
  //REACT-QUERY
  const { kpis } = useGetKpis(addKpiDrawer)
  // MUTATIONS
  const { mutate: addMasterKpis } = useCreateKpis()
  const { mutate: deleteKpis } = useDeleteKpis()
  const { mutate: updateKpis } = useUpdateKpis()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const typologyOption = useMemo(() => {
    return prepareDropdownOptions(typologies, 'typology')
  }, [typologies])

  // const getData = async () => {
  //   getMasterKpisApi()
  //   // const res: Record<string, any> = await getMasterTypologiesApi()
  // }

  useEffect(() => {
    if (addKpiDrawer) {
      // getData()
      formik.resetForm()
      setTableData([])
      setSelectedTopology('')
    }
  }, [addKpiDrawer])

  useEffect(() => {
    kpis?.length > 0 && selectedTopology && handleFilter(selectedTopology)
  }, [kpis])

  const formik = useFormik({
    initialValues: {
      kpisValue: '',
      master_typology_id: null,
    },
    onSubmit: async (values) => {
      const payload = { kpi: values.kpisValue, master_typology_id: values.master_typology_id }
      if (editIndex !== null) {
        updateKpis(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterKpis(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteKpis(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
  }

  const handleEditButtonClick = async (id: number) => {
    // const res: any = await getMasterKpisApi()
    const editControlKpis: any = kpis.find((kpi: any) => {
      //TODO
      return kpi.id === id
    })

    formik.setValues({
      kpisValue: editControlKpis?.kpi,
      master_typology_id: editControlKpis?.MasterTypology?.id,
    })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'project_type',
      header: 'Project Type',
      flex: 1,
    },
    {
      accessorKey: 'kpi',
      header: 'Item',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  const handleFilter = (val: any) => {
    const filterData: any = kpis?.filter((item) => {
      return item.MasterTypology?.id === val
    })
    setTableData(filterData)
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Item'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <ComboBox
              options={typologyOption}
              labelText={'Typology'}
              placeholder="Type of search..."
              value={
                formik.values.master_typology_id ? getValue(typologyOption, formik.values.master_typology_id) : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  master_typology_id: val?.value || '',
                })
                setSelectedTopology(val?.value)
                handleFilter(val?.value)
              }}
            />
            <TextInputField
              className={styles.entityField}
              name="kpisValue"
              labelText={'Item'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.kpisValue}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.kpisValue?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={!formik.values.kpisValue?.trim() || !formik.values.master_typology_id}
              >
                {'+ Add Item'}
              </Button>
            )}
          </div>
        </form>
        {kpis?.length >= 1 && (
          // <Table data={tableData} columns={columns} />
          <TanStackTable
            rows={sortData(getMasterKpiTableData(kpis), 'project_type') as any}
            columns={columns}
            isOverflow={true}
          />
        )}

        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddMasterKpi
