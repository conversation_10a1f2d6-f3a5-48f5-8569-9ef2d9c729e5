import React, { useState } from 'react'
import { useFormik } from 'formik'
import styles from './Districts.module.scss'
import { IAddDistrictsProps } from './interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { sortData } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { useCreateDistricts, useDeleteDistricts, useGetDistricts, useUpdateDistricts } from '@/src/hooks/useDistricts'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const AddDistricts: React.FC<IAddDistrictsProps> = ({ drawerStates, onClose }) => {
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const { addDistrict } = drawerStates
  //REACT-QUERY
  const { districts } = useGetDistricts(addDistrict)
  // MUTATIONS
  const { mutate: addMasterDistricts } = useCreateDistricts()
  const { mutate: deleteDistricts } = useDeleteDistricts()
  const { mutate: updateDistricts } = useUpdateDistricts()

  const handleMutationCallbacks = (successMsg: string, errorMsg: string) => ({
    onSuccess: () => {
      successToast(successMsg)
      formik.resetForm()
    },
    onError: (err: any) => {
      errorToast(err.response?.data?.message || errorMsg)
    },
  })

  const formik = useFormik({
    initialValues: { district: '' },
    onSubmit: async (values) => {
      const payload = { district: values.district }
      if (editIndex !== null) {
        updateDistricts(
          { id: editIndex, ...payload },
          handleMutationCallbacks('Record updated successfully', 'Failed to update record'),
        )
        setEditIndex(null)
      } else {
        addMasterDistricts(payload, handleMutationCallbacks('Record successfully added', 'Failed to add record'))
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    deleteDistricts(id, handleMutationCallbacks('Record deleted successfully', 'Failed to delete record'))
    setDeleteModel(null)
    setEditIndex(null)
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = districts.find((district) => {
      //TODO
      return district.id === id
    })

    formik.setValues({ district: editControlManagers?.district })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'district',
      header: 'District',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 70,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add District'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="district"
              labelText={'District'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.district}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.district?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.district?.trim()}
              >
                {'+ Add District'}
              </Button>
            )}
          </div>
        </form>
        {districts?.length >= 1 && (
          //  <Table data={districts} columns={columns} />
          <TanStackTable rows={sortData(districts, 'district') as any} columns={columns} isOverflow={true} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddDistricts
