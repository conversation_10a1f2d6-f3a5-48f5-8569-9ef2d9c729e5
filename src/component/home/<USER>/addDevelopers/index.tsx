import React, { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import styles from './AddDevelopers.module.scss'
import { IAddDevelopersProps } from '../interface'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TextInputField from '@/src/component/shared/textInputField'
import Typography<PERSON>ield from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { showCustomToast } from '@/src/component/toast/ToastManager'
import useDevelopers from '@/src/redux/developers/useDeveloper'

const AddDevelopers: React.FC<IAddDevelopersProps> = ({ drawerStates, onClose }) => {
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const {
    developers,
    getMasterDevelopersApi,
    addMasterDeveloperApi,
    updateMasterDeveloperApi,
    deleteMasterDeveloperApi,
  } = useDevelopers()
  const { addDevelopers } = drawerStates
  useEffect(() => {
    addDevelopers && getMasterDevelopersApi()
  }, [addDevelopers])

  const formik = useFormik({
    initialValues: { developer: '' },
    onSubmit: async (values) => {
      if (editIndex !== null) {
        const response: Record<string, any> = await updateMasterDeveloperApi({
          id: editIndex,
          developers: values.developer,
        })
        if (response.payload.success === true) {
          showCustomToast('Record successfully added', 'success')
          getMasterDevelopersApi()
        } else {
          showCustomToast(response.payload.response.data.message, 'error')
        }
        setEditIndex(null)
      } else {
        const response: Record<string, any> = await addMasterDeveloperApi({
          developers: values.developer,
        })
        if (response.payload.success === true) {
          showCustomToast('Record successfully added', 'success')
          getMasterDevelopersApi()
        } else {
          showCustomToast(response.payload.response.data.message, 'error')
        }
      }
      formik.resetForm()
    },
  })

  const handleDeleteEntity = async (id: number) => {
    const response: Record<string, any> = await deleteMasterDeveloperApi(id)
    if (response.payload.success === true) {
      setDeleteModel(null)
      getMasterDevelopersApi()
    }
  }

  const handleEditButtonClick = (id: number) => {
    const editControlManagers: any = developers.find((developer) => {
      //TODO
      return developer.id === id
    })

    formik.setValues({ developer: editControlManagers?.developers })
    setEditIndex(id)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'developers',
      header: 'Executing Entity',
      flex: 1,
    },
    {
      accessorKey: 'action',
      header: 'Action',
      size: 82,
      cell: (row: Record<string, any>, rowIndex?: number) => {
        return (
          <div className={styles.actionButtons}>
            <EditIcon className={styles.editRowIcon} onClick={() => handleEditButtonClick(row.row.id)} />
            <DeleteIcon onClick={() => setDeleteModel(row.row.id)} className={styles.deleteRowIcon} />
          </div>
        )
      },
    },
  ]

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Executing Entity'} />
        <div className={styles.actionButtons}>
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
            <TextInputField
              className={styles.entityField}
              name="developer"
              labelText={'Executing Entity'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.developer}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div>
            {editIndex !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.developer?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={formik.isSubmitting || !formik.values.developer?.trim()}
              >
                {'+ Add Executing Entity'}
              </Button>
            )}
          </div>
        </form>
        {developers?.length >= 1 && (
          // <Table data={developers} columns={columns} />
          <TanStackTable rows={developers as any} columns={columns} />
        )}
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDeleteEntity(deleteModel as number)}
        />
      </div>
    </div>
  )
}

export default AddDevelopers
