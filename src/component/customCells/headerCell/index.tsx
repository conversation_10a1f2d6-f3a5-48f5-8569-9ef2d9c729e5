import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import {
  ContactSupportOutlined,
  FilterAltOutlined,
  NorthOutlined,
  SouthOutlined,
  SwapVertOutlined,
  ViewWeekOutlined,
} from '@mui/icons-material'
import { flexRender } from '@tanstack/react-table'
import { generateOptions } from './generateOptions'
import styles from './HeaderCell.module.scss'
import { FilterValue, HeaderCellProps } from './interface'
import FilterPopover, { FilterOption } from '../../shared/FilterPopover'
import TableTooltip from '../../shared/tableTooltip'
import { excludeColumns } from '../../shared/tanStackTable/constant'
import { getTooltipFormulaStageTableToColumn } from '@/src/helpers/helpers'
import { getUniqueValuesFromArray } from '@/src/utils/arrayUtils'

const HeaderCell: React.FC<HeaderCellProps> = ({
  columnWidth,
  setColumnWidth,
  isResizeColumn,
  table,
  header,
  filterValue,
  rows,
  setFilterValue,
  setGridFilters,
  setManageColumnsAnchorEl,
  style,
}) => {
  const [selectedOptions, setSelectedOptions] = useState<FilterOption[]>([])
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const { column } = header
  const { isDragging, setNodeRef, transform } = useSortable({
    id: header.column.id,
  })
  const filterTimeOutRef = useRef<NodeJS.Timeout | null>(null) // Using a ref to store the timeout
  const columnDef: any = column?.columnDef || {}
  const filterWithRelation = columnDef?.filterWithRelation || []
  const subItemKey = filterWithRelation?.length > 0 ? filterWithRelation[0]?.row : null

  // Memoize the selected options to prevent unnecessary recalculations
  useEffect(() => {
    if (filterValue?.length) {
      const columnData = filterValue.find((item) => item?.colId === columnDef?.accessorKey)
      if (columnData) {
        const initialOptions = columnData.values.map((filter) => ({
          label: filter.value,
          value: filter.value,
          subItem: filter.subValues?.values?.map((sub) => ({ label: sub.value, value: sub.value })) || [],
        }))
        setSelectedOptions(initialOptions)
      }
    } else {
      setSelectedOptions([])
    }
  }, [filterValue, columnDef?.accessorKey])

  const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => setAnchorEl(event.currentTarget)
  const handlePopoverClose = () => setAnchorEl(null)

  const updateFilterValue = useCallback(
    (colFilter: FilterValue) => {
      const otherFilters = filterValue?.filter((filter) => filter.colId !== colFilter?.colId) || []
      return colFilter?.values ? [...otherFilters, { ...colFilter }] : otherFilters
    },
    [filterValue],
  )

  const checkValueExists = useCallback(
    (arr: FilterValue[], colId: string) => arr?.some((item) => item?.colId === colId && item?.values?.length > 0),
    [],
  )

  useEffect(() => {
    setColumnWidth({ ...columnWidth, [header.column.id]: header.column.getSize() })
  }, [header.column.getIsResizing()])

  const onApply = (selectedOptions: FilterOption[]) => {
    if (filterTimeOutRef.current) {
      clearTimeout(filterTimeOutRef.current) // Clear previous timeout
    }
    filterTimeOutRef.current = setTimeout(() => {
      handleApplyFilters(selectedOptions) // Apply filters after debounce
    }, 500)
  }

  const handleApplyFilters = useCallback(
    (selectedOptions: FilterOption[]) => {
      const newFilterValue = selectedOptions.map((res) => ({
        value: res.value,
        subValues:
          res.subItem?.length && subItemKey
            ? { colId: subItemKey, values: res.subItem.map((sub) => ({ value: sub.value })) }
            : null,
      }))

      const colFilter = {
        colId: column.id,
        values: newFilterValue,
        type: columnDef?.filterType,
      }
      const updatedFilterValue = updateFilterValue(colFilter)

      setFilterValue(updatedFilterValue) // Update the filter value
      setGridFilters?.(updatedFilterValue) // Optionally update the local filter values
    },
    [subItemKey, column.id, updateFilterValue, setFilterValue, setGridFilters],
  )

  const options = useMemo(() => generateOptions(columnDef, rows, subItemKey), [columnDef, rows, subItemKey])

  return (
    <>
      <div
        key={header?.id}
        ref={setNodeRef}
        className={styles.headerCell}
        style={{
          opacity: isDragging ? 0.8 : 1,
          position: 'relative',
          transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
          transition: 'width transform 0.2s ease-in-out',
          whiteSpace: 'nowrap',
          zIndex: isDragging ? 1 : 0,
          width: columnWidth?.[header.column.id] ? columnWidth[header.column.id] : header.column.getSize(),
          textAlign: (header?.column?.columnDef as any)?.align || 'left',
          flex: (header?.column?.columnDef as any).flex,
          backgroundColor: (header?.column?.columnDef as any)?.backgroundColor,
          ...style,
        }}
        onDoubleClick={() => header.column.resetSize()}
      >
        {header?.id === 'isRibbon' ? (
          !excludeColumns.includes(header?.id) && (
            <div className={styles.icons} onClick={handlePopoverOpen}>
              <FilterAltOutlined />
            </div>
          )
        ) : header?.id === 'actionCol' ? (
          <div style={{ width: '100%' }}>
            <ViewWeekOutlined
              className={styles.columnManageIcon}
              onClick={(e: { currentTarget: any }) => setManageColumnsAnchorEl(e.currentTarget)}
            />
          </div>
        ) : (
          <TableTooltip
            title={getTooltipFormulaStageTableToColumn(header.column)}
            arrow
            PopperProps={{ sx: { '& .MuiTooltip-tooltip': { maxWidth: 430 } } }}
          >
            <div
              className={`${styles.ellipsis} ${checkValueExists(filterValue as FilterValue[], header.id) ? styles.boldHeader : styles.header}`}
            >
              {header?.isPlaceholder ? null : flexRender(header?.column?.columnDef?.header, header?.getContext())}
            </div>
          </TableTooltip>
        )}
        {header?.id !== 'isRibbon' && !excludeColumns.includes(header?.id) && (
          <div className={styles.icons}>
            {header?.column?.getCanSort() &&
              (header?.column?.getIsSorted() === 'asc' ? (
                <NorthOutlined onClick={header?.column?.getToggleSortingHandler()} />
              ) : header?.column?.getIsSorted() === 'desc' ? (
                <SouthOutlined onClick={header?.column?.getToggleSortingHandler()} />
              ) : (
                <SwapVertOutlined onClick={header?.column?.getToggleSortingHandler()} />
              ))}
            <div onClick={handlePopoverOpen}>
              <FilterAltOutlined />
            </div>
          </div>
        )}
        {isResizeColumn && (
          <div
            {...{
              onDoubleClick: () => header.column.resetSize(),
              onMouseDown: header.getResizeHandler(),
              onTouchStart: header.getResizeHandler(),
              className: `${styles.resizer} ${table.options.columnResizeDirection} ${
                header.column.getIsResizing() ? styles.isResizing : ''
              }`,
              style: {
                transform: header.column.getIsResizing()
                  ? `translateX(${
                      (table.options.columnResizeDirection === 'rtl' ? -1 : 1) *
                      (table.getState().columnSizingInfo.deltaOffset ?? 0)
                    }px)`
                  : '',
              },
            }}
          />
        )}
      </div>

      <FilterPopover
        isBlank={!columnDef?.require && columnDef?.filterType !== 'boolean'}
        type={columnDef?.filterType}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        options={options}
        onApply={onApply}
        selectedOptions={selectedOptions}
        setSelectedOptions={setSelectedOptions}
        popOverWidth={columnDef?.popOverWidth}
      />
    </>
  )
}

export default HeaderCell
