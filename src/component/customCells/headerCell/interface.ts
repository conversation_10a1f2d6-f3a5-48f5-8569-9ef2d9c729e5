import { CSSProperties } from 'react'
import { Head<PERSON> } from '@tanstack/react-table'

interface Filter {
  value: string
  subValues?: { colId: string; values: { value: string }[] } | null
}

export interface FilterValue {
  colId: string
  values: Filter[]
  type: string
}

export interface HeaderCellProps {
  columnWidth: any
  setColumnWidth: any
  table: any
  isResizeColumn?: boolean
  header: Header<any, any>
  rows: any
  filterValue?: FilterValue[]
  setFilterValue: React.Dispatch<React.SetStateAction<FilterValue[]>>
  setManageColumnsAnchorEl: (args: any) => void
  setGridFilters?: React.Dispatch<React.SetStateAction<FilterValue[]>>
  style?: CSSProperties | undefined
}
