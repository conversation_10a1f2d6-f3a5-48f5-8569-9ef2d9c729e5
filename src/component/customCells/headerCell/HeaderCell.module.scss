@import '/styles/breakpoints.scss';

.headerCell {
  // min-height: 10px;
  padding: 8px;
  font-size: 12px;
  position: relative;
  display: flex;
  gap: 10px;
  align-items: center;
  box-sizing: border-box;

  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &:hover {
    .icons svg {
      visibility: visible;
    }
  }
  .icons {
    display: flex;
    gap: 4px;
    right: 0px;
    top: 50%;
    width: 42px;
    @media (max-width: 1440px) {
      position: unset;
      transform: unset;
    }
    svg {
      width: 16px;
      height: 16px;
      cursor: pointer;
      visibility: hidden;
    }
  }
}

.columnManageIcon {
  cursor: pointer;
}

.menuRoot {
  .menuPaper {
    min-width: 500px;
    max-width: 500px;
    margin-top: 8px;
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.boldHeader {
  color: darkblue;
  font-weight: bold !important;
}
.header {
  font-weight: bold !important;
}
.resizer {
  height: 100%;
  right: 0;
  position: absolute;
  top: 0;
  // height: 100%;
  width: 5px;
  background: rgba(0, 0, 0, 0.5);
  cursor: col-resize;
  user-select: none;
  touch-action: none;
}

.resizer.ltr {
  height: 10px;
  background-color: black;
  width: 10px;
  right: 0;
}

.resizer.rtl {
  height: 10px;
  background-color: black;
  width: 10px;
  left: 0;
}

.resizer.isResizing {
  background: rgb(0, 0, 253);
  opacity: 1;
}

@include respond-to('mobile') {
  .resizer {
    opacity: 0;
  }
}

@include respond-to('tablet') {
  .resizer {
    opacity: 0;
  }
}

@media (hover: hover) {
  .resizer {
    opacity: 0;
  }

  *:hover > .resizer {
    opacity: 1;
  }
}
.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
}
.filterComponent {
  width: 100%;
  margin: 10px;
}
.field {
  display: flex;
  // border-bottom: 1px solid #888888;
  gap: 10px;
  align-items: center;
  .name {
    font-size: 14px;
  }
}
.action {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
}
.textField {
  position: sticky;
  top: 0;
}

.container {
  margin: 5px;
  overflow: auto;
  height: 100%;
  max-height: 590px;
}
