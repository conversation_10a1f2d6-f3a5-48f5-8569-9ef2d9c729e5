.wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}

.highlightEditableCell {
  padding: 5px;
  background-color: #f0f8ff !important; // light blue or any color you prefer
  border-radius: 3px;
  outline: 1px solid #2865dc !important; // primary blue or any color you prefer
  width: 100%;
}

.highlightCheckBoxEditableCell {
  background-color: #f0f8ff !important; // light blue or any color you prefer
  border: 1px solid #2865dc !important;
  border-radius: 3px;

  > span {
    padding: 0 !important;
    border-radius: 3px;
  }
}

.checkboxCell {
  > span {
    padding: 0 !important;
    border-radius: 3px;
  }
  border-radius: 3px;
}

.loaderWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}