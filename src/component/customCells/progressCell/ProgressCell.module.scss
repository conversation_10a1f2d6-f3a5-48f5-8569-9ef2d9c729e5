.progressCell {
  display: flex;
  gap: 4px;
  align-items: center;
  // flex-direction: column;
  .progressBar {
    display: flex;
    align-items: center;
    height: 10px;
    background-color: #fff;
    border-radius: 6px;
    // max-width: 250px;
    // min-width: 100px;
    width: 150px;
    padding: 2px;
    > span {
      width: 100%;
    }
  }
  .text {
    text-align: left;
    flex-grow: 1;
  }
}
.textField {
  width: fit-content !important;
  > div > div {
    background-color: #ffffff !important;
    > input {
      font-size: 12px !important;
      padding: 0px !important;
      width: 56px !important;
    }
  }
}
.highlightEditableCell {
  padding: 5px;
  background-color: #f0f8ff !important; // light blue or any color you prefer
  border-radius: 3px;
  outline: 1px solid #2865dc !important; // primary blue or any color you prefer
}

.highlightTextEditableCell {
  width: fit-content !important;
  > div > div {
    background-color: #f0f8ff !important;
    > input {
      font-size: 12px !important;
      padding: 0px !important;
      width: 56px !important;
    }
  }
}
