import React from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { DragIndicator } from '@mui/icons-material' // Or any other drag icon

type DragCellProps = {
  rowId: string
}

const DragCell: React.FC<DragCellProps> = ({ rowId }) => {
  const { attributes, listeners } = useSortable({ id: rowId })

  return (
    <div {...listeners} {...attributes} style={{ cursor: 'grab' }}>
      <DragIndicator />
    </div>
  )
}

export default DragCell
