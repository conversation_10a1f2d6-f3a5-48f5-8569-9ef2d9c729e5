@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.field {
  display: flex;
  flex-grow: 1;
  .fieldLabel {
    min-height: 23px;
    white-space: nowrap;
    color: $DARK_200;
    max-width: 209px;
    width: 100%;
    // min-width: 209px;
    padding: 9px 0 6px 8px;
    border-bottom: 1px solid $LIGHT_200;
  }

  .richTextLabel {
    display: flex;
    flex-direction: row;
    gap: 2px;
    svg {
      position: relative;
      top: -2px;
      // left: 99px;
      width: 15px;
      height: 15px;
    }
    @include respond-to('mobile') {
      min-width: 100px !important;
      max-width: 120px !important;
      white-space: unset !important;
    }
    @include respond-to('tablet') {
      min-width: 100px !important;
      max-width: 140px !important;
      white-space: unset !important;
    }
    @include respond-to('laptop') {
      min-width: 140px !important;
      max-width: 150px !important;
      white-space: unset !important;
    }
  }

  .fieldValue {
    width: 100%;
    display: flex;
    flex: 1;
    color: $DARK;
    word-wrap: break-word;
    .dot {
      min-height: 22px;
      padding: 9px 0 6px 30px;
      border-bottom: 1px solid $LIGHT_200;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
    }

    .value {
      min-height: 22px;
      padding: 9px 0 6px 8px;
      width: 100%;
      border-bottom: 1px solid $LIGHT_200;
    }
    .textAreaValue {
      padding: 6px 0 6px 8px;
      width: 100%;
      border-bottom: 1px solid $LIGHT_200;
    }
    .textEditorValue {
      width: 100%;
      border-bottom: 1px solid $LIGHT_200;
    }
  }
}

.textField {
  > div > div {
    background: $FOCUS_TEXTFIELD_BG !important;
    border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    border-radius: 0px;
    padding-right: 0px !important;
    > input {
      min-height: 25px;
      padding: 6px 0 6px 8px !important;
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
      &:focus {
        background: $FOCUS_TEXTFIELD_BG !important;
      }
    }
  }
}
.disableTextField {
  > div > div {
    background-color: $WHITE !important;
    border-radius: 0px;
    padding-right: 0px !important;
    > input {
      min-height: 25px;
      padding: 6px 0 6px 8px !important;
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
    }
  }
}
.textArea {
  width: 100%;
  border-radius: 0px;
  display: flex;
  padding: 4px;
  :focus {
    border-radius: 0px;
  }
  > textarea {
    max-height: 85px !important;
    overflow-y: scroll !important;
    background: $FOCUS_TEXTFIELD_BG !important;
    border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    color: $DARK;
    border-radius: 0px !important;
    padding: 6px 0 0px 8px !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    height: 65px !important;
    &::-webkit-scrollbar {
      width: 3px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #dddddd;
      border-radius: 5px;
      width: 3px;
    }
  }
}

.highlightField {
  width: 100%;
  div > div {
    max-height: 38px !important;
    border-radius: 0px !important;
    border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    background: $FOCUS_TEXTFIELD_BG !important;
    input {
      color: $DARK;
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
    }
    > div {
      border: none !important;
    }
  }
}

.inputField {
  width: 100%;
  div > div {
    max-height: 38px !important;
    background-color: $WHITE !important;
    border-radius: 0px !important;
    // border-bottom: 1px solid #eaeaea;
    height: 100% !important;
    input {
      color: $DARK;
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
    }
    > div {
      border: none !important;
    }
  }
}

.endAdornmentIcon {
  path {
    fill: #444444;
  }
}

.comboBox {
  .focusCompoBox {
    border-radius: 0px;
    background: #f0f8ff !important;
    border-bottom: 1px solid rgb(40 101 220) !important;
  }
  > div > div {
    min-height: 38px !important;
    background: $WHITE !important;
    border-radius: 0px;
    padding: 6px 0 6px 8px !important;
    background: $FOCUS_TEXTFIELD_BG !important;
    border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    height: 100% !important;
    > div {
      padding: 0px !important;
      padding-right: 5px !important;
      > div {
        color: $DARK !important;
        font-size: 12px !important;
        font-weight: 400 !important;
        padding: 0px !important;
        line-height: 18px !important;
      }
      svg {
        margin-right: -2px !important;
      }
    }
    &:focus {
      border-radius: 0px;
      background: $FOCUS_TEXTFIELD_BG !important;
      border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    }
  }
}
.disableComboBox {
  .focusCompoBox {
    border-radius: 0px;
    background: #f0f8ff !important;
    border-bottom: 1px solid rgb(40 101 220) !important;
  }
  > div > div {
    min-height: 38px !important;
    background: $WHITE !important;
    border-radius: 0px;
    padding: 6px 0 6px 8px !important;
    border-bottom: 1px solid $DARK !important;
    > div {
      padding: 0px !important;
      padding-right: 5px !important;
      > div {
        color: $DARK !important;
        font-size: 12px !important;
        font-weight: 400 !important;
        padding: 0px !important;
        line-height: 18px !important;
      }
    }
    &:focus {
      border-radius: 0px;
    }
  }
}

.checkBox {
  width: 100%;
  border-radius: unset !important;

  > span {
    background: $FOCUS_TEXTFIELD_BG !important;
    border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    justify-content: flex-start;
  }
}

.checkBoxBorder {
  width: 100%;
  border-radius: unset !important;
  > span {
    border-bottom: 1px solid $LIGHT_200 !important;
    justify-content: flex-start;
  }
}

.disableDatePicker {
  border-bottom: 1px solid $LIGHT_200 !important;
  > div {
    border-bottom: 0px solid !important;
  }

  input {
    color: #000 !important;
    -webkit-text-fill-color: #000 !important;
  }
}
.multiSelectField {
  > div > div {
    text-align: start;
    align-items: start;
    height: 51px !important;
    background: $FOCUS_TEXTFIELD_BG !important;
    border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    border-radius: 0px;
    padding-right: 0px !important;
    padding-top: 2px !important;
    padding-bottom: 2px !important;
    height: 100% !important;
    > input {
      display: block;
      height: 33px !important;
      padding: 6px 0 6px 8px !important;
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
    }
  }
}

.borderBottomNone {
  border-bottom: none !important;
}
