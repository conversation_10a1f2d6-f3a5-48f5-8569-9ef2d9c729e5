import React, { ReactElement, useRef, useEffect } from 'react'
import { KeyboardArrowDownRounded, KeyboardArrowLeftRounded, KeyboardArrowRightRounded } from '@mui/icons-material'
import { LocalizationProvider, PickersActionBarProps } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { DatePicker as MuiDatePicker } from '@mui/x-date-pickers/DatePicker'
import { format, isBefore } from 'date-fns'
import styles from './CollapseDatePicker.module.scss'
import Button from '@/src/component/shared/button'
import TypographyField from '@/src/component/shared/typography'
import CalendarIcon from '@/src/component/svgImages/calenderIcon'
import { DARK_200 } from '@/src/constant/color'
import { parseDateFromString } from '@/src/utils/dateUtils'
import { intoToast } from '@/src/utils/toastUtils'

interface MuiDatePickerProps {
  labelText?: string
  value: string | number | Date | null
  onChange?: (date: any) => void
  className?: string
  minDate?: string | number | Date | null
  maxDate?: string | number | Date
  error?: boolean
  helperText?: string
  disableEditable?: boolean
  onAccept?: (value: any) => void
  placeholder?: string
  onError?: (reason?: string | null) => void
  shouldDisableDate?: any
  disableWeekends?: boolean
  disabled?: boolean
  required?: boolean
  name?: string
  sx?: any
  onBlur?: any
  open?: boolean
  onOpen?: () => void
  onClose?: () => void
  actionButtons?: boolean
  CustomDay?: any
  shouldCloseOnSelect?: boolean
}

const CollapseDatePicker: React.FC<MuiDatePickerProps> = ({
  labelText,
  open,
  onOpen,
  onClose,
  value,
  onChange = () => null,
  className,
  minDate,
  maxDate,
  error = false,
  helperText,
  disableEditable = false,
  onAccept,
  onBlur,
  placeholder = '',
  onError = () => null,
  shouldDisableDate,
  disableWeekends = false,
  actionButtons = false,
  disabled = false,
  required = false,
  name = '',
  sx,
  CustomDay,
  ...props
}): ReactElement => {
  const dateValue = value ? parseDateFromString(value as string) : null
  const pickerRef = useRef<HTMLDivElement>(null)
  const defaultValue = useRef(dateValue)

  useEffect(() => {
    const handleScroll = (event: Event) => {
      const popper = document.querySelector('.MuiPickersPopper-root')
      if (popper && popper.contains(event.target as Node)) {
        return
      }
      if (onClose) {
        onClose()
      }
    }

    if (open) {
      window.addEventListener('scroll', handleScroll, true)
    }

    return () => {
      window.removeEventListener('scroll', handleScroll, true)
    }
  }, [open, onClose])

  const CustomActionBar = ({ onAccept, onCancel }: PickersActionBarProps) => (
    <div className={styles.buttonActions} style={{ display: 'flex', gap: '10px' }}>
      <Button
        color="secondary"
        onClick={() => {
          if (onCancel) onCancel()
          if (onClose) onClose()
        }}
      >
        Cancel
      </Button>
      <Button
        onClick={() => {
          //   if (onAccept) onAccept(dateValue)
          if (onClose) onClose()
        }}
      >
        Add
      </Button>
    </div>
  )

  const shouldDisableDateHandler = (date: any) => {
    return disableWeekends && (date.getDay() === 0 || date.getDay() === 6)
  }

  const handleDateChange = (val: any) => {
    if (!val) {
      onChange(null)
      return
    }

    if (val) {
      const date = new Date(val)
      const year = date.getFullYear()

      // Check for complete valid date
      const isCompleteDate =
        !isNaN(date.getTime()) && year.toString().length === 4 && date.getMonth() < 12 && date.getDate() <= 31

      // Allow typing in progress
      if (!isCompleteDate) {
        onChange(val)
        return
      }

      const isBeforeMinDate = minDate && isBefore(val, minDate)
      const isDisabledDate = shouldDisableDateHandler(val)

      if (isBeforeMinDate) {
        onChange(null)
        intoToast(`Date must be greater than or equal to ${format(minDate as string, 'dd-MM-yyyy')}`, {
          style: { minWidth: 380 },
        })
      } else if (isDisabledDate) {
        onChange(null)
        intoToast('Weekend date can not be selected')
      } else {
        onChange(val)
      }
    } else {
      onChange(null)
    }
  }

  return (
    <div style={{ width: '100%' }} className={`${styles.datePickerWrapper} ${className}`} ref={pickerRef}>
      {labelText && (
        <div>
          <TypographyField style={{ color: DARK_200 }} variant="caption" text={labelText} />
        </div>
      )}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <MuiDatePicker
          {...props}
          open={open}
          onClose={() => {
            onChange(defaultValue?.current)
            if (onClose) onClose()
          }}
          format="dd-MM-yyyy"
          closeOnSelect={false}
          className={className}
          value={dateValue}
          onChange={(val) => handleDateChange(val ? val : null)}
          disabled={disabled}
          onAccept={(value) => {
            if (onAccept) onAccept(value)
            if (onClose) onClose()
          }}
          shouldDisableDate={(date) => shouldDisableDateHandler(date)}
          slots={{
            actionBar: CustomActionBar,
            openPickerIcon: () => <CalendarIcon />,
            leftArrowIcon: () => <KeyboardArrowLeftRounded />,
            rightArrowIcon: () => <KeyboardArrowRightRounded />,
            switchViewIcon: () => <KeyboardArrowDownRounded />,
            day: CustomDay && CustomDay,
          }}
          slotProps={{
            layout: { sx: { display: 'block', textAlign: 'end' } },
            textField: { sx: { '& .Mui-error': { borderBottom: '0px solid #ffffff' }, ...sx } },
            openPickerButton: {
              onClick: () => {
                onOpen?.()
              },
              onMouseDown: (e) => {
                e.preventDefault()
              },
            },
          }}
          minDate={minDate}
        />
      </LocalizationProvider>
    </div>
  )
}

export default CollapseDatePicker
