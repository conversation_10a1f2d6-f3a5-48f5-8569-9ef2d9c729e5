import React, { useMemo } from 'react'
import { FormControlLabel, FormLabel, Radio, RadioGroup, Switch } from '@mui/material'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './KpiDrawer.module.scss'
import AutoCompleteDropdown from '@/src/component/shared/AutocompleteDropdown'
import Button from '@/src/component/shared/button'
import Checkbox from '@/src/component/shared/checkbox'
import ComboBox from '@/src/component/shared/combobox'
import TextInput<PERSON>ield from '@/src/component/shared/textInputField'
import Typography<PERSON>ield from '@/src/component/shared/typography'
import { useGetTypologies } from '@/src/hooks/useTypology'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectKpi from '@/src/redux/projectKpi/useProjectKpi'
import useStatus from '@/src/redux/status/useStatus'
import { getUniqueValuesById, getValue, populateDropdownOptions } from '@/src/utils/arrayUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const KpiDrawer: React.FC<any> = ({ onClose, filterProjectKpiData, project, editId, setEditId, statuses }) => {
  const router = useRouter()
  const { currentPeriod } = useMasterPeriod()
  const { getProjectKpisApi, projectKpis: dirtyProjectKpis, addProjectKpiApi, updateProjectKpiApi } = useProjectKpi()

  const projectKpis = useMemo(() => {
    return (
      dirtyProjectKpis
        ?.filter((item: any) => !!item?.kpi)
        ?.map((item: any) => {
          if (item?.kpi_value === '') {
            return { ...item, kpi_value: '0' }
          }
          return item
        }) || []
    )
  }, [dirtyProjectKpis])

  const kpiOption = useMemo(() => {
    return populateDropdownOptions(filterProjectKpiData, 'kpi')
  }, [filterProjectKpiData])

  const phaseOptionsData = useMemo(() => {
    return populateDropdownOptions(
      statuses.filter((item: any) => item?.project_name === router.query.slug),
      'LookupProjectToPhase',
    )
  }, [statuses, router.query.slug])

  const phaseOptions = useMemo(() => {
    const uniquePhases = getUniqueValuesById(phaseOptionsData.flat())
    return uniquePhases
      ?.map((item: any) => {
        return {
          label: item?.phase,
          value: item?.id,
        }
      })
      .filter((item: any) => !!item?.label && item?.label !== 'Overall')
  }, [phaseOptionsData])

  const initialValues = useMemo(() => {
    const editData = projectKpis?.find((item: any) => item.id === editId)
    // setSelectedOption(editId ? editData?.kpi : '')
    return {
      kpi: editId ? editData?.kpi : '',
      value: editId ? editData?.kpi_value : 0,
      lookup_project_to_phase_id: editData?.LookupProjectToPhase.id,
      MasterTypology: project.MasterTypology,
      typologyCheck: editData?.typologyCheck ?? true,
      is_executive_project: editId ? Boolean(editData?.is_executive_kpi) : Boolean(false),
    }
  }, [editId, projectKpis])

  const formik = useFormik({
    initialValues: initialValues,
    enableReinitialize: true,
    onSubmit: async (values) => {
      const existingProjectKpi: any = projectKpis.find((item: any) => item.kpi === values.kpi)
      if (editId) {
        const res: Record<string, any> = await updateProjectKpiApi({
          id: editId,
          data: {
            period: currentPeriod,
            is_executive_kpi: Boolean(values.is_executive_project),
            project_name: project?.project_name,
            lookup_project_to_phase_id: values?.lookup_project_to_phase_id,
            master_typology_id: values?.MasterTypology.id,
            kpi: values.kpi,
            kpi_value: values.value,
            last_updated: new Date(),
          },
        })
        if (res.payload.success) {
          successToast(res?.payload?.message || 'KPI Updated Successfully')
        }
        if (!res.payload.success) {
          errorToast(res?.payload?.response.data.message || 'Error')
        }
      } else {
        const res: Record<string, any> = await addProjectKpiApi({
          period: currentPeriod,
          project_name: project?.project_name,
          master_typology_id: values?.MasterTypology.id,
          lookup_project_to_phase_id: values?.lookup_project_to_phase_id,
          kpi: values.kpi,
          kpi_value: values.value,
          last_updated: new Date(),
          is_executive_kpi: Boolean(values.is_executive_project),
        })
        if (!res.payload.success) {
          errorToast(res?.payload?.response.data.message || 'Error')
        } else {
          successToast(res?.payload?.message || 'KPI Added Successfully')
        }
      }
      await getProjectKpisApi({ period: currentPeriod, project_name: project?.project_name as string })
      setEditId(null)
      formik.resetForm()
      onClose()
    },
  })

  const handleChange = (newValue: string) => {
    formik.setValues({
      ...formik.values,
      kpi: newValue || '',
    })
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <TypographyField className={styles.headerTitle} variant="subheadingSemiBold" text={'Add Item'} />
        <div className={styles.actionButtons}>
          <Button
            className={styles.closeButton}
            color="secondary"
            onClick={() => {
              formik.resetForm()
              onClose()
            }}
          >
            X Close
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <form className={styles.form} onSubmit={formik.handleSubmit}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '24px',
            }}
          >
            <div className={styles.phaseTypologyRadioGroup}>
              {/* Label for clarity */}
              <FormLabel component="legend">Categorize by:</FormLabel>

              {/* Radio Group */}
              <RadioGroup
                row // 👈 makes radios horizontal with spacing
                name="toggleOption"
                value={formik.values.typologyCheck ? 'typology' : 'phase'}
                onChange={(event) => {
                  const selected = event.target.value
                  formik.setValues({
                    ...formik.values,
                    typologyCheck: selected === 'typology',
                    lookup_project_to_phase_id: null,
                  })
                }}
              >
                <FormControlLabel value="typology" control={<Radio />} label="Typology" />
                <FormControlLabel value="phase" control={<Radio />} label="Phase" />
              </RadioGroup>

              {/* Conditionally render dropdown */}
              {formik.values.typologyCheck ? (
                <TextInputField
                  className={styles.entityField}
                  name="value"
                  labelText={'Typology'}
                  variant={'outlined'}
                  value={formik.values?.MasterTypology?.typology}
                  disabled={true}
                />
              ) : (
                <ComboBox
                  labelText="Phase"
                  placeholder="Select Phase"
                  options={phaseOptions}
                  value={
                    formik.values.lookup_project_to_phase_id
                      ? getValue(phaseOptions, formik.values.lookup_project_to_phase_id)
                      : null
                  }
                  clearIcon={true}
                  onChange={(val) => {
                    formik.setValues({
                      ...formik.values,
                      lookup_project_to_phase_id: val?.value || '',
                    })
                  }}
                />
              )}
            </div>

            <AutoCompleteDropdown
              label="Item *"
              placeHolder={'Type something..'}
              options={kpiOption}
              value={formik.values.kpi as string}
              onChange={handleChange}
              sx={false}
              // className={styles.}
            />

            <TextInputField
              className={styles.entityField}
              name="value"
              labelText={'Value *'}
              placeholder="Type something ..."
              variant={'outlined'}
              value={formik.values.value}
              onChange={(e) => {
                const inputValue = e.target.value
                // Filter out non-digit characters
                const val = inputValue.replace(/\D/g, '')
                if (val.length > 15) return
                formik.setFieldValue('value', parseFloat(val))
              }}
              onBlur={formik.handleBlur}
              type="number"
            />
          </div>

          <div>
            {project?.is_executive_project ? (
              <>
                <span className={styles.label}>Executive Project</span>
                <Checkbox
                  name="is_executive_project"
                  checked={Boolean(formik.values.is_executive_project)}
                  onChange={(e) => {
                    formik.setFieldValue('is_executive_project', e.target.checked)
                  }}
                />
              </>
            ) : null}
          </div>
          <div>
            {editId !== null ? (
              <Button
                className={styles.addProjectButton}
                type="submit"
                disabled={formik.isSubmitting || !formik.values.kpi?.trim()}
              >
                {'Update'}
              </Button>
            ) : (
              <Button
                className={styles.addProjectButton}
                color="secondary"
                type="submit"
                disabled={!formik.values?.value || !formik.values?.kpi}
              >
                {'+ Add Item'}
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}

export default KpiDrawer
