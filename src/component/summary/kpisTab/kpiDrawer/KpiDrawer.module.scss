@import '/styles/color.scss';

.container {
  .header {
    border-bottom: 1px solid $LIGHT_200;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;
    .headerTitle {
      text-align: left;
      padding: 13px 0 13px 20px;
      color: $BLACK;
    }

    .actionButtons {
      display: flex;
      gap: 20px;

      .closeButton {
        padding: 8px 10px;
      }
    }
  }

  .content {
    margin: 20px 20px 0px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .addProjectButton {
      padding: 8px 10px;
    }
  }

  .entityField {
    > div > div > input {
      color: $BLACK;
      border-radius: 4px;
      min-width: 400px;
      color: #444444 !important;

      &:focus {
        border-radius: 4px;
        background: $FOCUS_TEXTFIELD_BG;
        border: 1px solid $FOCUS_TEXTFIELD_BORDER;
      }
    }
  }
}
.form {
  display: flex;
  flex-direction: column;

  .label {
    font-family: Poppins;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: $GRAY_500;
    margin-top: 24px;
  }
}
.actionButtons {
  display: flex;
  gap: 5px;
  .editRowIcon,
  .deleteRowIcon {
    cursor: pointer;
  }
}

.phaseTypologySwitchPosition {
  display: flex;
  align-items: center;
  justify-content: end;
  .switchPosition {
    top: 10px;
    position: relative;
  }
}
