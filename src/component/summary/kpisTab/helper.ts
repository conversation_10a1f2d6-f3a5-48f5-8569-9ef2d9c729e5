import { IProjectKpi } from '@/src/redux/projectKpi/interface'

/**
 * Original function: Splits KPIs into phase vs typology
 */
export const splitKpisByPhaseAndTypology = (combinedKpiData: IProjectKpi[]) => {
  const phaseArray: IProjectKpi[] = []
  const typologyArray: IProjectKpi[] = []

  combinedKpiData.forEach((item) => {
    if (item?.LookupProjectToPhase && Array.isArray(item.LookupProjectToPhase) === false) {
      phaseArray.push(item)
    } else {
      typologyArray.push(item)
    }
  })

  return { phaseArray, typologyArray }
}
