import React, { useState } from 'react'
import { DndContext, closestCenter } from '@dnd-kit/core'
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { WarningAmberOutlined } from '@mui/icons-material'
import DragIndicatorIcon from '@mui/icons-material/DragIndicator'
import Checkbox from '@mui/material/Checkbox'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import { SortableItem } from '..'
import TextInputField from '../../../shared/textInputField'
import TypographyField from '../../../shared/typography'
import CancelRoundedIcon from '../../../svgImages/cancelRoundedIcon'
import DeleteIcon from '../../../svgImages/deleteIcon'
import EditIcon from '../../../svgImages/editIcon'
import SaveIcon from '../../../svgImages/saveIcon'
import styles from '../KpisTab.module.scss'
import useProjectKpi from '@/src/redux/projectKpi/useProjectKpi'
import { errorToast, successToast } from '@/src/utils/toastUtils'

const KpiCards = ({
  sensors,
  kpiData,
  allkpiData,
  setKpiData,
  view,
  inputValues,
  setInputValues,
  isEditForUser,
  formatNumberWithCommas,
  project,
  checkboxValues,
  currentPeriod,
  handleCheckboxChange,
  setDeleteModel,
}: any) => {
  const [editKpis, setEditKpis] = useState<any[]>([])
  const {
    getProjectKpisApi,
    projectKpis: dirtyProjectKpis,
    addProjectKpiApi,
    deleteProjectKpiApi,
    updateProjectKpiApi,
    sortProjectKpiApi,
  } = useProjectKpi()
  const router = useRouter()
  const handleDragEnd = async (event: any) => {
    const { active, over } = event
    const cloneTableData: any[] = JSON.parse(JSON.stringify([...allkpiData]))

    if (active.id !== over?.id) {
      const oldIndex = cloneTableData.findIndex((item) => item.kpi === active.id)
      const newIndex = cloneTableData.findIndex((item) => item.kpi === over?.id)

      const newItems = arrayMove(cloneTableData, oldIndex, newIndex)

      // Update sorting order
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        kpi_sorting_order: index + 1,
      }))

      setKpiData(updatedItems)

      const newPayload = updatedItems?.map((item: any, index: number) => ({
        id: item?.id,
        kpi_sorting_order: item?.kpi_sorting_order,
      }))

      await sortProjectKpiApi({ period: currentPeriod, KPIRecords: newPayload })
    }
  }

  const handleInputChange = (kpi: string, value: string) => {
    const rawValue = value.replace(/,/g, '').replace(/\D/g, '')
    setInputValues((prevValues: any) => ({
      ...prevValues,
      [kpi]: rawValue,
    }))
  }

  const handleUpdate = async (kpi: any) => {
    const updatedKpiValue = inputValues[kpi.kpi]?.toString()?.includes(',')
      ? inputValues[kpi.kpi].replace(/,/g, '')
      : inputValues[kpi.kpi]
    const res: any = kpi.id
      ? await updateProjectKpiApi({
          id: kpi.id,
          data: {
            period: currentPeriod,
            project_name: project?.project_name,
            lookup_project_to_phase_id: kpi?.lookup_project_to_phase_id,
            master_typology_id: kpi?.master_typology_id,
            kpi: kpi.kpi,
            kpi_value: parseFloat(String(updatedKpiValue)),
            is_executive_kpi: Boolean(checkboxValues[kpi.kpi]),
            last_updated: new Date(),
          },
        })
      : await addProjectKpiApi({
          period: currentPeriod,
          project_name: project?.project_name,
          lookup_project_to_phase_id: kpi?.lookup_project_to_phase_id,
          master_typology_id: kpi?.master_typology_id,
          kpi: kpi.kpi,
          kpi_value: parseFloat(String(updatedKpiValue)),
          is_executive_kpi: Boolean(checkboxValues[kpi.kpi]),
          last_updated: new Date(),
        })

    if (!res.payload.success) {
      errorToast(res?.payload?.response?.data?.message || 'Failed')
      return
    }
    if (res.payload.success) {
      successToast(res?.payload?.message || 'KPI Updated Successfully')
    }

    await getProjectKpisApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    setEditKpis([])
  }

  const handleEdit = (kpi: any) => {
    setEditKpis((prev: any) => [...prev, kpi.id])
    // setEditKpis((prev) => [...prev, kpi.editId])
  }

  const handleDiscard = (kpi: any) => {
    setEditKpis((prev: any) => prev.filter((id: any) => id !== kpi.id))
    setInputValues((prevValues: any) => ({
      ...prevValues,
      [kpi.kpi]: formatNumberWithCommas(kpi.kpi_value || 0),
    }))
    // setEditKpis((prev) => prev.filter((id) => id !== kpi.editId))
  }

  return (
    <div className={styles.cards}>
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext
          items={kpiData.map((item: any) => item.kpi)}
          strategy={view === 'Card View' ? horizontalListSortingStrategy : verticalListSortingStrategy}
        >
          <div className={view === 'Card View' ? styles.kpiCards : styles.listView}>
            {kpiData
              .sort((a: any, b: any) => a?.kpi_sorting_order - b?.kpi_sorting_order)
              .map((kpi: any, index: number) => {
                const showInput = view === 'Card View' ? true : inputValues[kpi.kpi] || editKpis.includes(kpi.id)
                return (
                  <SortableItem
                    key={index}
                    id={kpi.kpi}
                    className={`${view === 'Card View' ? styles.card : styles.listItem} ${
                      editKpis.includes(kpi.id) ? styles.highlightedField : ''
                    }`}
                  >
                    {({ attributes, listeners }: any) => (
                      <div>
                        <span
                          className={styles.dragButton}
                          {...attributes}
                          style={{
                            top: view === 'Card View' ? '18px' : '50%',
                            transform: view === 'Card View' ? '' : 'translateY(-40%)',
                          }}
                          {...listeners}
                        >
                          <DragIndicatorIcon />
                        </span>
                        <div className={styles.header}>
                          <div className={view === 'Card View' ? styles.cardDetails : styles.listItemDetails}>
                            <TypographyField
                              variant={'body1'}
                              text={kpi.kpi}
                              className={styles.headerDetails}
                              style={{
                                width: view === 'Card View' ? '200px' : `${kpi.kpi.length + 1}ch`,
                                whiteSpace: view === 'List View' ? 'nowrap' : 'wrap',
                              }}
                            />
                            {showInput && (
                              <TextInputField
                                variant="outlined"
                                className={`${styles.textField} ${
                                  editKpis.includes(kpi.id) ? styles.highlightedTextField : ''
                                } `}
                                disabled={!editKpis.includes(kpi.id)}
                                onChange={(e) => handleInputChange(kpi.kpi, e.target.value)}
                                value={
                                  inputValues[kpi.kpi]
                                    ? formatNumberWithCommas(inputValues[kpi.kpi])
                                    : inputValues[kpi.kpi] === 0
                                      ? '0'
                                      : ''
                                }
                              />
                            )}
                          </div>
                          <div
                            className={styles.actions}
                            style={{ flexDirection: view === 'Card View' ? 'column' : 'row' }}
                          >
                            {Number(project?.is_executive_project) ? (
                              <Checkbox
                                className={`${view === 'List View' ? styles.checkBox : styles.gridView}`}
                                disabled={!editKpis.includes(kpi.id)}
                                name="is_executive_project"
                                checked={Boolean(checkboxValues[kpi.kpi]) || false}
                                onChange={(e) => {
                                  if (!isEditForUser) {
                                    toast(`The current reporting period is locked`, {
                                      icon: <WarningAmberOutlined />,
                                    })
                                  } else {
                                    handleCheckboxChange(e, kpi)
                                  }
                                }}
                              />
                            ) : null}
                            <div className={styles.actionOfKpi}>
                              <div className={styles.action}>
                                {kpi?.id && view === 'List View' && (
                                  <div className={styles.deleteContainer}>
                                    <DeleteIcon
                                      onClick={() => {
                                        if (!isEditForUser) {
                                          toast(`The current reporting period is locked`, {
                                            icon: <WarningAmberOutlined />,
                                          })
                                        } else {
                                          setDeleteModel(kpi.id)
                                        }
                                      }}
                                    />
                                  </div>
                                )}
                                {!editKpis.includes(kpi.id) ? (
                                  <EditIcon
                                    onClick={() => {
                                      if (!isEditForUser) {
                                        toast(`The current reporting period is locked`, {
                                          icon: <WarningAmberOutlined />,
                                        })
                                      } else {
                                        handleEdit(kpi)
                                      }
                                    }}
                                  />
                                ) : (
                                  <>
                                    <CancelRoundedIcon onClick={() => handleDiscard(kpi)} />
                                    <SaveIcon color="#000000" onClick={() => handleUpdate(kpi)} />
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {kpi?.id && view === 'Card View' && (
                          <div className={styles.deleteContainer}>
                            <DeleteIcon
                              onClick={() => {
                                if (!isEditForUser) {
                                  toast(`The current reporting period is locked`, {
                                    icon: <WarningAmberOutlined />,
                                  })
                                } else {
                                  setDeleteModel(kpi.id)
                                }
                              }}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </SortableItem>
                )
              })}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  )
}

export default KpiCards
