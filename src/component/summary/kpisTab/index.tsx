import React, { useEffect, useMemo, useState } from 'react'
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { WarningAmberOutlined } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import DragIndicatorIcon from '@mui/icons-material/DragIndicator'
import GridOnOutlinedIcon from '@mui/icons-material/GridOnOutlined'
import ViewListOutlinedIcon from '@mui/icons-material/ViewListOutlined'
import { FormControlLabel } from '@mui/material'
import Tooltip from '@mui/material/Tooltip'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import { splitKpisByPhaseAndTypology } from './helper'
import KpiCards from './kpiCards'
import KpiDrawer from './kpiDrawer'
import styles from './KpisTab.module.scss'
import ConfirmDeleteModal from '../../confirmDeleteModal'
import PulseButton from '../../shared/button/pulseButton'
import Drawer from '../../shared/drawer'
import Loader from '../../shared/loader'
import PulseAccordion from '../../shared/pulseAccordion'
import { PROJECT_QUERY_KEY, PROJECTS_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IProjectKpi } from '@/src/redux/projectKpi/interface'
import useProjectKpi from '@/src/redux/projectKpi/useProjectKpi'
import useStatus from '@/src/redux/status/useStatus'
import { getMasterOneProject, getProjects } from '@/src/services/projects'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

export const SortableItem = ({ id, children, className }: any) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div ref={setNodeRef} className={className} style={style}>
      {children({ attributes, listeners })}
    </div>
  )
}

const KpisTab = ({ selectedTab }: any) => {
  const {
    getProjectKpisApi,
    projectKpis: dirtyProjectKpis,
    addProjectKpiApi,
    deleteProjectKpiApi,
    updateProjectKpiApi,
    sortProjectKpiApi,
  } = useProjectKpi()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const router = useRouter()
  const { currentUser } = useAuthorization()
  const [kpiData, setKpiData] = useState<any[]>([])
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [loader, setLoader] = useState(true)
  const [drawer, setDrawer] = useState(false)
  const [inputValues, setInputValues] = useState<any>({})
  const [checkboxValues, setCheckboxValues] = useState<any>({})
  const [view, setView] = useState('Card View')
  const sensors = useSensors(useSensor(PointerSensor))
  const { statuses, getStatusApi } = useStatus()
  const [expanded, setExpanded] = useState<string[]>([])

  const projectKpis = useMemo(() => {
    return (
      dirtyProjectKpis
        ?.filter((item: IProjectKpi) => !!item?.kpi)
        ?.map((item: any) => {
          if (item?.kpi_value === '') {
            return { ...item, kpi_value: '0' }
          }
          return item
        }) || []
    )
  }, [dirtyProjectKpis])

  const { data: projects } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    select: (response) => response.data,
  })

  const getProjectPayload = {
    projectName: router.query.slug as string,
    period: currentPeriod,
  }
  // const { kpis } = useGetKpis() //* NO need to use master kpi data as now missing data coming from project-kpi record (Confirmed by backed tame)
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  const fetchData = async () => {
    setLoader(true)
    try {
      await Promise.all([
        // getMasterKpisApi(),
        getStatusApi({
          period: currentPeriod,
          project_name: router?.query?.slug as string,
        }),
        getProjectKpisApi({ period: currentPeriod, project_name: router?.query?.slug as string }),
      ])
    } catch (error) {
      console.error('Error occurred while fetching the KPI data:', error)
    } finally {
      setLoader(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [router.query.slug, selectedTab])

  //***************************--------- NO need to use master kpi data as now missing data coming from project-kpi record (Confirmed by backed tame) -------------
  useEffect(() => {
    /* // if (!projects?.length || !kpis?.length) {
    //   setKpiData([])
    //   return
    // }
    // const projectSlug = router.query.slug
    // const foundProject = projects.find((item: any) => item.project_name === projectSlug)
    // const filteredKpiData = kpis.filter((item) => item.project_type === foundProject?.project_type)

    // Enrich KPI data with project KPI details
    // const generateUniqueId = (index: any) => `${Date.now() + index + 1}-${Math.random().toString(36).substr(2, 9)}`
    // const enrichedKpiData = filteredKpiData.map((kpi, index) => {
    //   const projectKpi = projectKpis.find((item) => item.kpi === kpi.kpi && item.project_name === projectSlug)

    //   return {
    //     ...kpi,
    //     ...projectKpi,
    //     editId: generateUniqueId(index),
    //     kpi_value: projectKpi?.kpi_value || null,
    //     id: projectKpi?.id,
    //     id_for_update_order: kpi?.id,
    //     is_executive_kpi: projectKpi?.is_executive_kpi === 'true' ? true : false,
    //   }
    // })

    // Identify and enrich remaining project KPIs not covered in enrichedKpiData

    // const remainingKpiData = projectKpis
    //   .filter((item) => item.project_name === projectSlug && !enrichedKpiData.some((i) => i.id === item.id))
    //   .map((item, index) => {
    //     // Generate unique editId and set default values
    //     const editId = generateUniqueId(index)
    //     const isExecutiveKpi = item?.is_executive_kpi === 'true' ? true : false

    //     return {
    //       ...item,
    //       is_executive_kpi: isExecutiveKpi,
    //       editId,
    //       kpi_value: item.kpi_value,
    //     }
    //   }) */

    // Combine enriched and remaining KPIs
    const combinedKpiData = [...projectKpis].map((item: any, index) => ({
      ...item,
      is_executive_kpi: Boolean(item?.is_executive_kpi),
      kpi_sorting_order: item?.kpi_sorting_order,
    }))

    setKpiData(combinedKpiData)

    // Initialize input and checkbox values
    setInputValues(
      combinedKpiData.reduce((acc: any, kpi: any) => {
        acc[kpi.kpi] = formatNumberWithCommas(kpi.kpi_value)
        return acc
      }, {}),
    )

    setCheckboxValues(
      combinedKpiData.reduce((acc: any, kpi: any) => {
        acc[kpi.kpi] = Boolean(kpi.is_executive_kpi)
        return acc
      }, {}),
    )
  }, [projects, projectKpis, router.query.slug])

  // inside KpisTab component
  const accordionData = useMemo(() => {
    const { phaseArray, typologyArray } = splitKpisByPhaseAndTypology(kpiData)

    return [
      { key: 'phase', label: 'Phase', data: phaseArray },
      { key: 'typology', label: 'Typology', data: typologyArray },
    ]
  }, [kpiData])

  const handleCheckboxChange = async (e: React.ChangeEvent<HTMLInputElement>, kpi: any) => {
    const updatedCheckboxValue = e.target.checked
    setCheckboxValues((prev: any) => ({
      ...prev,
      [kpi.kpi]: updatedCheckboxValue,
    }))

    // const res: any = await updateProjectKpiApi({
    //   id: kpi.id,
    //   data: {
    //     period: currentPeriod,
    //     is_executive_kpi: updatedCheckboxValue ? 'true' : 'false',
    //     project_name: kpi.project_name,
    //     project_type: kpi.project_type,
    //     kpi: kpi.kpi,
    //     kpi_value: kpi.kpi_value,
    //     last_updated: new Date().toLocaleTimeString(),
    //   },
    // })

    // if (!res.payload.success) return
    // await getProjectKpisApi({ period: currentPeriod })
  }

  const handleDelete = async (kpi: any) => {
    const res: any = await deleteProjectKpiApi(kpi)
    if (!res.payload.success) {
      errorToast(res?.payload?.response?.data?.message || 'Failed')
      return
    } else {
      successToast(res?.payload?.message || 'KPI Updated Successfully')
    }
    await getProjectKpisApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    setDeleteModel(null)
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  // Handle accordion toggle
  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded((prevExpanded) => {
      if (isExpanded) {
        // Add panel to expanded list
        return [...prevExpanded, panel]
      } else {
        // Remove panel from expanded list
        return prevExpanded.filter((p) => p !== panel)
      }
    })
  }

  return (
    <>
      <div className={styles.cards}>
        <PulseButton
          onClick={() => {
            if (!isEditForUser) {
              toast(`The current reporting period is locked`, {
                icon: <WarningAmberOutlined />,
              })
            } else {
              setDrawer(true)
            }
          }}
          label="Add Item"
          icon={<AddOutlinedIcon fontSize="large" />}
          disabled={loader}
        />
        {/* <RadioGroup aria-label="menu-items" name="menu-items" value={view} className={styles.radioGroup}>
          {['List View', 'Card View'].map((item, index) => (
            <div key={index} onClick={() => setView(item)}>
              <FormControlLabel
                value={item}
                control={<Radio color="primary" />}
                label={item}
                style={{ margin: 0 }}
                checked={view === item}
              />
            </div>
          ))}
        </RadioGroup> */}
        <div className={styles.iconButton}>
          {['List View', 'Card View'].map((item, index) => (
            <div key={index} onClick={() => setView(item)} style={{ display: 'inline-block' }}>
              <Tooltip title={item} arrow>
                <FormControlLabel
                  value={item}
                  control={<div />}
                  label={
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      {item === 'List View' ? (
                        <ViewListOutlinedIcon
                          style={{ width: 27, height: 27, color: view === 'List View' ? '#4a4a4a' : '#b0b0b0' }}
                        />
                      ) : (
                        <GridOnOutlinedIcon
                          style={{ width: 20, height: 20, color: view === 'Card View' ? '#4a4a4a' : '#b0b0b0' }}
                        />
                      )}
                    </div>
                  }
                  style={{ margin: 0 }}
                  checked={view === item}
                />
              </Tooltip>
            </div>
          ))}
        </div>

        <div className={styles.cards}>
          <Drawer anchor="right" open={drawer} onClose={() => setDrawer(false)}>
            <KpiDrawer
              drawerStates={drawer}
              onClose={() => setDrawer(false)}
              filterProjectKpiData={kpiData}
              project={project}
              editId={null}
              setEditId={() => {}}
              statuses={statuses}
            />
          </Drawer>
        </div>
      </div>

      <div className={styles.kpiContainer}>
        {loader ? (
          <Loader />
        ) : (
          <>
            {accordionData.map(({ key, label, data }) => {
              return (
                <PulseAccordion
                  key={key}
                  expanded={expanded.includes(key)}
                  onChange={handleAccordionChange(key)}
                  summaryContent={
                    <div className={styles.summaryContent}>
                      <span>{label}</span>
                    </div>
                  }
                  detailsContent={
                    data.length > 0 ? (
                      <KpiCards
                        sensors={sensors}
                        kpiData={data} // 👈 filtered per accordion
                        allkpiData={kpiData}
                        setKpiData={setKpiData} // you can still pass setter if needed
                        view={view}
                        inputValues={inputValues}
                        setInputValues={setInputValues}
                        isEditForUser={isEditForUser}
                        formatNumberWithCommas={formatNumberWithCommas}
                        project={project}
                        checkboxValues={checkboxValues}
                        currentPeriod={currentPeriod}
                        handleCheckboxChange={handleCheckboxChange}
                        setDeleteModel={setDeleteModel}
                      />
                    ) : (
                      <p style={{ textAlign: 'center', padding: '1rem', color: '#888' }}>
                        No {key} KPIs available for this project.
                      </p>
                    )
                  }
                  className={styles.accordionSummary}
                  summaryClassName={styles.accordionGlobalSummary}
                  detailsClassName="global-details"
                />
              )
            })}

            {/* Phase Accordion */}
            {/*   <PulseAccordion
              expanded={expanded.includes('phase')} // Allow multiple expansions
              onChange={handleAccordionChange('phase')}
              summaryContent={
                <div className={styles.summaryContent}>
                  <span>Phase</span>
                </div>
              }
              detailsContent={
                <KpiCards
                  sensors={sensors}
                  kpiData={kpiData}
                  setKpiData={setKpiData}
                  view={view}
                  inputValues={inputValues}
                  setInputValues={setInputValues}
                  isEditForUser={isEditForUser}
                  formatNumberWithCommas={formatNumberWithCommas}
                  project={project}
                  checkboxValues={checkboxValues}
                  currentPeriod={currentPeriod}
                  handleCheckboxChange={handleCheckboxChange}
                  setDeleteModel={setDeleteModel}
                />
              }
              className={styles.accordionSummary}
              summaryClassName={styles.accordionGlobalSummary}
              detailsClassName="global-details"
            />
             <PulseAccordion
              expanded={expanded.includes('typology')} // Allow multiple expansions
              onChange={handleAccordionChange('typology')}
              summaryContent={
                <div className={styles.summaryContent}>
                  <span>Typology</span>
                </div>
              }
              detailsContent={
                <KpiCards
                  sensors={sensors}
                  kpiData={kpiData}
                  setKpiData={setKpiData}
                  view={view}
                  inputValues={inputValues}
                  setInputValues={setInputValues}
                  isEditForUser={isEditForUser}
                  formatNumberWithCommas={formatNumberWithCommas}
                  project={project}
                  checkboxValues={checkboxValues}
                  currentPeriod={currentPeriod}
                  handleCheckboxChange={handleCheckboxChange}
                  setDeleteModel={setDeleteModel}
                />
              }
              className={styles.accordionSummary}
              summaryClassName={styles.accordionGlobalSummary}
              detailsClassName="global-details"
            /> */}
          </>
        )}
      </div>

      {deleteModel && (
        <ConfirmDeleteModal
          open={deleteModel ? true : false}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => handleDelete(deleteModel as number)}
        />
      )}
    </>
  )
}

export default KpisTab
