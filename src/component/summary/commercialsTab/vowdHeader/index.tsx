import React from 'react'
import { Box } from '@mui/material'
import styles from './VowdHeader.module.scss'
import Button from '@/src/component/shared/button'
import ContractsAndVOs from '@/src/component/svgImages/contractsAndVOs'
import PlusIcon from '@/src/component/svgImages/plusIcon'

const VowdHeader = ({ expanded, areAllExpanded, handleToggleAll, handleAddVowd, hasEmptyRecord, hasRecords }: any) => {
  return (
    <>
      <div className={styles.summaryTitle}>
        <div className={styles.header}>
          <ContractsAndVOs />
          <Box sx={{ flexGrow: 1 }}>VOWD</Box>
          <div
            className={`${styles.buttonPlus} ${hasEmptyRecord ? styles.disabled : ''}`}
            onClick={(e) => {
              e.stopPropagation() // Prevent accordion from closing
              if (hasEmptyRecord) {
                // toast.warning('Please save the current empty record before adding a new one')
                console.log('Please save the current empty record before adding a new one')
              } else {
                handleAddVowd()
              }
            }}
            title={hasEmptyRecord ? 'Save current record first' : 'Add new VOWD record'}
          >
            <PlusIcon color="#ffffff" />
          </div>
        </div>
      </div>
      {expanded && hasRecords && (
        <>
          <div className={styles.expandCollapseGroup}>
            <Button
              className={styles.expandedCollapse}
              onClick={(e) => {
                e.stopPropagation() // Prevent accordion from closing
                handleToggleAll()
              }}
            >
              {areAllExpanded ? 'Collapse All' : 'Expand All'}
            </Button>
          </div>
        </>
      )}
    </>
  )
}

export default VowdHeader
