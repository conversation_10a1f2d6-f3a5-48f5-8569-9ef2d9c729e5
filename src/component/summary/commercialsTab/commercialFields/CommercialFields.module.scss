.commercialFields {
  display: flex;
  flex-direction: column;
  gap: 20px;
  // margin-top: 20px;
  .textArea input {
    background-color: #f2f2f2;
    border-radius: 8px;
    padding: 14px 12px !important;
  }
  .content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    gap: 30px;
  }
}

.inputFields {
  div > div {
    background-color: #f2f2f2 !important;
    border: 1px solid #f2f2f2;
    border-radius: 5px;
  }
}
.highlightField {
  div > div {
    border: 1px solid rgb(40 101 220);
    background-color: #f0f8ff !important;

    > div {
      border: none !important;
    }
  }
}

.comboHighlight {
  > div {
    border: px solid rgb(40 101 220);
    background-color: #f0f8ff !important;
    border-radius: 5px;
  }
}
