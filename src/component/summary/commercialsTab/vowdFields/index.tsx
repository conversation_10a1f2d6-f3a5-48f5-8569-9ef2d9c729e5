import React, { useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { InputAdornment } from '@mui/material'
import { format } from 'date-fns'
import { useFormik } from 'formik'
import { toast } from 'sonner'
import styles from './VowdFields.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import Button from '@/src/component/shared/button'
import DatePicker from '@/src/component/shared/dataPicker'
import Loader from '@/src/component/shared/loader'
import NumberInputField from '@/src/component/shared/numberInputField'
import Tooltip from '@/src/component/shared/observerTooltip/ScrollHideTooltip'
import TextInputField from '@/src/component/shared/textInputField'
import TypographyField from '@/src/component/shared/typography'
import AedIcon from '@/src/component/svgImages/aedIcon'
import CancelRoundedIcon from '@/src/component/svgImages/cancelRoundedIcon'
import CommercialsDeleteIcon from '@/src/component/svgImages/commercialsDeleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'

import { payloadDateFormate } from '@/src/helpers/helpers'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useVowd from '@/src/redux/vowd/useVowd'
import { getLastUpdatedTime } from '@/src/utils/dateUtils'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const NUMBER_INPUT_STYLE = {
  '& .Mui-focused': {
    background: '#f4f4fc',
    '& .MuiOutlinedInput-notchedOutline': {
      // border: '1px solid #2333C2BF',
    },
  },
}
interface VowdFieldsProps {
  projectSummaryId: number
  loader?: boolean
  vowdData?: any // Add this to receive existing data
  onLocalRecordRemove?: (tempId: number) => void // Callback to remove local records
}

interface initialValues {
  input_date: string
  justification: string
  budget: number | null
  forecast_budget: number | null
  vowd_status: string
  project_summary_id: number
  variance: number | null
}

const VowdFields: React.FC<VowdFieldsProps> = ({ projectSummaryId, loader, vowdData, onLocalRecordRemove }) => {
  const { currentUser } = useAuthorization()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { addVowdApi, getAllVowdApi, updateVowdApi, deleteVowdApi } = useVowd()
  const [isEditMode, setIsEditMode] = useState<boolean>(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const initialValues: initialValues = useMemo(() => {
    return {
      project_summary_id: projectSummaryId,
      input_date: vowdData?.input_date || '',
      justification: vowdData?.justification || '',
      budget: vowdData?.budget ? Number(vowdData?.budget) : null,
      forecast_budget: vowdData?.forecast_budget ? Number(vowdData?.forecast_budget) : null,
      vowd_status: vowdData?.vowd_status || '',
      variance: vowdData?.variance ? Number(vowdData?.variance) : null,
    }
  }, [projectSummaryId, vowdData])

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const handleSubmit = async (values: any) => {
    try {
      setIsSaving(true) // Start loading
      const todayDate = new Date().toISOString()
      // const last_updated = DDMMYYYYformate(date) //issue

      const payload = {
        justification: values.justification,
        budget: values.budget ? parseFloat(values.budget) : null,
        forecast_budget: values.forecast_budget ? parseFloat(values.forecast_budget) : null,
        input_date: values.input_date ? payloadDateFormate(values?.input_date) : null,
        vowd_status: values.vowd_status,
      }

      let response: Record<string, any> | null = null
      if (vowdData?.id) {
        response = await updateVowdApi({ id: vowdData.id, data: { ...payload, last_updated: todayDate } })
      } else {
        response = await addVowdApi({ ...payload, project_summary_id: projectSummaryId })
      }
      if (response?.payload?.success) {
        successToast(response?.payload?.message)
        getAllVowdApi({ projectSummaryId: projectSummaryId })

        // Remove local record if it was a new record (has tempId but no id)
        if (vowdData?.tempId && !vowdData?.id && onLocalRecordRemove) {
          onLocalRecordRemove(vowdData.tempId)
        }
      } else {
        const errorMessage =
          response?.payload?.response?.data?.message || response?.payload?.message || 'Something went wrong!'
        errorToast(errorMessage || 'Something went wrong!')
      }
      setIsEditMode(false)
    } catch (error) {
      console.log('error: ', error)
    } finally {
      setIsSaving(false) // End loading
    }
  }

  const handleDelete = async () => {
    try {
      setIsSaving(true) // Start loading
      if (vowdData?.id) {
        // Delete existing record via API
        const res: Record<string, any> = await deleteVowdApi(vowdData?.id)
        if (!res.payload.success) {
          setShowDeleteConfirmation(false)
          errorToast(res.payload?.response?.data?.message || 'Something went wrong!')
          return
        }
        getAllVowdApi({ projectSummaryId: projectSummaryId })
        successToast('Record deleted successfully')
      } else if (vowdData?.tempId && onLocalRecordRemove) {
        // Delete local record (empty record)
        onLocalRecordRemove(vowdData.tempId)
        successToast('Record removed')
      }
      setShowDeleteConfirmation(false)
    } catch (error) {
      console.log('error: ', error)
      setShowDeleteConfirmation(false)
    } finally {
      setIsSaving(false) // End loading
    }
  }

  const handleDiscard = () => {
    formik.resetForm()
    setIsEditMode(false)

    // For local records (tempId only), just reset the form - don't remove the record
    // For saved records (with id), just reset the form
    // Only remove local records when explicitly deleted via delete button
  }

  const formik = useFormik({
    initialValues: initialValues,
    enableReinitialize: true,
    // validationSchema,
    onSubmit: (values) => {
      handleSubmit(values)
    },
  })

  return (
    <div className={styles.commercialFields}>
      {loader ? (
        <Loader />
      ) : (
        <>
          {/* Action Buttons */}
          <div className={styles.actionButtons}>
            {isEditMode ? (
              <>
                <Button
                  type="button"
                  startIcon={<CancelRoundedIcon />}
                  className={styles.discardButton}
                  color="secondary"
                  onClick={handleDiscard}
                  disabled={isSaving}
                >
                  Discard
                </Button>
                <Button
                  className={styles.saveButton}
                  startIcon={<SaveIcon />}
                  variant="contained"
                  onClick={() => formik.handleSubmit()}
                  disabled={!formik.dirty || isSaving}
                >
                  Save
                </Button>
              </>
            ) : (
              <Button
                className={styles.editButton}
                startIcon={<EditIcon fill="#FFFFFF" />}
                variant="contained"
                onClick={() => {
                  if (!isEditForUser) {
                    toast(`The current reporting period is locked`, {
                      icon: <WarningAmberOutlined />,
                    })
                  } else {
                    setIsEditMode(true)
                  }
                }}
              >
                Edit
              </Button>
            )}
            <CommercialsDeleteIcon
              className={`${styles.deleteIcon} ${isSaving ? styles.disabled : ''}`}
              onClick={() => {
                if (isSaving) return // Prevent click when deleting
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  setShowDeleteConfirmation(true)
                }
              }}
            />
            <div>
              <TypographyField
                variant={'bodyBold'}
                className={styles.lastUpdatedText}
                text={(vowdData?.updated_by && `${vowdData.updated_by}`) || ''}
              />
              <TypographyField
                variant={'thin'}
                className={styles.lastUpdatedText}
                text={vowdData?.last_updated ? `${getLastUpdatedTime(vowdData?.last_updated as string)}` : ''}
              />
            </div>
          </div>

          <div className={styles.content}>
            {/* Month */}
            <div>
              <DatePicker
                name="input_date"
                labelText="Month/Year"
                format="MM-yyyy"
                views={['month', 'year']}
                disabled={!isEditMode}
                className={`${!isEditMode ? '' : styles.highlightField} ${!isEditMode ? styles.dataPickerInput : ''}`}
                value={
                  formik.values.input_date
                    ? (() => {
                        // Convert DD-MM-YYYY to Date object for DatePicker
                        const [day, month, year] = formik.values.input_date.split('-')
                        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
                      })()
                    : null
                }
                onChange={(value) => {
                  if (value) {
                    // Set the date to the 1st day of the selected month/year
                    const firstDayOfMonth = new Date(value.getFullYear(), value.getMonth(), 1)
                    const formattedDate = format(firstDayOfMonth, 'dd-MM-yyyy')
                    formik.setFieldValue('input_date', formattedDate)
                  } else {
                    formik.setFieldValue('input_date', '')
                  }
                }}
              />
            </div>
            {/* Description */}
            <TextInputField
              name="justification"
              labelText="Justification"
              placeholder="Type something ..."
              value={formik.values.justification}
              className={`${styles.textField} ${isEditMode ? styles.highlightedTextField : ''}`}
              fullWidth
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              variant={'outlined'}
              disabled={!isEditMode}
            />
            {/* budget */}
            <Tooltip title={formik.values.budget ? (formatNumberWithCommas(formik.values.budget) as string) : ''} arrow>
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="budget"
                  labelText="Budget"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={formik?.values?.budget ? (formatNumberWithCommas(formik?.values?.budget) as string) : ''}
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('budget', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.budget && Boolean(formik.errors.budget)}
                  //   helperText={formik.touched.budget && formik.errors.budget}
                />
              </div>
            </Tooltip>
            {/* VOWD status */}

            <div>
              <TextInputField
                name="vowd_status"
                labelText="VOWD Status"
                placeholder="Type something ..."
                value={formik.values.vowd_status}
                className={`${styles.textField} ${isEditMode ? styles.highlightedTextField : ''}`}
                fullWidth
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                variant={'outlined'}
                disabled={!isEditMode}
              />
            </div>

            {/* Forecast Budget */}
            <Tooltip
              title={
                formik?.values?.forecast_budget
                  ? (formatNumberWithCommas(formik?.values?.forecast_budget) as string)
                  : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="forecast_budget"
                  labelText="Forecast Budget"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    formik?.values?.forecast_budget
                      ? (formatNumberWithCommas(formik?.values?.forecast_budget) as string)
                      : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('forecast_budget', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.forecast_budget && Boolean(formik.errors.forecast_budget)}
                  //   helperText={formik.touched.forecast_budget && formik.errors.forecast_budget}
                />
              </div>
            </Tooltip>
            {/* variance */}
            <Tooltip
              title={formik?.values?.variance ? (formatNumberWithCommas(formik?.values?.variance) as string) : ''}
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="variance"
                  labelText="Variance"
                  className={`${styles.inputFields}`}
                  value={formik?.values?.variance ? (formatNumberWithCommas(formik?.values?.variance) as string) : ''}
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('variance', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={true}
                  error={formik.touched.variance && Boolean(formik.errors.variance)}
                  //   helperText={formik.touched.variance && formik.errors.variance}
                />
              </div>
            </Tooltip>
          </div>
        </>
      )}
      {showDeleteConfirmation && (
        <ConfirmDeleteModal
          open={showDeleteConfirmation}
          onClose={() => setShowDeleteConfirmation(false)}
          handleConfirm={handleDelete}
          loading={isSaving}
        />
      )}
    </div>
  )
}

export default VowdFields
