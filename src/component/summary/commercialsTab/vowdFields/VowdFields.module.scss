.commercialFields {
  display: flex;
  flex-direction: column;
  gap: 20px;
  // margin-top: 20px;
  .textArea input {
    background-color: #f2f2f2;
    border-radius: 8px;
    padding: 14px 12px !important;
  }
  .content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    gap: 30px;
  }
}

.inputFields {
  div > div {
    background-color: #f2f2f2 !important;
    border: 1px solid #f2f2f2;
    border-radius: 5px;
  }
}

.highlightField {
  div > div {
    border: 1px solid rgb(40 101 220) !important  ;
    background-color: #f0f8ff !important;

    > div {
      border: none !important;
    }
  }
}

.dataPickerInput {
  div > div {
    background-color: #f2f2f2 !important;
    border: 1px solid #f2f2f2;
  }
}

.textField {
  > div > div {
    background-color: #f2f2f2 !important;
    border: 1px solid #f2f2f2;
    border-radius: 5px;
  }
  :global(.MuiFormHelperText-root) {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: unset !important;
    word-break: break-word;
    display: block !important;
  }

  // Style for disabled Mui input
  :global(.Mui-disabled) {
    cursor: not-allowed !important;
  }
}

.highlightedTextField {
  > div > div {
    border-radius: 5px;
    background: #f0f8ff !important;
    border: 1px solid rgb(40 101 220) !important;
  }
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: auto;

  .editButton,
  .saveButton,
  .discardButton {
    min-width: 100px;
  }

  .deleteIcon {
    cursor: pointer;
    transition: transform 0.2s ease, opacity 0.2s ease;

    &:hover:not(.disabled) {
      transform: scale(1.1);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}
