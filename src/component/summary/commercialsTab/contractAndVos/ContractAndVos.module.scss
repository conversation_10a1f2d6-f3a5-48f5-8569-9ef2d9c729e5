@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.accordion {
  box-shadow: none;
  border-radius: 8px !important;
  // margin-bottom: 16px;
  &::before {
    content: none;
  }
  .summary {
    // margin-bottom: 0px;
    // padding: 0 20px;
    padding-left: 0 !important;
    min-height: 56px;
    .content {
      margin: 0;
      padding-right: 10px;
      align-items: center;
      position: relative;
      // &::after {
      //   content: "";
      //   position: absolute;
      //   left: 150px;
      //   right: 80px;
      //   top: 50%;
      //   transform: translateY(-50%);
      //   border: 1px solid $LIGHT_200;
      // }
      .summaryTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .header {
          display: flex;
          gap: 10px;
          align-items: center;
          padding: 8px 20px;
          background: #eeeeec;
          border-radius: 0 8px 8px 0;
          // min-width: 150px;
          font-size: 12px;
          font-weight: 700;
          line-height: 18px;
          color: $DARK;
          white-space: nowrap;
          position: absolute;
          z-index: 1;
          padding-right: 8px;

          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 85%;
          display: block;

          @include respond-to('mobile') {
            width: 38%;
          }
          @include respond-to('tablet') {
            width: 60%;
          }
          @include respond-to('laptop') {
            width: 65%;
          }
          @include respond-to('desktop') {
            width: 71%;
          }
        }
      }
      .editIcon {
        border: 1px solid #444444 !important;
        padding: 4px 6px !important;
        font-size: 12px !important;
        color: black !important;
        margin: 0 8px 0 auto;
      }
    }
  }
  .details {
    padding: 0;
    margin: 0 16px 16px;
    display: flex;
    gap: 10px;
    flex-direction: column;
  }
}
.container {
  padding: 0px 0px;
}
.crudIcon {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  width: 100%;
  gap: 8px;
  .action {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
.timerIcon {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.accordionContainerabc {
  display: flex;
  gap: 10px;
  flex-direction: column;
  // margin-top: 20px;
  // padding: 0 24px;

  .buttons {
    display: flex;
    justify-content: flex-end;
  }
}