.content {
  display: flex;
  flex-direction: column;
  // display:flex;
  // grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 30px;
  margin-top: 20px;
}
.editIcon {
  border: 1px solid #444444 !important;
  padding: 4px 6px !important;
  font-size: 12px !important;
  color: black !important;
  margin: 0 8px 0 auto;
}
.textField div {
  background-color: #f2f2f2 !important;
  border: 1px solid #f2f2f2;
}
.inputField {
  div > div {
    background-color: #f2f2f2 !important;
    border: 1px solid #f2f2f2;
    border-radius: 3px;
  }
}
// .comboBoxInput > div {
//   border: 1px solid #f2f2f2;
//   background-color: #f2f2f2;
// }

.comboBoxInput {
  > div {
    border: 1px solid #f2f2f2;
    border-radius: 3px;
    > div {
      background-color: #f2f2f2 !important;
    }
  }
}

// .comboBoxInput {
//   div > div {
//     background-color: #f2f2f2 !important;
//     border: 1px solid #f2f2f2;
//     border-radius: 3px;
//   }
// }

.isActive > div > div > div > div {
  color: rgba(0, 0, 0, 0.38) !important;
}
.upGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 2.1fr;
  gap: 30px;
}
.dowGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 30px;
}
.textInputField {
  // flex-grow: 2; // corrected from 'flex2' to '2'
}

.highlightField {
  div > div {
    border: 1px solid rgb(40 101 220);
    background-color: #f0f8ff !important ;

    > div {
      border: none !important;
    }
  }
}

.comboHighlight {
  > div {
    border: 1px solid rgb(40 101 220);
    background-color: #f0f8ff;
    border-radius: 3px;
  }
}

.comboHighlight {
  > div {
    border: 1px solid rgb(40 101 220);
    border-radius: 3px;
    > div {
      background-color: #f0f8ff !important;
    }
  }
}

.errorBox {
  > div,
  p {
    border: none !important;
    background-color: white !important;
  }
}

.contractorCheckbox {
  // margin-top: 10px;
  background-color: #f0f8ff !important; // light blue or any color you prefer
  border: 1px solid #2865dc !important;
  border-radius: 3px;

  > span {
    padding: 0 !important;
    border-radius: 3px;
  }
}
