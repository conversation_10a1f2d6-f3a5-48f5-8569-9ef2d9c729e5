import React from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { toast } from 'sonner'
import styles from './CommercialHeader.module.scss'
import Button from '@/src/component/shared/button'
import Typography<PERSON>ield from '@/src/component/shared/typography'
import CancelRoundedIcon from '@/src/component/svgImages/cancelRoundedIcon'
import CommercialsDeleteIcon from '@/src/component/svgImages/commercialsDeleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import ProjectDetails from '@/src/component/svgImages/projectDetails'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import { getLastUpdatedTime } from '@/src/utils/dateUtils'

const CommercialHeader = ({ isEditMode, isEditForUser, setIsEditMode, formik, handleDelete, commercialData }: any) => {
  return (
    <>
      <div className={styles.summaryTitle}>
        <div className={styles.accordianHeader}>
          <ProjectDetails />
          <div>Project Summary</div>
        </div>
        <div className={styles.detailsAction}>
          <div className={styles.action}>
            {!isEditMode ? (
              <Button
                className={styles.btnOfAction}
                startIcon={<EditIcon fill="#FFFFFF" />}
                variant={'contained'}
                onClick={(e) => {
                  e.stopPropagation()
                  if (!isEditForUser) {
                    toast(`The current reporting period is locked`, {
                      icon: <WarningAmberOutlined />,
                    })
                  } else {
                    setIsEditMode(true)
                  }
                }}
              >
                Edit
              </Button>
            ) : (
              <>
                <Button
                  type="button"
                  startIcon={<CancelRoundedIcon />}
                  className={styles.addProjectButton}
                  color="secondary"
                  onClick={(e) => {
                    e.stopPropagation()
                    formik.resetForm() // Reset the form to initial values
                    setIsEditMode(false)
                  }}
                >
                  Discard
                </Button>
                <Button
                  className={styles.btnOfAction}
                  startIcon={<SaveIcon />}
                  variant={'contained'}
                  onClick={(e) => {
                    e.stopPropagation()
                    formik.handleSubmit()
                  }}
                  disabled={!formik.dirty} // Disable the button if there are no changes
                >
                  Save
                </Button>
              </>
            )}
            <CommercialsDeleteIcon
              className={styles.deleteIcon}
              onClick={(e) => {
                e.stopPropagation()
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  handleDelete()
                }
              }}
            />
          </div>
          <div className={styles.lastUpdatedSection}>
            <div className={styles.timerIcon}>
              {/* {commercialData?.last_updated && <TimerIcon />} */}
              <TypographyField
                variant={'thin'}
                className={styles.lastUpdatedText}
                text={
                  commercialData?.last_updated ? `${getLastUpdatedTime(commercialData?.last_updated as string)}` : ''
                }
              />
            </div>
            <TypographyField
              variant={'bodyBold'}
              className={styles.lastUpdatedText}
              text={commercialData?.updated_by && `${commercialData.updated_by}`}
            />
          </div>
        </div>
      </div>
    </>
  )
}

export default CommercialHeader
