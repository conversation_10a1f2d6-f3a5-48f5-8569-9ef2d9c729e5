import { useState } from 'react'
import { format, parseISO } from 'date-fns'
import styles from './VOWD.module.scss'
import VOWDFields from '../vowdFields'
import VowdHeader from '../vowdHeader'
import PulseAccordion from '@/src/component/shared/pulseAccordion'
import useVowd from '@/src/redux/vowd/useVowd'

interface IVOWDProps {
  loader: boolean
  projectSummaryId: number
}

const VOWD = ({ projectSummaryId, loader }: IVOWDProps) => {
  const { vowds } = useVowd()
  const [expandedItems, setExpandedItems] = useState<{ [key: number]: boolean }>({})
  const [mainAccordionExpanded, setMainAccordionExpanded] = useState(true)
  const [localVowds, setLocalVowds] = useState<any[]>([])

  // Combine API vowds with local vowds (for new empty records)
  const allVowds = [...(vowds || []), ...localVowds]

  // Sort VOWD records by date (ascending order)
  const sortedVowdData =
    allVowds?.length > 0
      ? [...allVowds]?.sort((a, b) => {
          // Handle empty records (put them at the end)
          if (!a.input_date && !b.input_date) return 0
          if (!a.input_date) return 1
          if (!b.input_date) return -1
          return new Date(a.input_date).getTime() - new Date(b.input_date).getTime()
        })
      : []

  // Function to format date for header display (e.g., "Feb 2025")
  const formatHeaderDate = (dateString: string) => {
    try {
      const date = parseISO(dateString)
      return format(date, 'MMM yyyy')
    } catch (error) {
      return dateString
    }
  }

  // Function to convert YYYY-MM-DD to DD-MM-YYYY for the component
  const convertToComponentFormat = (dateString: string) => {
    try {
      const date = parseISO(dateString)
      return format(date, 'dd-MM-yyyy')
    } catch (error) {
      return dateString
    }
  }

  // Check if all items are expanded
  const areAllExpanded = sortedVowdData.every((item) => {
    const itemKey = item.id || item.tempId
    return expandedItems[itemKey]
  })

  // Expand/Collapse all items
  const handleToggleAll = () => {
    const newExpandedState: { [key: number]: boolean } = {}
    const shouldExpandAll = !areAllExpanded

    sortedVowdData.forEach((item) => {
      const itemKey = item.id || item.tempId
      newExpandedState[itemKey] = shouldExpandAll
    })

    setExpandedItems(newExpandedState)
  }

  // Check if there's already an empty record (without id)
  const hasEmptyRecord = localVowds.some((item) => !item.id)

  // Add new empty VOWD record
  const handleAddVowd = () => {
    if (hasEmptyRecord) {
      // Prevent adding multiple empty records
      console.log('Please save the current empty record before adding a new one')
      return
    }

    // Create a new empty record with temporary id
    const newEmptyRecord = {
      id: null, // No ID for new records
      tempId: Date.now(), // Temporary ID for React key
      input_date: '',
      justification: '',
      budget: '',
      forecast_budget: '',
      vowd_status: '',
      project_summary_id: projectSummaryId,
    }

    setLocalVowds((prev) => [...prev, newEmptyRecord])

    // Auto-expand the main accordion to show the new record
    setMainAccordionExpanded(true)

    // Auto-expand the new record
    setExpandedItems((prev) => ({
      ...prev,
      [newEmptyRecord.tempId]: true,
    }))
  }

  // Remove empty record from local state (when user cancels or saves)
  const handleRemoveLocalRecord = (tempId: number) => {
    setLocalVowds((prev) => prev.filter((item) => item.tempId !== tempId))
    setExpandedItems((prev) => {
      const newState = { ...prev }
      delete newState[tempId]
      return newState
    })
  }

  return (
    <div className={styles.accordionSection}>
      {/* Main VOWD Accordion */}
      <PulseAccordion
        expanded={mainAccordionExpanded}
        onChange={(_, isExpanded) => setMainAccordionExpanded(isExpanded)}
        summaryContent={
          <VowdHeader
            expanded={mainAccordionExpanded}
            areAllExpanded={areAllExpanded}
            handleToggleAll={handleToggleAll}
            handleAddVowd={handleAddVowd}
            hasEmptyRecord={hasEmptyRecord}
            hasRecords={sortedVowdData.length > 0}
          />
        }
        detailsContent={
          <div>
            {/* Individual VOWD Records */}
            {sortedVowdData.map((vowdItem) => {
              const itemKey = vowdItem.id || vowdItem.tempId
              return (
                <PulseAccordion
                  key={itemKey}
                  expanded={expandedItems[itemKey] || false}
                  onChange={(_, isExpanded) => {
                    setExpandedItems((prev) => ({
                      ...prev,
                      [itemKey]: isExpanded,
                    }))
                  }}
                  summaryContent={
                    <div className={styles.vowdItemHeader}>
                      <span className={styles.dateHeader}>{formatHeaderDate(vowdItem.input_date) || '-'}</span>
                      {/* <span className={styles.statusBadge}>{vowdItem.vowd_status}</span> */}
                    </div>
                  }
                  detailsContent={
                    <div className={styles.container}>
                      <div className={styles.contentWrapper}>
                        <VOWDFields
                          loader={loader}
                          projectSummaryId={projectSummaryId}
                          vowdData={{
                            ...vowdItem,
                            input_date: convertToComponentFormat(vowdItem.input_date),
                          }}
                          onLocalRecordRemove={vowdItem.tempId ? handleRemoveLocalRecord : undefined}
                        />
                      </div>
                    </div>
                  }
                  className={styles.innerAccordion}
                  summaryClassName={styles.content}
                  detailsClassName={styles.details}
                />
              )
            })}
          </div>
        }
        className={styles.mainAccordion}
        summaryClassName={styles.content}
        detailsClassName={styles.details}
      />
    </div>
  )
}

export default VOWD
