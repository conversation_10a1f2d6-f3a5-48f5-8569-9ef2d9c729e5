@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.accordionSection {
  margin: 16px 20px 24px 0;
}

.accordion {
  background-color: #f9f9f9 !important;
  box-shadow: none;
  border-radius: 8px !important;
  margin-bottom: 28px;
  &::before {
    content: none;
  }
  .expandCollapseGroup {
    gap: 6px;
  }
  .expandedCollapse {
    width: 120px;
    margin-right: 12px;
  }
  .summary {
    // padding: 0 20px;
    padding-left: 0 !important;
    min-height: 56px;
    align-items: baseline;
    max-height: 72px;

    .expandIcon {
      width: 24px;
      height: 24px;
    }
    .content {
      margin: 16px 0 35px 0;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // &::after {
      //   content: "";
      //   position: absolute;
      //   left: 50px;
      //   right: 51px;
      //   top: 50%;
      //   transform: translateY(-50%);
      //   border: 1px solid $LIGHT_200;
      // }
      .summaryTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-right: 8px;

        .header {
          display: flex;
          gap: 10px;
          align-items: center;
          padding: 8px 20px;
          background: #eeeeec;
          border-radius: 0 8px 8px 0;
          font-size: 16px;
          font-weight: 700;
          line-height: 18px;
          color: $DARK;
          white-space: nowrap;
          position: relative;
          z-index: 1;
          padding-right: 8px;
          min-width: 212px;
        }
      }
      .editIcon {
        color: black !important;
        margin: 0 8px 0 auto;
      }
    }
  }
  .details {
    padding: 0;
    margin: 0 16px 25px;
  }
}

.content {
  padding-left: 0px !important;
}

.vowdItemHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .dateHeader {
    padding: 8px 20px;
    background: #eeeeec;
    border-radius: 0 8px 8px 0;
    font-size: 12px;
    font-weight: 700;
    line-height: 18px;
    color: #444444;
    white-space: nowrap;
    padding-right: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 100px;
    display: block;
    width: 85%;

    @include respond-to('mobile') {
      width: 38%;
    }
    @include respond-to('tablet') {
      width: 60%;
    }
    @include respond-to('laptop') {
      width: 65%;
    }
    @include respond-to('desktop') {
      width: 71%;
    }
  }

  .statusBadge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
  }
}

.container {
  .contentWrapper {
    padding: 0;
  }
}

.mainAccordion {
  background-color: #f9f9f9 !important;
  box-shadow: none;
  border-radius: 8px !important;
  margin-bottom: 28px;

}

// .vowdRecordsContainer {
//   padding: 16px;
//   background-color: #ffffff;
//   border-radius: 8px;
//   margin: 0 16px 16px 16px;
// }

.innerAccordion {
  background-color: #ffffff !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 6px !important;
  margin-bottom: 16px;
  border: 1px solid #e0e0e0;

  & .MuiAccordionSummary-root{
    max-height: 64px !important;
    height: 64px !important;
    min-width: none;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &::before {
    content: none;
  }
}
