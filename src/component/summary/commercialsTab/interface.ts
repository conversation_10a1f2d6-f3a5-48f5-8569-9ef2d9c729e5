export interface ICommercial {
  id?: number
  period?: string
  project_name: string
  description: string
  budget: number
  totalPVR?: number
  committed_cost: number
  design_budget: number
  forecast_at_completion: number
  construction_budget: number
  supervision_budget: number
  management_fees: number
  contingency: number
  year_planned_vowd: number
  year_forecast_vowd: number
  vowd: number
  paid_amount: number
  last_updated: Date
}

export interface IAddCommercialButtonProps {
  commercial: any
  setCommercial: (args: any) => void
  setIsOpenDrawer: React.Dispatch<React.SetStateAction<boolean>>
  setSelectedCommercials: React.Dispatch<React.SetStateAction<ICommercial | null>>
  className?: string
  startIcon?: React.ReactNode
  buttonColor?: 'primary' | 'inherit' | 'secondary' | 'success' | 'error' | 'info' | 'warning'
}

export interface ICommercialColumn {
  key: string
  label: string
  renderCell?: (value: any, row: Record<string, any>, rowIndex: number) => JSX.Element | undefined
}

export interface ICommercialsTableProps {
  columns: ICommercialColumn[]
  data: Record<string, any>[]
}

export interface TableHeaderProps {
  columns: ICommercialColumn[]
}

export interface TableBodyProps {
  data: Record<string, any>[]
  columns: ICommercialColumn[]
}

export interface TableRowProps {
  row: Record<string, any>
  rowIndex: number
  columns: ICommercialColumn[]
}

export interface TableCellProps {
  value: any
  column: ICommercialColumn
  rowIndex: number
  row: Record<string, any>
}
