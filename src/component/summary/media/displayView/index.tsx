// MediaDisplay.tsx
import React, { useEffect, useState } from 'react'
import { AddOutlined, PictureAsPdfOutlined, GridOnOutlined, DescriptionOutlined } from '@mui/icons-material'
import { Tooltip } from '@mui/material'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import AddPhaseModel from '../addPhaseModel'
import styles from '../media.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import PulseModel from '@/src/component/shared/pulseModel'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import Accordion from '@/src/component/updateProgress/progressForm/accordion'
import { convertValuesToCommaSeparated } from '@/src/helpers/helpers'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useFile from '@/src/redux/uploadPicture/useFile'
import { getMasterOneProject } from '@/src/services/projects'
import { IProjects } from '@/src/services/projects/interface'
import { errorToast } from '@/src/utils/toastUtils'

interface IMediaDisplay {
  mediaCategory: any
  images: any[]
  fetchImages: (project: IProjects) => void
  setIsLoading: (args: boolean) => void
  isEditForUser: boolean
}

const MediaDisplay: React.FC<IMediaDisplay> = ({ mediaCategory, images, fetchImages, setIsLoading, isEditForUser }) => {
  const router = useRouter()
  const { currentPeriod } = useMasterPeriod()
  const { deleteFileApi, updatePhaseApi } = useFile()
  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }

  const getImages = async () => {
    const res: any = await getMasterOneProject(getProjectPayload)
    fetchImages(res?.data)
  }

  const [deleteModel, setDeleteModel] = useState<any>(null)
  const [addPhaseModel, setAddPhaseModel] = useState<boolean>(false)
  const [selectedImage, setSelectedImage] = useState<any>(null)
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false)
  const queryClient = useQueryClient()

  const handleDelete = async () => {
    setIsLoading(true)
    if (deleteModel) {
      //TODO: Old code commented
      /*   const payload = {
        project_name: router.query.slug,
        field_type: deleteModel.type,
        period: currentPeriod,
        fileName: deleteModel.fileName,
      }
      const res: Record<string, any> = await deleteFileApi(payload) */

      const payload = {
        project_name: router.query.slug as string,
        period: currentPeriod,
        id: deleteModel.lookup_project_to_media_id,
        type: 'project',
      }
      const res: Record<string, any> = await deleteFileApi(payload)
      if (res.payload.success) {
        getImages()
        queryClient.invalidateQueries({ queryKey: [PROJECT_QUERY_KEY] })
      } else {
        errorToast(res.payload.response.data.message || 'Failed to delete file')
        setIsLoading(false)
      }
      setDeleteModel(null)
    }
    // setIsLoading(false)
  }

  const handleSubmit = async (lookup_project_to_phase_id: number) => {
    try {
      setIsLoading(true)
      const payload = {
        // project_name: router.query.slug,
        // period: currentPeriod,
        // field_type: selectedImage?.type,
        // file_paths_to_update: [selectedImage?.file_path],
        lookup_project_to_media_id: selectedImage.lookup_project_to_media_id,
        lookup_project_to_phase_id: lookup_project_to_phase_id ? lookup_project_to_phase_id : null,
      }
      const res: Record<string, any> = await updatePhaseApi(payload)
      if (res.payload.success) {
        getImages()
      } else {
        errorToast(res.payload.response.data.message || 'Failed to update phase')
        setIsLoading(false)
      }
      setAddPhaseModel(false)
    } catch (error) {
      console.log('Error updating phase:', error)
      setIsLoading(false)
    }
  }

  const handleAccordionToggle = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false)
  }

  return (
    <div>
      <p className={styles.title}>
        {mediaCategory.header === 'Progress Videos' ? 'Progress Media' : mediaCategory.header}
      </p>
      {/* Group by phase dynamically */}
      <div className={styles.sectionSticky}>
        {images[mediaCategory.header] &&
          Object.entries(images[mediaCategory.header] as Record<string, any[]>).map(
            ([phaseName, phaseImages]: [string, any[]], index) => {
              return (
                <>
                  {phaseName ? (
                    <Accordion
                      key={index}
                      title={convertValuesToCommaSeparated(phaseName)}
                      expanded={expandedAccordion === `${mediaCategory.header}-${phaseName}`}
                      onChange={handleAccordionToggle(`${mediaCategory.header}-${phaseName}`) as any}
                    >
                      <div className={styles.media}>
                        {phaseImages?.map((img: any, i: number) => {
                          return img?.fileName?.includes('pdf') ? (
                            <div className={styles.pdfWrapper} key={i}>
                              <PictureAsPdfOutlined
                                className={styles.pdfIcon}
                                onClick={() => {
                                  const link = document.createElement('a')
                                  link.href = img.url
                                  link.download = img.fileName
                                  link.click()
                                }}
                              />
                              <div className={styles.content}>
                                <p className={styles.pdfName}>{img?.display_name}</p>
                                {isEditForUser && (
                                  <DeleteIcon className={styles.deletePdf} onClick={() => setDeleteModel(img)} />
                                )}
                              </div>
                            </div>
                          ) : (
                            <div key={i} className={styles.imageWrapper}>
                              {img?.file_path?.toLowerCase().endsWith('.mp4') ? (
                                <video src={img.url} width={400} height={254} controls className={styles.videoBox} />
                              ) : (
                                <img src={img.url} alt="" width={400} height={254} className={styles.imageBox} />
                              )}
                              <div className={styles.content}>
                                <p className={styles.imageName}>{img?.display_name}</p>
                                {isEditForUser && (
                                  <div className={styles.iconWrapper}>
                                    {img?.type !== 'Plot Plan' && (
                                      <Tooltip title={'Edit Phase'} arrow>
                                        <AddOutlined
                                          onClick={() => {
                                            setAddPhaseModel(true)
                                            setSelectedImage(img)
                                          }}
                                        />
                                      </Tooltip>
                                    )}
                                    <DeleteIcon className={styles.deleteIcon} onClick={() => setDeleteModel(img)} />
                                  </div>
                                )}
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </Accordion>
                  ) : (
                    <div className={styles.emptyPhase}>
                      <div className={styles.media}>
                        {phaseImages?.map((img: any, i: number) =>
                          img?.fileName?.includes('pdf') ? (
                            <div className={styles.pdfWrapper} key={i}>
                              <PictureAsPdfOutlined
                                className={styles.pdfIcon}
                                onClick={() => {
                                  const link = document.createElement('a')
                                  link.href = img.url
                                  link.download = img.fileName
                                  link.click()
                                }}
                              />
                              <div className={styles.content}>
                                <p className={styles.pdfName}>{img?.display_name}</p>
                                {isEditForUser && (
                                  <DeleteIcon className={styles.deletePdf} onClick={() => setDeleteModel(img)} />
                                )}
                              </div>
                            </div>
                          ) : img?.fileName.toLowerCase().endsWith('.xlsx') ||
                            img?.fileName.toLowerCase().endsWith('.xls') ? (
                            <div className={styles.pdfWrapper} key={i}>
                              <GridOnOutlined
                                className={styles.excelIcon}
                                onClick={() => {
                                  const link = document.createElement('a')
                                  link.href = img.url
                                  link.download = img.fileName
                                  link.click()
                                }}
                              />
                              <div className={styles.content}>
                                <p className={styles.pdfName}>{img?.display_name}</p>
                                {isEditForUser && (
                                  <DeleteIcon className={styles.deletePdf} onClick={() => setDeleteModel(img)} />
                                )}
                              </div>
                            </div>
                          ) : img?.fileName?.includes('txt') ? (
                            <div className={styles.pdfWrapper} key={i}>
                              <DescriptionOutlined
                                className={styles.textIcon}
                                onClick={() => {
                                  const link = document.createElement('a')
                                  link.href = img.url
                                  link.download = img.fileName
                                  link.click()
                                }}
                              />
                              <div className={styles.content}>
                                <p className={styles.pdfName}>{img?.display_name}</p>
                                {isEditForUser && (
                                  <DeleteIcon className={styles.deletePdf} onClick={() => setDeleteModel(img)} />
                                )}
                              </div>
                            </div>
                          ) : (
                            <div key={i} className={styles.imageWrapper}>
                              {img?.file_path?.toLowerCase().endsWith('.mp4') ? (
                                <video src={img.url} width={400} height={254} controls className={styles.videoBox} />
                              ) : (
                                <img src={img.url} alt="" width={400} height={254} className={styles.imageBox} />
                              )}
                              <div className={styles.content}>
                                <p className={styles.imageName}>{img?.display_name}</p>
                                {isEditForUser && (
                                  <div className={styles.iconWrapper}>
                                    {img?.type !== 'Plot Plan' && (
                                      <Tooltip title={'Add Phase'} arrow>
                                        <AddOutlined
                                          onClick={() => {
                                            setAddPhaseModel(true)
                                            setSelectedImage(img)
                                          }}
                                        />
                                      </Tooltip>
                                    )}
                                    <DeleteIcon className={styles.deleteIcon} onClick={() => setDeleteModel(img)} />
                                  </div>
                                )}
                              </div>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  )}
                </>
              )
            },
          )}
      </div>
      {/* <div className={styles.media}>
        {images[mediaCategory.header]?.map((img: any, index: number) =>
          img?.fileName?.includes('pdf') ? (
            <>
              <div className={styles.pdfWrapper} key={index}>
                <PictureAsPdfOutlined
                  className={styles.pdfIcon}
                  onClick={() => {
                    const link = document.createElement('a')
                    link.href = img.url
                    link.download = img.fileName
                    link.click()
                  }}
                />
                <div className={styles.content}>
                  <p className={styles.pdfName}>{img?.display_name}</p>
                  <DeleteIcon className={styles.deletePdf} onClick={() => setDeleteModel(img)} />
                </div>
              </div>
            </>
          ) : (
            <div key={index} className={styles.imageWrapper}>
              <img src={img.url} alt="" width={400} height={254} />
              <div className={styles.content}>
                <p className={styles.imageName}>{img?.display_name}</p>
                <div className={styles.iconWrapper}>
                  <Tooltip title={'Add Phase'} arrow>
                    <AddOutlined
                      onClick={() => {
                        setAddPhaseModel(true)
                        setSelectedImage(img)
                      }}
                    />
                  </Tooltip>
                  <DeleteIcon className={styles.deleteIcon} onClick={() => setDeleteModel(img)} />
                </div>
              </div>
            </div>
          ),
        )}
      </div> */}

      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDelete()}
      />
      <PulseModel
        closable={false}
        style={{ width: 'fitContent' }}
        open={addPhaseModel}
        onClose={() => setAddPhaseModel(false)}
        content={
          <AddPhaseModel
            lookup_project_to_phase_id={selectedImage?.lookup_project_to_phase_id}
            handleSubmit={handleSubmit}
            onClose={() => setAddPhaseModel(false)}
          />
        }
      />
    </div>
  )
}

export default MediaDisplay
