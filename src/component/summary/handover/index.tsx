import React, { useState } from 'react'
import styles from './Handover.module.scss'
import PhaseHandover from './phase'
import UniqueHandover from './unique'
import PulseAccordion from '../../shared/pulseAccordion'

const HandOver: React.FC = () => {
  const [expanded, setExpanded] = useState<string[]>([])

  // Handle accordion toggle
  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded((prevExpanded) => {
      if (isExpanded) {
        // Add panel to expanded list
        return [...prevExpanded, panel]
      } else {
        // Remove panel from expanded list
        return prevExpanded.filter((p) => p !== panel)
      }
    })
  }

  return (
    <div className={styles.handOverContainer}>
      {/* Phase Accordion */}
      <PulseAccordion
        expanded={expanded.includes('panel1')} // Allow multiple expansions
        onChange={handleAccordionChange('panel1')}
        summaryContent={
          <div className={styles.summaryContent}>
            <span>Phase / Sub-phase</span>
          </div>
        }
        detailsContent={<PhaseHandover />}
        className={styles.accordionSummary}
        summaryClassName={styles.accordionGlobalSummary}
        detailsClassName="global-details"
      />

      {/* Unique Accordion */}
      <PulseAccordion
        expanded={expanded.includes('panel2')} // Allow multiple expansions
        onChange={handleAccordionChange('panel2')}
        summaryContent={
          <div className={styles.summaryContent}>
            <span>Unit Tracking</span>
          </div>
        }
        detailsContent={<UniqueHandover />}
        className={styles.accordionSummary}
        summaryClassName={styles.accordionGlobalSummary}
        detailsClassName="global-details"
      />
    </div>
  )
}

export default HandOver
