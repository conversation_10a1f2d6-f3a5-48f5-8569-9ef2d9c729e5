.handOverContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1.5rem;
}

.accordionSummary {
  border-radius: 8px !important;
  margin: 0px !important;
  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  }
}

.accordionGlobalSummary {
  padding: 0 !important;
  padding-right: 15px !important;
}

.rotatedIcon {
  transform: rotate(90deg);
}

.global-details {
  padding: 1rem;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  color: #555;
  font-size: 0.95rem;
}

.summaryContent {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 8px 20px;
  background: #eeeeec;
  border-radius: 0 8px 8px 0;
  font-size: 16px;
  font-weight: 700;
  line-height: 18px;
  color: #444444;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  padding-right: 8px;
  min-width: 212px;

  span {
    font-size: 1.1rem;
    color: #333;
  }
}

@media (max-width: 768px) {
  .handOverContainer {
    padding: 1rem;
  }

  .global-summary {
    font-size: 1rem;
  }

  .global-details {
    font-size: 0.9rem;
  }
}
