import React, { useEffect, useMemo } from 'react'
import { InputAdornment } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import * as Yup from 'yup'
import styles from './AddSpaDrawer.module.scss'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import DatePicker from '@/src/component/shared/dataPicker'
import MultiAutoSelect from '@/src/component/shared/multiAutoSelect'
import NumberInputField from '@/src/component/shared/numberInputField'
import Textarea from '@/src/component/shared/textArea'
import TextInputField from '@/src/component/shared/textInputField'
import AedIcon from '@/src/component/svgImages/aedIcon'
import CheckIcon from '@/src/component/svgImages/checkIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { convertValuesToCommaSeparated, getUniquePhases } from '@/src/helpers/helpers'
import { useGetMilestoneNumber } from '@/src/hooks/useMilestoneNumber'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import { ISpa } from '@/src/redux/spaMileston/interface'
import useSpa from '@/src/redux/spaMileston/useSpaMileston'
import useStatus from '@/src/redux/status/useStatus'
import { getMasterOneProject } from '@/src/services/projects'
import {
  getUniqueValuesById,
  getUniqueValuesFromArray,
  getValue,
  populateDropdownOptions,
  prepareDropdownOptions,
  prepareMultiPhaseCategoryDropdownOptions,
} from '@/src/utils/arrayUtils'
import { convertDDMMYYYYToLongDate, isoToPayload, payloadDateFormate } from '@/src/utils/dateUtils'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'

interface AddCommercialProps {
  onClose: () => void
  onAddCommercial: (values: any) => void
  onUpdateCommercial: (values: any) => void
  editSpa: any
  selectedGroup: string
  buttonDisable: boolean
  setButtonDisable: Function
}

const milestoneTypeOptions = [
  'Signing of SPA',
  'Commencement',
  'Interim Milestone (No SPA)',
  'SPA - Physical',
  'SPA - Date Based',
  'Hand Over',
]
const milestoneNumberOptions = [
  'Interim No.1',
  'Interim No.2',
  'Interim No.3',
  'Interim No.4',
  'Interim No.5',
  'Interim No.6',
  'Interim No.7',
  'Interim No.8',
  'Interim No.9',
  'Interim No.10',
  'Interim No.11',
  'Interim No.12',
  'Section No.1',
  'Section No.2',
  'Section No.3',
  'Section No.4',
  'Section No.5',
  'Section No.6',
  'Section No.7',
  'Section No.8',
  'Section No.9',
  'Section No.10',
  'Section No.11',
  'Section No.12',
]

const AddSpaDrawer: React.FC<AddCommercialProps> = ({
  onClose,
  onAddCommercial,
  onUpdateCommercial,
  editSpa,
  selectedGroup,
  buttonDisable,
  setButtonDisable,
}) => {
  const { currentPeriod } = useMasterPeriod()
  //REACT-QUERY
  const isEditMode = useMemo(() => !!editSpa, [editSpa])
  const router = useRouter()
  const { getMasterPhaseApi } = usePhase()
  const { milestoneNumbers } = useGetMilestoneNumber()
  const { statuses } = useStatus()
  const { spas } = useSpa()

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data, // This extracts 'data' directly
    enabled: false,
  })

  const milestoneNumberOptions = useMemo(() => {
    return (
      milestoneNumbers?.map((res) => ({
        label: res?.milestone_number,
        value: res?.id,
      })) || []
    )
  }, [milestoneNumbers])

  const milestoneTypeStatus = ['Completed', 'Date Confirmed', 'Potential Risk', 'Major Risk', 'NA']
  const statusOptions = ['Not Started', 'Ongoing', 'Completed']
  const today = format(new Date(), 'dd-MM-yyyy')
  const todayDate: any = payloadDateFormate(today as string)

  const selectSpa: ISpa | undefined = spas.find((item: any) => item?.id === editSpa)

  const initialValues = useMemo(() => {
    return {
      lookup_project_to_phase_id: selectSpa?.LookupProjectToPhase?.id || null,
      milestone_description: selectSpa?.milestone_description || '',
      master_milestone_number_id: selectSpa?.MasterMilestoneNumber?.id,
      milestone_date: selectSpa?.milestone_date
        ? convertDDMMYYYYToLongDate(selectSpa?.milestone_date.toString())
        : null,
      forecasted_end_date: selectSpa?.forecasted_end_date
        ? convertDDMMYYYYToLongDate(selectSpa?.forecasted_end_date)
        : null,
      actual_forecast_milestone_completion: selectSpa?.actual_forecast_milestone_completion
        ? convertDDMMYYYYToLongDate(selectSpa?.actual_forecast_milestone_completion)
        : null,
      permit_and_land_clearances_date: selectSpa?.permit_and_land_clearances_date
        ? convertDDMMYYYYToLongDate(selectSpa?.permit_and_land_clearances_date)
        : null,
      baseline_end_date: selectSpa?.baseline_end_date ? convertDDMMYYYYToLongDate(selectSpa?.baseline_end_date) : null,
      revised_spa_date: selectSpa?.revised_spa_date ? convertDDMMYYYYToLongDate(selectSpa?.revised_spa_date) : null,
      actual_forecast_milestone_collection: selectSpa?.actual_forecast_milestone_collection
        ? convertDDMMYYYYToLongDate(selectSpa?.actual_forecast_milestone_collection)
        : null,
      eot: selectSpa?.eot ? convertDDMMYYYYToLongDate(selectSpa?.eot) : null,
      latest_communication_date: selectSpa?.latest_communication_date
        ? convertDDMMYYYYToLongDate(selectSpa?.latest_communication_date)
        : null,
      revised_spa_communication: selectSpa?.revised_spa_communication
        ? convertDDMMYYYYToLongDate(selectSpa?.revised_spa_communication)
        : null,
      SPA_Column_1: selectSpa?.SPA_Column_1?.toString() || '',
      SPA_Column_2: selectSpa?.SPA_Column_2?.toString() || '',
      SPA_Column_3: selectSpa?.SPA_Column_3?.toString() || '',
      SPA_Column_4: selectSpa?.SPA_Column_4?.toString() || '',
      milestone_status: selectSpa?.milestone_status || null,
      contract_variance: selectSpa?.contract_variance?.toString() || '',
      communication_variance: selectSpa?.communication_variance?.toString() || '',
      spa_variance: selectSpa?.spa_variance?.toString() || '',
      milestone_type: selectSpa?.milestone_type || '',
      duration_from_start: selectSpa?.duration_from_start || null,
      letter_reference: selectSpa?.letter_reference || '',
      // status: selectSpa?.milestone_status?.toString() || 'Not Started',
      delay_damage_per_diem: selectSpa?.delay_damage_per_diem || 0,
      conditions_precedence: selectSpa?.conditions_precedence?.toString() || '',
      justification: selectSpa?.justification?.toString() || '',
      remarks: selectSpa?.remarks?.toString() || '',
    }
  }, [editSpa])

  const validationSchema = useMemo(() => {
    return Yup.object().shape({
      SPA_Column_3: Yup.number().typeError('Please enter a valid number'),

      justification: Yup.string().when(
        ['milestone_status', 'actual_forecast_milestone_completion', 'forecasted_end_date'],
        {
          is: (milestone_status: string, govDate: Date | null, nonGovDate: Date | null) => {
            if (!isEditMode || !(milestone_status === 'Not Started' || milestone_status === 'Ongoing')) {
              return false
            }
            const isGovernment = project?.MasterEntityCategory?.entity_category === 'Government'
            const newDateValue = isGovernment ? govDate : nonGovDate
            const initialDateValue = isGovernment
              ? initialValues.actual_forecast_milestone_completion
              : initialValues.forecasted_end_date

            if (!newDateValue || !initialDateValue) {
              return false
            }

            const newDate = new Date(newDateValue)
            const initialDate = new Date(initialDateValue)

            if (isNaN(newDate.getTime()) || isNaN(initialDate.getTime())) {
              return false
            }

            newDate.setHours(0, 0, 0, 0)
            initialDate.setHours(0, 0, 0, 0)

            const hasDateChanged = newDate.getTime() !== initialDate.getTime()
            return hasDateChanged && newDate > initialDate
          },
          then: (schema) => schema.trim().required('Justification is required'),
          otherwise: (schema) => schema.notRequired(),
        },
      ),
    })
  }, [isEditMode, project, initialValues])

  const formik = useFormik({
    initialValues: initialValues,
    enableReinitialize: true,
    validationSchema,
    onSubmit: async (values) => {
      setButtonDisable(true)
      if (isEditMode) {
        await onUpdateCommercial({
          ...selectSpa,
          ...values,
          period: currentPeriod,
        })

        formik.resetForm()
      } else {
        await onAddCommercial({
          ...values,
          project_name: router.query.slug as string,
          period: currentPeriod,
        })
        formik.resetForm()
      }
    },
  })

  const { values } = formik

  const showSlippageField = useMemo(() => {
    const { milestone_status, actual_forecast_milestone_completion } = values
    if (!isEditMode || !(milestone_status === 'Not Started' || milestone_status === 'Ongoing')) {
      return false
    }

    const newDateValue = actual_forecast_milestone_completion
    const initialDateValue = initialValues.actual_forecast_milestone_completion
    if (!newDateValue || !initialDateValue) {
      return false
    }

    const newDate = new Date(newDateValue)
    const initialDate = new Date(initialDateValue)

    if (isNaN(newDate.getTime()) || isNaN(initialDate.getTime())) {
      return false
    }

    newDate.setHours(0, 0, 0, 0)
    initialDate.setHours(0, 0, 0, 0)

    const hasDateChanged = newDate.getTime() !== initialDate.getTime()

    return hasDateChanged && newDate > initialDate
  }, [
    isEditMode,
    values.milestone_status,
    values.actual_forecast_milestone_completion,
    values.forecasted_end_date,
    initialValues,
    project?.MasterEntityCategory?.entity_category,
  ])

  // TODO: comment old code
  // const phaseOptionsData = useMemo(() => {
  //   let data = populateDropdownOptions(
  //     statuses.filter((item: any) => item?.project_name === router.query.slug),
  //     'LookupProjectToPhase',
  //   ).flat()

  //   return getUniqueValuesById(data)
  //   // return populateDropdownOptions(
  //   //   statuses.filter((item: any) => item?.project_name === router.query.slug),
  //   //   'LookupProjectToPhase',
  //   // )
  // }, [statuses, router.query.slug])

  const phaseOptionsData = useMemo(
    () =>
      populateDropdownOptions(
        statuses.filter((item: any) => item?.project_name === router.query.slug),
        'phase',
      ),
    [statuses, router.query.slug],
  )

  const phaseOptionsWithStageStatus = useMemo(() => {
    const statusesWithStageStatus = statuses.filter((item: any) => item?.project_name === router.query.slug)
    const phaseOptionsWithStageStatus = statusesWithStageStatus?.map((item: any) => {
      return {
        phase: item?.LookupProjectToPhase,
        stage_status: item?.MasterProjectStageStatus.project_stage_status,
      }
    })
    return phaseOptionsWithStageStatus
  }, [statuses, router.query.slug])

  useEffect(() => {
    getMasterPhaseApi()
  }, [])

  const REGEX = useMemo(() => /^[0-9]*(\.[0-9]*)?$/, [])

  // TODO: comment old code
  // const projectPhaseOption: any = useMemo(() => {
  //   const optionMap = new Map()
  //   statuses.forEach((item: any) => {
  //     if (item.phase?.includes(MULTI_SELECT_SEPARATOR)) {
  //       const multiCategories = item.phase.split(MULTI_SELECT_SEPARATOR)
  //       multiCategories.forEach((category: any) => {
  //         optionMap.set(category, { id: category, name: category })
  //       })
  //     } else {
  //       optionMap.set(item.phase, { id: item.phase, name: item.phase })
  //     }
  //   })
  //   return Array.from(optionMap.values()).filter((item: any) => item.name?.length)
  // }, [statuses])

  // const getMultiSelectedValue = (value: string) => {
  //   if (value.includes(MULTI_SELECT_SEPARATOR)) {
  //     return value.split(MULTI_SELECT_SEPARATOR)
  //   }
  //   return typeof value === 'string' ? [value] : value
  // }

  // const handlePhaseChange = (value: string[]) => {
  //   formik.setValues({
  //     ...formik.values,
  //     lookup_project_to_phase_id: value?.filter((item: string) => item.length).join(MULTI_SELECT_SEPARATOR),
  //   })
  // }

  const phaseOptions = useMemo(() => {
    const phaseOptionsWithConstructionStageStatus = phaseOptionsWithStageStatus
      .filter((item: any) => item?.stage_status === 'Construction')
      ?.map((item: any) => {
        return item?.phase
      })

    const uniquePhases = getUniqueValuesById(phaseOptionsWithConstructionStageStatus.flat())

    return uniquePhases
      ?.map((item: any) => {
        return {
          label: item?.phase,
          value: item?.id,
        }
      })
      .filter((item: any) => item?.label)
  }, [statuses, router.query.slug])

  return (
    <div className={styles.container}>
      <form className={styles.form} onSubmit={formik.handleSubmit}>
        <div className={styles.header}>
          <div className={styles.headerTitle}>{isEditMode ? 'Edit SPA & Milestones' : 'Add SPA & Milestones'}</div>
          <div className={styles.actionButtons}>
            {/* disabled={!formik.dirty || buttonDisable} */}
            <Button
              disabled={!(formik.isValid && formik.dirty)}
              startIcon={isEditMode ? <CheckIcon /> : <SaveIcon />}
              type="submit"
            >
              {isEditMode ? 'Update' : 'Save'}
            </Button>
            <Button
              className={styles.closeButton}
              color="secondary"
              onClick={() => {
                formik.resetForm()
                onClose()
              }}
            >
              X Close
            </Button>
          </div>
        </div>
        <div className={styles.content}>
          {/* Description */}
          <ComboBox
            options={phaseOptions}
            labelText={'Phase'}
            placeholder="Type Something..."
            clearIcon={true}
            value={
              formik.values?.lookup_project_to_phase_id
                ? getValue(phaseOptions, formik.values?.lookup_project_to_phase_id)
                : null
            }
            // onChange={(val) => handlePhaseChange(val)}
            onChange={(val) => formik.setValues({ ...formik.values, lookup_project_to_phase_id: val?.value || null })}
          />
          {/* <MultiAutoSelect
            labelText="Phase/Package"
            placeholder="Select phase..."
            isSubOption={false}
            isSx={false}
            options={projectPhaseOption}
            value={getMultiSelectedValue(formik.values.phase) as unknown as string[]}
            handleSelectedOption={(val) => handlePhaseChange(val)}
            className={styles.multiSelect}
          /> */}
          {/*for now project?.MasterEntityCategory?.entity_category is (Non Government,Government,null)*/}
          {project?.MasterEntityCategory?.entity_category !== 'Government' && (
            <>
              <DatePicker
                name="milestone_date"
                labelText="SPA Date"
                placeholder="DD-MM-YY"
                value={values.milestone_date || null}
                onChange={(date) => {
                  formik.setFieldValue('milestone_date', date)
                }}
                onBlur={formik.handleBlur}
              />
              <ComboBox
                options={milestoneTypeOptions}
                labelText={'Milestone type'}
                placeholder="Type Something..."
                clearIcon={true}
                value={values?.milestone_type ? { label: values?.milestone_type, value: values?.milestone_type } : null}
                onChange={(val) => formik.setValues({ ...formik.values, milestone_type: val?.value || '' })}
              />
              <ComboBox
                options={milestoneTypeStatus}
                labelText="Milestone Status"
                placeholder="Type Something..."
                value={
                  formik.values?.milestone_status
                    ? { label: formik.values?.milestone_status, value: formik.values?.milestone_status }
                    : null
                }
                clearIcon={true}
                onChange={(val) => {
                  formik.setValues({ ...formik.values, milestone_status: val?.value || null })
                }}
              />
              <DatePicker
                name="actual_forecast_milestone_collection"
                labelText="Actual /Forecast Milestone Collection"
                placeholder="DD-MM-YY"
                value={values.actual_forecast_milestone_collection || null}
                onChange={(date) => formik.setFieldValue('actual_forecast_milestone_collection', date)}
                onBlur={formik.handleBlur}
              />
              <DatePicker
                name="revised_spa_communication"
                labelText="Revised SPA Communication"
                placeholder="DD-MM-YY"
                value={values.revised_spa_communication || null}
                onChange={(date) => formik.setFieldValue('revised_spa_communication', date)}
                onBlur={formik.handleBlur}
              />
              <DatePicker
                name="baseline_end_date"
                labelText="Contract Date"
                placeholder="DD-MM-YY"
                value={formik.values.baseline_end_date || null}
                onChange={(date) => formik.setFieldValue('baseline_end_date', date)}
                onBlur={formik.handleBlur}
              />
              <DatePicker
                name="forecasted_end_date"
                labelText="Actual /Forecast Milestone Completion"
                placeholder="DD-MM-YY"
                value={formik.values.forecasted_end_date || null}
                onChange={(date) => formik.setFieldValue('forecasted_end_date', date)}
                onBlur={formik.handleBlur}
                minDate={
                  formik.values?.SPA_Column_4 === 'Not Started' || formik.values?.SPA_Column_4 === 'Ongoing'
                    ? new Date()
                    : ''
                }
              />
              <DatePicker
                name="eot"
                labelText="EOT"
                placeholder="DD-MM-YY"
                value={formik.values.eot || null}
                onChange={(date) => formik.setFieldValue('eot', date)}
                onBlur={formik.handleBlur}
              />
              {/* // TODO : 158 Change the name of the field */}
              <DatePicker
                name="latest_communication_date"
                labelText="Latest communication date"
                placeholder="DD-MM-YY"
                value={formik.values.latest_communication_date || null}
                onChange={(date) => formik.setFieldValue('latest_communication_date', date)}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                name="letter_reference"
                labelText="Letter Reference"
                placeholder="Type something ..."
                value={values.letter_reference}
                className={styles.textArea}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                variant={'outlined'}
              />
              <ComboBox
                options={milestoneNumberOptions}
                labelText={'Milestone Number'}
                placeholder="Type Something..."
                value={
                  formik.values.master_milestone_number_id
                    ? getValue(milestoneNumberOptions, formik.values.master_milestone_number_id)
                    : null
                }
                clearIcon={true}
                onChange={(val) => formik.setValues({ ...formik.values, master_milestone_number_id: val?.value || 0 })}
              />
              <Textarea
                name="milestone_description"
                labelText={'Milestone Description'}
                onChange={(e) => formik.setFieldValue('milestone_description', e.target.value)}
                value={values.milestone_description}
                onBlur={formik.handleBlur}
                placeholder="Type something ..."
              />
              {/* {showSlippageField && (
                <Textarea
                  name="justification"
                  labelText={'Justification'}
                  onChange={(e) => formik.setFieldValue('justification', e.target.value)}
                  value={values.justification}
                  onBlur={formik.handleBlur}
                  placeholder="Type something ..."
                  required={showSlippageField}
                />
              )} */}
            </>
          )}
          {project?.MasterEntityCategory?.entity_category === 'Government' && (
            <>
              <ComboBox
                options={statusOptions}
                labelText="Status"
                placeholder="Type Something..."
                value={
                  formik.values?.milestone_status
                    ? { label: formik.values?.milestone_status, value: formik.values?.milestone_status }
                    : null
                }
                clearIcon={true}
                onChange={(val) => formik.setValues({ ...formik.values, milestone_status: val?.value || 0 })}
              />
              <NumberInputField
                name="duration_from_start"
                labelText="Duration from Start (Days)"
                placeholder="Type something ..."
                value={values.duration_from_start}
                className={styles.textArea}
                onChange={(val) => {
                  // Remove all non-digit characters
                  const onlyNumbers = String(val)?.replace(/\D/g, '')
                  // Optional: prevent leading zeros
                  const sanitizedValue = onlyNumbers?.replace(/^0+(?=\d)/, '')
                  formik.setFieldValue('duration_from_start', sanitizedValue)
                }}
                onBlur={formik.handleBlur}
                isUpAndDowns={false}
              />
              <DatePicker
                name="actual_forecast_milestone_completion"
                labelText="Forecast/Actual Completion Date"
                placeholder="DD-MM-YY"
                value={values.actual_forecast_milestone_completion || null}
                onChange={(date) => formik.setFieldValue('actual_forecast_milestone_completion', date)}
                onBlur={formik.handleBlur}
                minDate={
                  formik.values?.milestone_status === 'Not Started' || formik.values?.milestone_status === 'Ongoing'
                    ? new Date()
                    : ''
                }
              />
              <NumberInputField
                isUpAndDowns={false}
                name="delay_damage_per_diem"
                labelText="Delay Damage per diem (AED)"
                value={
                  values?.delay_damage_per_diem
                    ? (formatNumberWithCommas(Number(values?.delay_damage_per_diem)) as string)
                    : ''
                }
                format="comma-separated"
                onChange={(value) => {
                  formik.setFieldValue('delay_damage_per_diem', parseInt(String(value)) || 0)
                }}
                onBlur={formik.handleBlur}
                placeholder="Type something ..."
                endAdornment={
                  <InputAdornment position="start" className={styles.endAdornment}>
                    <AedIcon className={styles.endAdornmentIcon} />
                  </InputAdornment>
                }
              />
              <TextInputField
                name="remarks"
                labelText="Remarks"
                placeholder="Type something ..."
                value={values.remarks}
                className={styles.textArea}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                variant={'outlined'}
              />
              <Textarea
                name="milestone_description"
                labelText={'Milestone Description'}
                onChange={(e) => formik.setFieldValue('milestone_description', e.target.value)}
                value={values.milestone_description}
                onBlur={formik.handleBlur}
                placeholder="Type something ..."
              />
              <ComboBox
                options={milestoneNumberOptions}
                labelText={'Milestone Number'}
                placeholder="Type Something..."
                value={
                  formik.values.master_milestone_number_id
                    ? getValue(milestoneNumberOptions, formik.values.master_milestone_number_id)
                    : null
                }
                clearIcon={true}
                onChange={(val) => formik.setValues({ ...formik.values, master_milestone_number_id: val?.value || 0 })}
              />
              <Textarea
                name="conditions_precedence"
                labelText={'Conditions Precedence'}
                onChange={(e) => formik.setFieldValue('conditions_precedence', e.target.value)}
                value={values.conditions_precedence}
                onBlur={formik.handleBlur}
                placeholder="Type something ..."
              />
              <Textarea
                name="justification"
                labelText={'Justification'}
                onChange={(e) => formik.setFieldValue('justification', e.target.value)}
                value={values.justification}
                onBlur={formik.handleBlur}
                placeholder="Type something ..."
                required={showSlippageField}
              />
            </>
          )}
        </div>
      </form>
    </div>
  )
}

export default AddSpaDrawer
