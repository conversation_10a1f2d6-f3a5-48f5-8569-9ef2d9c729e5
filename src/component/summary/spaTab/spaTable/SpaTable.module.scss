.actions {
  display: flex;
  gap: 10px;
  flex-direction: row;
}
.editRowIcon,
.CorrectIcon,
.deleteRowIcon {
  cursor: pointer;
}
.spaTableContainer {
  margin-top: 20px;
  position: relative;
  height: calc(100% - 40px);
  overflow: auto;
  @media (max-width: 1020px) {
    height: calc(100% - 40px);
  }

  :global(.SPATableWrapper) {
    max-height: 100% !important;
    overflow: auto !important;
  }
}

.textEditor {
  margin-top: 15px;
  .title {
    font-family: Poppins;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: #808080;
  }
}
// .expandTable {
//   max-height: calc(100vh - 250px) !important;
// }
