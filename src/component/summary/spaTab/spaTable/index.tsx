import React, { useMemo, useRef, useState, useEffect } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import styles from './SpaTable.module.scss'
import { ISpaTableProps } from '../interface'
import LetterReferenceCell from '../letterRefCell'
import { preparePayload, filterByProjectName, getSpaData } from '../utils'
import DragCell from '@/src/component/customCells/dragCell'
import RichTextEditor from '@/src/component/richTextEditor'
import LineClampWithTooltip from '@/src/component/shared/lineClampWithTooltip'
import PulseModel from '@/src/component/shared/pulseModel'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { dateSortingFn, singleAssociationValueSorting } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import ValidationModel from '@/src/component/updateProgress/progressForm/validationModel'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { ILetterRefAttach } from '@/src/redux/spaMileston/interface'
import useSpa from '@/src/redux/spaMileston/useSpaMileston'
import { getMasterOneProject } from '@/src/services/projects'
import { dateNonRequiredFields, sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'
import { DDMMYYYYformate } from '@/src/utils/dateUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const SpaTable: React.FC<ISpaTableProps> = ({
  isEditTable,
  setIsOpenDrawer,
  setEditSpa,
  setDeleteModel,
  setSpaData,
  spaData,
  group,
  expanded,
  gridFilters,
  setGridFilters,
}) => {
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { getSpaApi, updateSpaApi, updatePDFSpaApi, sortSpaApi } = useSpa()
  const router = useRouter()
  const slippageJustification = useRef<string | null>(null)
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState<string[]>([])

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const getTextFromHtml = (htmlString: string | null): string | null => {
    if (!htmlString) return null

    // Create a temporary DOM element to parse the HTML
    const div = document.createElement('div')
    div.innerHTML = htmlString

    // Use textContent or innerText to get the plain text
    const text = div.textContent || div.innerText || ''

    return text.trim() // Trim whitespace from the start and end
  }

  const onCellUpdate = async (cell: any, newValue: any, row: any): Promise<boolean> => {
    if (!row) return false
    try {
      const status = row.milestone_status?.toLowerCase() || ''
      const createFormData = (payload: Record<string, any>, cell: any) => {
        const formData = new FormData()
        const formObject: Record<string, any> = {}

        Object.entries(payload).forEach(([key, value]) => {
          if (value != null || dateNonRequiredFields(key, cell?.columnId?.toString())) {
            formObject[key] = value
          }
          // if (value != null || dateNonRequiredFields(key, cell?.columnId?.toString())) formData.append(key, value)
        })

        return formObject
      }

      const justificationText = getTextFromHtml(slippageJustification.current)
      const payload = preparePayload(row, cell, newValue)

      if (cell?.columnId === 'actual_forecast_milestone_completion') {
        const isNotCompleted = status === 'not started' || status === 'ongoing'
        let isDelayed = false

        const newDate = parseDateString(newValue)

        const oldRowData = spaData.find((data) => data.id === row.id)
        const oldDate = oldRowData ? parseDateString(oldRowData.actual_forecast_milestone_completion) : null

        if (newDate && oldDate && isNotCompleted) {
          newDate.setHours(0, 0, 0, 0)
          oldDate.setHours(0, 0, 0, 0)

          if (newDate > oldDate) {
            isDelayed = true
          }
        }

        if (isDelayed && !justificationText) {
          setValidationMessage(['Justification is required when a milestone date is changed.'])
          setIsValidationModel(true)
          return false
        }
      }

      const { period, phase, project_name, id } = row || {}
      const updatedPayload = {
        ...payload,

        period: currentPeriod,

        phase: phase,

        project_name: router.query.slug?.toString() as string,

        justification: justificationText || null,
      }
      const response: Record<string, any> = await updateSpaApi({
        id: id,
        data: createFormData(updatedPayload, cell),
      })

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || 'Failed to update cell')
        return false
      }
      const res: Record<string, any> = await getSpaApi({
        period: currentPeriod,
        project_name: router.query.slug as string,
      })

      if (!res?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || `Failed to fetch updated data`)
        return false
      }
      const formateData = getSpaData(res.payload.data)
      setSpaData(filterByProjectName(formateData, router.query.slug as string) as any)
      return true
    } catch (error) {
      errorToast('Failed...')
      return false
    }
  }

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    row: any,
    onStart?: () => void,
    onFinish?: () => void,
  ) => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
      return
    }
    const file = event.target.files?.[0]
    if (!file) return

    try {
      onStart?.() // Start loader

      const formData = new FormData()
      const { LookupProjectToPhase, id } = row?.original || {}

      formData.append('period', currentPeriod)
      formData.append('project_name', router.query.slug as string)
      formData.append('project_milestone_id', id || null)
      formData.append('files', file)

      const response: Record<string, any> = await updatePDFSpaApi({ data: formData })

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message || 'Upload failed')
        return true
      }

      const res: Record<string, any> = await getSpaApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })

      if (!res?.payload?.success) {
        errorToast(res?.payload?.response?.data?.message || 'Failed to fetch data')
        return true
      }

      successToast('Letter Reference Attachment Successfully Uploaded')
    } catch (error) {
      console.error('Error uploading file:', error)
      errorToast('An unexpected error occurred')
      return true
    } finally {
      onFinish?.() // Stop loader
    }

    return true
  }

  const handleDownload = (letter_reference_attachment: ILetterRefAttach[]) => {
    if (!isEditForUser) {
      toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
      return
    }
    if (!letter_reference_attachment) {
      toast('No attachment found')
      return
    }
    const a = document.createElement('a')
    a.href = letter_reference_attachment[0]?.url
    a.download = letter_reference_attachment[0]?.name // This may not work for all URLs (e.g., if the server doesn't allow it)
    a.click()
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'dragCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'center',
    },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }) => (
        <>
          {isEditForUser ? (
            <div className={styles.actions}>
              <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.original.id)} />
              <EditIcon
                className={styles.deleteRowIcon}
                onClick={() => {
                  setIsOpenDrawer(true)
                  setEditSpa(row.original.id)
                }}
              />
            </div>
          ) : (
            <></>
          )}
        </>
      ),
      size: 70,
    },
    { accessorKey: 'milestone_sorting_order', header: 'ID', size: 100 },
    {
      accessorKey: 'phase',
      childAccessorKey: ['LookupProjectToPhase'],
      filterType: 'wildcard-single-association',
      header: 'Phase',
      size: 150,
      sortingFn: (...data) => singleAssociationValueSorting(...data, 'Single phase'),
      cell: ({ row }) => {
        const val = row.original?.LookupProjectToPhase?.phase || '_'
        return <span>{val || ' '}</span>
      },
    },
    {
      accessorKey: 'milestone_number',
      header: 'Milestone Number',
      size: 170,
      // cell: ({ row }) => {
      //   const val = row.original?.MasterMilestoneNumber?.milestone_number || ''
      //   return <span>{val}</span>
      // },
    },
    {
      accessorKey: 'milestone_description',
      header: 'Milestone Description',
      size: 450,
      flex: 1,
      cell: ({ row }) => {
        const val = row.original.milestone_description
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
    },
  ]
  const parseDateString = (dateString: any) => {
    if (!dateString) {
      return null
    }

    const parts = dateString.split('-')
    if (parts.length === 3) {
      const day = parseInt(parts[0], 10)
      const month = parseInt(parts[1], 10) - 1 // Month is 0-indexed (0–11)
      const year = parseInt(parts[2], 10)
      const parsedDate = new Date(year, month, day)

      if (parsedDate.getFullYear() === year && parsedDate.getMonth() === month && parsedDate.getDate() === day) {
        return parsedDate
      }
    }

    const defaultParsedDate = new Date(dateString)
    if (!isNaN(defaultParsedDate.getTime())) {
      return defaultParsedDate
    }

    return null
  }

  const governmentColumns: CustomColumnDef<any>[] = [
    ...columns,
    {
      accessorKey: 'duration_from_start',
      header: 'Duration from Start (Days)',
      size: 220,
    },
    {
      accessorKey: 'planned_completion_date',
      header: 'Planned Completion Date',
      size: 210,
      filterType: 'date',
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      tableId: 'SPAGovernmentTable',
    },
    {
      accessorKey: 'remaining_duration',
      header: 'Remaining in Days',
      size: 170,
    },
    {
      accessorKey: 'actual_forecast_milestone_completion',
      header: 'Forecasted / Actual Completion Date',
      size: 285,
      isSaveConfirmationRequired: true,
      isEditableCell: isEditTable,
      editableType: 'date',
      filterType: 'date',
      minDate: (row: any) => {
        const isCheckArray = ['not started', 'ongoing']
        if (isCheckArray.includes(row?.original?.milestone_status?.toLowerCase())) {
          return new Date()
        }
        return null
      },
      onEditCell: (cell, newValue, row) => {
        onCellUpdate(cell, newValue, row)
      },
      renderInputWithConfirmationMessage: (cell, newValue) => {
        slippageJustification.current = cell?.row?.original?.justification
        const status = cell?.row?.original?.milestone_status?.toLowerCase() || ''
        const isNotCompleted = status === 'not started' || status === 'ongoing'
        let isPastDate = false

        if (newValue) {
          const dateParts = newValue.split('-')
          const newDate = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0])
          newDate.setHours(0, 0, 0, 0)

          const oldRow = spaData.find((data) => data.id === cell?.row?.id)
          const oldDateRaw = oldRow && parseDateString(oldRow.actual_forecast_milestone_completion)

          if (!oldDateRaw) return

          const oldDateObj = new Date(oldDateRaw)
          if (isNaN(oldDateObj.getTime())) {
            return
          }

          oldDateObj.setHours(0, 0, 0, 0)
          if (newDate > oldDateObj && isNotCompleted) {
            isPastDate = true
          }
        }
        if (isNotCompleted && isPastDate) {
          return (
            <div className={styles.textEditor}>
              <div className={styles.title}> Justification </div>
              <RichTextEditor
                value={slippageJustification?.current || ''}
                handleChange={(val) => {
                  return (slippageJustification.current = val)
                }}
                isEdit={true}
                className={styles.textEditor}
              />
            </div>
          )
        }
        return null
      },
      sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
    },
    {
      accessorKey: 'variance_in_days',
      header: 'Variance (In Days)',
      size: 170,
      tableId: 'SPAGovernmentTable',
    },
    {
      accessorKey: 'milestone_status',
      header: 'Status',
      size: 120,
    },
    {
      accessorKey: 'delay_damage_per_diem',
      header: 'Delay Damage per diem (AED)',
      size: 240,
    },
    {
      accessorKey: 'conditions_precedence',
      header: 'Conditions Precedence',
      size: 350,
      cell: ({ row }) => {
        const val = row.original.conditions_precedence
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
    },
    {
      accessorKey: 'remarks',
      header: 'Remarks',
      size: 300,
      cell: ({ row }) => {
        const val = row.original.remarks
        return (
          <TypographyField
            style={{ fontSize: '12px', color: 'black', marginLeft: '5px' }}
            variant={'body1'}
            text={val || ' '}
          />
        )
      },
    },
    {
      accessorKey: 'justification',
      header: 'Justification',
      size: 150,
      cell: ({ row }) => {
        const val = row.original.justification
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
    },
  ]

  const nonGovernmentColumnsA: CustomColumnDef<any>[] = useMemo(
    () => [
      {
        accessorKey: 'dragCol',
        header: '',
        cell: ({ row }) => <DragCell rowId={row?.id} />,
        size: 40,
        align: 'center',
      },
      {
        accessorKey: 'actionCol',
        header: 'Action',
        cell: ({ row }) => (
          <>
            {isEditForUser ? (
              <div className={styles.actions}>
                <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.original.id)} />
                <EditIcon
                  className={styles.deleteRowIcon}
                  onClick={() => {
                    setIsOpenDrawer(true)
                    setEditSpa(row.original.id)
                  }}
                />
              </div>
            ) : (
              <></>
            )}
          </>
        ),
        size: 70,
      },
      { accessorKey: 'milestone_sorting_order', header: 'ID', size: 100 },
      {
        accessorKey: 'phase',
        childAccessorKey: ['LookupProjectToPhase'],
        filterType: 'wildcard-single-association',
        header: 'Phase',
        size: 150,
        sortingFn: (...data) => singleAssociationValueSorting(...data, 'Single phase'),
        cell: ({ row }) => {
          const val = row.original?.LookupProjectToPhase?.phase || '-'
          return <span>{val}</span>
        },
      },
      {
        accessorKey: 'milestone_number',
        header: 'Milestone Number',
        size: 170,
        cell: ({ row }) => {
          const val = row.original?.MasterMilestoneNumber?.milestone_number || ''
          return <span>{val}</span>
        },
      },
      {
        accessorKey: 'milestone_description',
        header: 'Milestone Description',
        size: 450,
        cell: ({ row }) => {
          const val = row.original.milestone_description
          return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
        },
      },
      {
        accessorKey: 'milestone_type',
        header: 'Milestone Type',
        size: 200,
      },
      {
        accessorKey: 'milestone_date',
        header: 'SPA Date',
        size: 120,
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => {
          onCellUpdate(cell, newValue, row)
        },
        //TODO: Fix date format issue
        // cell: ({ row }) => {
        //   const val = DDMMYYYYformate(row.original.milestone_date)
        //   return <TypographyField style={{ fontSize: '12px', color: 'black' }} variant={'body1'} text={val || ' '} />
        // },
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'revised_spa_communication',
        header: 'Revised SPA Communication',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 230,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'actual_forecast_milestone_collection',
        header: 'Actual / Forecast Milestone Collection',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 280,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'latest_communication_date',
        header: 'Latest Communication Date',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 300,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'communication_variance',
        header: 'Comm Var',
        tableId: 'SPAGroupATable',
      },
      {
        accessorKey: 'spa_variance',
        header: 'SPA Variance',
        size: 150,
        tableId: 'SPAGroupATable',
      },
      {
        accessorKey: 'milestone_status',
        header: 'Milestone Status',
        // cell: ({ row }) => {
        //   const val = row.original.SPA_Column_4
        //   return (
        //     <TypographyField
        //       style={{ fontSize: '12px', color: 'black', marginLeft: '3px' }}
        //       variant={'body1'}
        //       text={val || ' '}
        //     />
        //   )
        // },
        // cell: ({ row }) => {
        //   const val = row.original.SPA_Column_4?.toLocaleString('en-IN')
        //   return (
        //     <TypographyField
        //       sx={'body1'}
        //       style={{ fontSize: '12px', color: 'black' }}
        //       text={val ? val.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ' '}
        //     />
        //   )
        // },
        flex: 1,
      },
      {
        accessorKey: 'letter_reference',
        header: 'Letter Reference',
        size: 300,
        cell: ({ row }) => {
          let pdfContent = row.original?.MediaFiles?.filter((item: any) => item.media_type === 'milestones_files')
          return (
            <LetterReferenceCell
              value={row.original.letter_reference}
              attachments={pdfContent}
              onUpload={(event: any, onStart: any, onFinish: any) => handleFileUpload(event, row, onStart, onFinish)}
              onDownload={() => handleDownload(pdfContent)}
              isEditForUser={isEditForUser}
            />
          )
        },
      },
    ],
    [isEditTable],
  ) // Dependencies to re-compute when changed

  const nonGovernmentColumnsB: CustomColumnDef<any>[] = useMemo(
    () => [
      {
        accessorKey: 'dragCol',
        header: '',
        cell: ({ row }) => <DragCell rowId={row?.id} />,
        size: 40,
        align: 'center',
      },
      {
        accessorKey: 'actionCol',
        header: 'Action',
        cell: ({ row }) => (
          <>
            {isEditForUser ? (
              <div className={styles.actions}>
                <DeleteIcon className={styles.deleteRowIcon} onClick={() => setDeleteModel(row.original.id)} />
                <EditIcon
                  className={styles.deleteRowIcon}
                  onClick={() => {
                    setIsOpenDrawer(true)
                    setEditSpa(row.original.id)
                  }}
                />
              </div>
            ) : (
              <></>
            )}
          </>
        ),
        size: 70,
      },
      { accessorKey: 'milestone_sorting_order', header: 'ID', size: 100 },
      {
        accessorKey: 'phase',
        childAccessorKey: ['LookupProjectToPhase'],
        filterType: 'wildcard-single-association',
        header: 'Phase',
        size: 150,
        sortingFn: (...data) => singleAssociationValueSorting(...data, 'Single phase'),
        cell: ({ row }) => {
          const val = row.original?.LookupProjectToPhase?.phase || '-'
          return <span>{val || ' '}</span>
        },
      },
      {
        accessorKey: 'milestone_number',
        header: 'Milestone Number',
        size: 170,
        cell: ({ row }) => {
          const val = row.original?.MasterMilestoneNumber?.milestone_number || ''
          return <span>{val}</span>
        },
      },
      {
        accessorKey: 'milestone_description',
        header: 'Milestone Description',
        size: 450,
        cell: ({ row }) => {
          const val = row.original.milestone_description
          return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
        },
      },
      {
        accessorKey: 'baseline_end_date',
        header: 'Contract Date',
        flex: 1,
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        // cell: ({ row }) => {
        //   // const val = DDMMYYYYformate(row.original.baseline_end_date)
        //   return (
        //     <TypographyField
        //       style={{ fontSize: '12px', color: 'black' }}
        //       variant={'body1'}
        //       text={row.original.baseline_end_date || ' '}
        //     />
        //   )
        // },
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'eot',
        header: 'EOT',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
        size: 120,
      },
      {
        accessorKey: 'forecasted_end_date',
        header: 'Actual / Forecast Milestone Completion',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 300,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'latest_communication_date',
        header: 'Latest Communication Date',
        isSaveConfirmationRequired: true,
        isEditableCell: isEditTable,
        editableType: 'date',
        filterType: 'date',
        onEditCell: (cell, newValue, row) => onCellUpdate(cell, newValue, row),
        size: 300,
        sortingFn: (...rest) => dateSortingFn(...rest, 'dd-MM-yyyy'),
      },
      {
        accessorKey: 'communication_variance',
        header: 'Comm Var',
        tableId: 'SPAGroupBTable',
      },
      {
        accessorKey: 'contract_variance',
        header: 'SPA Variance',
        size: 150,
        tableId: 'SPAGroupBTable',
      },
      {
        accessorKey: 'milestone_status',
        header: 'Milestone Status',
        // cell: ({ row }) => {
        //   const val = row.original.SPA_Column_4?.toLocaleString('en-IN')
        //   return (
        //     <TypographyField
        //       sx={'body1'}
        //       style={{ fontSize: '12px' }}
        //       text={val ? val.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ' '}
        //     />
        //   )
        // },
        flex: 1,
      },
      {
        accessorKey: 'letter_reference',
        header: 'Letter Reference',
        size: 300,
        cell: ({ row }) => {
          let pdfContent = row.original?.MediaFiles?.filter((item: any) => item.media_type === 'milestones_files')
          return (
            <LetterReferenceCell
              value={row.original.letter_reference}
              attachments={pdfContent}
              onUpload={(event: any, onStart: any, onFinish: any) => handleFileUpload(event, row, onStart, onFinish)}
              onDownload={() => handleDownload(pdfContent)}
              isEditForUser={isEditForUser}
            />
          )
        },
      },
    ],
    [isEditTable],
  ) // Dependencies to re-compute when changed

  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    // const dragItem = spas.find((item: any) => item.id == dragId)
    // const dropItem = spas.find((item: any) => item.id == dropId)

    const newPayload = data?.map((item: any, index: number) => ({
      id: item?.id,
      milestone_sorting_order: 1 + index,
    }))

    const res: any = await sortSpaApi({ period: currentPeriod, milestoneRecords: newPayload })

    if (res?.payload.success) {
      await getSpaApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })
    }
  }
  return (
    <>
      <div className={`${styles.spaTableContainer} ${!expanded && styles.expandTable}`}>
        {project?.MasterEntityCategory?.entity_category === 'Government' && (
          <TanStackTable
            rows={sortArrayByKeyWithTypeConversion(spaData, 'milestone_sorting_order', true)}
            columns={governmentColumns}
            onDragEnd={handleDragAndDrop}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            className="SPATableWrapper"
            stickyColumnCount={5} // Adjust sticky columns for government table
          />
        )}
        {project?.MasterEntityCategory?.entity_category !== 'Government' && group === 'A' && (
          <TanStackTable
            rows={sortArrayByKeyWithTypeConversion(spaData, 'milestone_sorting_order', true)}
            columns={nonGovernmentColumnsA}
            onDragEnd={handleDragAndDrop}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            className="SPATableWrapper"
            stickyColumnCount={5} // Adjust sticky columns for non-government table
          />
        )}
        {project?.MasterEntityCategory?.entity_category !== 'Government' && group === 'B' && (
          <TanStackTable
            rows={sortArrayByKeyWithTypeConversion(spaData, 'milestone_sorting_order', true)}
            columns={nonGovernmentColumnsB}
            onDragEnd={handleDragAndDrop}
            gridFilters={gridFilters}
            setGridFilters={setGridFilters}
            className="SPATableWrapper"
            stickyColumnCount={5} // Adjust sticky columns for non-government table
          />
        )}
      </div>
      <PulseModel
        closable={false}
        style={{ width: 'fitContent' }}
        open={isValidationModel}
        onClose={() => setIsValidationModel(false)}
        content={<ValidationModel messages={validationMessage} onClose={() => setIsValidationModel(false)} />}
      />
    </>
  )
}

export default SpaTable
