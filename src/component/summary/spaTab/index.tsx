import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import EditNoteIcon from '@mui/icons-material/EditNote'
import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import AddSpaDrawer from './addSpaDrawer'
import { IAddCommercialButtonProps } from './interface'
import styles from './SpaTab.module.scss'
import SpaTable from './spaTable'
import { getSpaData } from './utils'
import ConfirmDeleteModal from '../../confirmDeleteModal'
import Button from '../../shared/button'
import PulseButton from '../../shared/button/pulseButton'
import Drawer from '../../shared/drawer'
import Loader from '../../shared/loader'
import TypographyField from '../../shared/typography'
import AddRoundedIcon from '../../svgImages/addRounded'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { ISpa } from '@/src/redux/spaMileston/interface'
import useSpa from '@/src/redux/spaMileston/useSpaMileston'
import { getMasterOneProject } from '@/src/services/projects'
import { dateNonRequiredFields } from '@/src/utils/arrayUtils'
import { isoToPayload } from '@/src/utils/dateUtils'
import { errorToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const AddCommercialButton: React.FC<IAddCommercialButtonProps> = ({
  setIsOpenDrawer,
  isEditForUser = true,
  className,
  startIcon,
  buttonColor,
}) => {
  const handleButtonClick = () => {
    if (!isEditForUser) {
      return toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    }
    setIsOpenDrawer(true)
  }

  return (
    <Button
      startIcon={startIcon}
      onClick={handleButtonClick}
      className={`${styles.addCommercialBtn} ${className}`}
      color={buttonColor ? buttonColor : 'primary'}
    >
      Add SPA & Milestones
    </Button>
  )
}

export interface CommercialsTabProps {
  selectedTab: string
  isOpenDrawer: boolean
  setIsOpenDrawer: any
  expanded: boolean
  governmentFilters: { colId: string; values: any }[]
  groupAFilters: { colId: string; values: any }[]
  groupBFilters: { colId: string; values: any }[]
  setFilters: (type: 'A' | 'B' | 'GOV', filters: { colId: string; values: any }[]) => void
}

const SpaTab: React.FC<CommercialsTabProps> = ({
  selectedTab,
  isOpenDrawer,
  setIsOpenDrawer,
  expanded,
  governmentFilters,
  groupAFilters,
  groupBFilters,
  setFilters,
}) => {
  const [isEditTable, setIsEditTable] = useState(false)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [spaData, setSpaData] = useState<ISpa[]>([])
  const [editSpa, setEditSpa] = useState<number | null>(null)
  const [buttonDisable, setButtonDisable] = useState<boolean>(false)
  const { addSpaApi, deleteSpaApi, getSpaApi, getSpaStatus, spas, updateSpaApi } = useSpa()
  const { currentUser } = useAuthorization()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const router = useRouter()
  const dataRef = useRef<any>([])
  const [group, setGroup] = useState<string>('A')
  const [loading, setLoading] = useState<boolean>(true)

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
  })

  useEffect(() => {
    if (dataRef) {
      dataRef.current = spaData || []
    }
  }, [spaData])

  useEffect(() => {
    const data = getSpaData(spas)
    setSpaData(data as ISpa[])
  }, [selectedTab, spas])

  // Get current filters and setter based on project type and group
  const getCurrentFiltersConfig = useCallback(() => {
    if (project?.MasterEntityCategory?.entity_category === 'Government') {
      return {
        currentFilters: governmentFilters,
        setCurrentFilters: (filters: { colId: string; values: any }[]) => setFilters('GOV', filters),
      }
    }

    return group === 'A'
      ? {
          currentFilters: groupAFilters,
          setCurrentFilters: (filters: { colId: string; values: any }[]) => setFilters('A', filters),
        }
      : {
          currentFilters: groupBFilters,
          setCurrentFilters: (filters: { colId: string; values: any }[]) => setFilters('B', filters),
        }
  }, [
    project?.MasterEntityCategory?.entity_category,
    group,
    setFilters,
    governmentFilters,
    groupAFilters,
    groupBFilters,
  ])

  const handleAddCommercial = async (values: ISpa) => {
    const createPayload = (basePayload: Record<string, any>, extraFields: Record<string, any> = {}) => ({
      ...basePayload,
      ...extraFields,
    })

    const createFormData = (payload: Record<string, any>) => {
      const formData = new FormData()
      Object.entries(payload).forEach(([key, value]) => {
        if (value != null) formData.append(key, value)
      })
      return formData
    }

    const basePayload = {
      period: values.period ? values.period : null,
      project_name: values.project_name ? values.project_name : null,
      lookup_project_to_phase_id: values.lookup_project_to_phase_id ? values.lookup_project_to_phase_id : null,
      milestone_description: values.milestone_description ? values.milestone_description : null,
      master_milestone_number_id: values.master_milestone_number_id ? Number(values.master_milestone_number_id) : null,
      milestone_status: values.milestone_status ? values.milestone_status : null,
      milestone_sorting_order: spaData.length ? spaData.length + 1 : 1,
      justification: values.justification ? values.justification : null,
    }

    const governmentPayload = createPayload(basePayload, {
      duration_from_start: values.duration_from_start ? Number(values.duration_from_start) : null,
      actual_forecast_milestone_completion: values.actual_forecast_milestone_completion
        ? isoToPayload(values.actual_forecast_milestone_completion)
        : null,
      forecasted_end_date: values.forecasted_end_date ? isoToPayload(values.forecasted_end_date) : null,
      delay_damage_per_diem: values.delay_damage_per_diem ? values.delay_damage_per_diem : 0,
      // milestone_status: values.milestone_status ? values.milestone_status : null,
      conditions_precedence: values.conditions_precedence ? values.conditions_precedence : null,
      remarks: values.remarks ? values.remarks : null,
    })

    const nonGovernmentPayload = createPayload(basePayload, {
      milestone_type: values.milestone_type ? values.milestone_type : null,
      milestone_date: values.milestone_date ? isoToPayload(values.milestone_date) : null,
      actual_forecast_milestone_completion: values.actual_forecast_milestone_completion
        ? isoToPayload(values.actual_forecast_milestone_completion)
        : null,
      actual_forecast_milestone_collection: values.actual_forecast_milestone_collection
        ? isoToPayload(values.actual_forecast_milestone_collection)
        : null,
      revised_spa_communication: values.revised_spa_communication
        ? isoToPayload(values.revised_spa_communication)
        : null,
      baseline_end_date: values.baseline_end_date ? isoToPayload(values.baseline_end_date) : null,
      forecasted_end_date: values.forecasted_end_date ? isoToPayload(values.forecasted_end_date) : null,
      eot: values.eot ? isoToPayload(values.eot) : null,
      letter_reference: values?.letter_reference ? values?.letter_reference : null,
      latest_communication_date: values.latest_communication_date
        ? isoToPayload(values.latest_communication_date)
        : null,
    })

    const formData =
      project?.MasterEntityCategory?.entity_category === 'Government' ? governmentPayload : nonGovernmentPayload
    // project?.MasterEntityCategory?.entity_category === 'Government'
    //   ? createFormData(governmentPayload)
    //   : createFormData(nonGovernmentPayload)

    const response: Record<string, any> = await addSpaApi(formData)

    if (response.payload.success === true)
      getSpaApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    else {
      toast('Error')
    }
    setButtonDisable(false)
    setIsOpenDrawer(false)
  }

  const handleUpdateCommercial = async (values: ISpa) => {
    const excludeNullCheckKeys = ['phase']
    const createFormData = (payload: Record<string, any>) => {
      const formData = new FormData()
      Object.entries(payload).forEach(([key, value]) => {
        if (excludeNullCheckKeys.includes(key)) {
          formData.append(key, value)
        } else if (value != null || dateNonRequiredFields(key, key)) {
          formData.append(key, value)
        }
      })
      return formData
    }

    const createPayload = (basePayload: Record<string, any>, extraFields: Record<string, any> = {}) => ({
      ...basePayload,
      ...extraFields,
    })

    const basePayload = {
      period: values.period ? values.period : null,
      project_name: values.project_name ? values.project_name : router.query.slug ? router.query.slug : null,
      lookup_project_to_phase_id: values.lookup_project_to_phase_id ? values.lookup_project_to_phase_id : null,
      milestone_description: values.milestone_description ? values.milestone_description : null,
      master_milestone_number_id: values.master_milestone_number_id ? values.master_milestone_number_id : null,
      milestone_status: values.milestone_status ? values.milestone_status : null,
      justification: values.justification ? values.justification : null,
    }

    const governmentPayload = createPayload(basePayload, {
      duration_from_start: values.duration_from_start ? Number(values.duration_from_start) : null,
      actual_forecast_milestone_completion: values.actual_forecast_milestone_completion
        ? isoToPayload(values.actual_forecast_milestone_completion)
        : null,
      forecasted_end_date: values.forecasted_end_date ? isoToPayload(values.forecasted_end_date) : null,
      delay_damage_per_diem: values.delay_damage_per_diem ? values.delay_damage_per_diem : 0,
      milestone_status: values.milestone_status ? values.milestone_status : null,
      conditions_precedence: values.conditions_precedence ? values.conditions_precedence : null,
      remarks: values.remarks ? values.remarks : null,
    })

    const nonGovernmentPayload = createPayload(basePayload, {
      milestone_type: values.milestone_type ? values.milestone_type : null,
      actual_forecast_milestone_completion: values.actual_forecast_milestone_completion
        ? isoToPayload(values.actual_forecast_milestone_completion)
        : null,
      milestone_date: values.milestone_date ? isoToPayload(values.milestone_date) : null,
      actual_forecast_milestone_collection: values.actual_forecast_milestone_collection
        ? isoToPayload(values.actual_forecast_milestone_collection)
        : null,
      revised_spa_communication: values.revised_spa_communication
        ? isoToPayload(values.revised_spa_communication)
        : null,
      baseline_end_date: values.baseline_end_date ? isoToPayload(values.baseline_end_date) : null,
      forecasted_end_date: values.forecasted_end_date ? isoToPayload(values.forecasted_end_date) : null,
      eot: values.eot ? isoToPayload(values.eot) : null,
      letter_reference: values?.letter_reference ? values?.letter_reference : null,
      latest_communication_date: values.latest_communication_date
        ? isoToPayload(values.latest_communication_date)
        : null,
    })

    const formData =
      project?.MasterEntityCategory?.entity_category === 'Government' ? governmentPayload : nonGovernmentPayload

    const response: Record<string, any> = await updateSpaApi({
      id: values.id as number,
      data: formData,
    })

    if (response.payload.success === true) {
      const res: Record<string, any> = await getSpaApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })
      if (res.payload.success === true) {
        setSpaData(getSpaData(res.payload.data))
      } else {
        errorToast(response?.payload?.response?.data?.message || 'Failed to update')
      }
      setButtonDisable(false)
      setIsOpenDrawer(false)
      setEditSpa(null)
    } else {
      errorToast(response?.payload?.response?.data?.message || 'Failed to update')
    }
  }

  const handleDeleteAction = async (id: number) => {
    const response: any = await deleteSpaApi(id as number)
    if (response?.payload?.success === true) {
      getSpaApi({ period: currentPeriod, project_name: router?.query?.slug as string })
      setDeleteModel(null)
    } else {
      errorToast(response?.payload?.response?.data?.message || 'Failed to Delete')
    }
  }

  const handleCloseDrawer = () => {
    setIsOpenDrawer(false)
    setEditSpa(null)
  }

  // Memoized Edit Permissions
  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )
  const handleFetchData = async () => {
    setLoading(true)
    const res: Record<string, any> = await getSpaApi({
      period: currentPeriod,
      project_name: router?.query?.slug as string,
    })
    if (!res?.payload?.success) {
      toast(res?.payload?.message || 'SPA & Milestones Not Fetched Successfully ')
      setLoading(false)
      return
    }
    setLoading(false)
  }

  useEffect(() => {
    handleFetchData()
  }, [])

  return (
    <div className={styles.mainContainer}>
      {loading ? (
        <Loader />
      ) : (
        <>
          {spaData?.length ? (
            <div className={styles.tableWrapper}>
              {/* <div className={styles.addButtonWrapper}>
                <AddCommercialButton
                  setIsOpenDrawer={setIsOpenDrawer}
                  className={styles.addButtonPosition}
                  startIcon={<PlusIcon />}
                  buttonColor="secondary"
                />
              </div> */}
              <div className={styles.button}>
                <PulseButton
                  onClick={() => {
                    if (!isEditForUser) {
                      toast(`The current reporting period is locked`, {
                        icon: <WarningAmberOutlined />,
                      })
                    } else {
                      setEditSpa(null)
                      setIsOpenDrawer(true)
                    }
                  }}
                  label="Add SPA & Milestones"
                  icon={<AddOutlinedIcon fontSize="large" />}
                  // disabled={loader}
                />
                <div className={styles.rightCorner}>
                  {project?.MasterEntityCategory?.entity_category !== 'Government' && (
                    <div className={`${styles.groupToggle} ${group === 'B' ? styles.activeB : ''}`}>
                      <div
                        className={styles.slider}
                        style={{ transform: group === 'B' ? 'translateX(100%)' : 'translateX(0)' }}
                      ></div>
                      <span
                        className={`${styles.toggleBtn} ${group === 'A' ? styles.active : ''}`}
                        onClick={() => setGroup('A')}
                      >
                        Group A
                      </span>
                      <span
                        className={`${styles.toggleBtn} ${group === 'B' ? styles.active : ''}`}
                        onClick={() => setGroup('B')}
                      >
                        Group B
                      </span>
                    </div>
                  )}
                  {currentUser.role?.view_permissions.includes('SPA & Milestones Edit Permission') && (
                    <Button
                      startIcon={<EditNoteIcon />}
                      variant={isEditTable ? 'outlined' : 'contained'}
                      disabled={!isEditForUser}
                      onClick={() => {
                        if (!isEditForUser) {
                          toast(`The current reporting period is locked`, {
                            icon: <WarningAmberOutlined />,
                          })
                        } else {
                          setIsEditTable(!isEditTable)
                        }
                      }}
                    >
                      {`Edit Table`}
                    </Button>
                  )}
                </div>
              </div>

              <SpaTable
                isEditTable={isEditTable}
                setIsOpenDrawer={setIsOpenDrawer}
                setEditSpa={setEditSpa}
                setDeleteModel={setDeleteModel}
                setSpaData={setSpaData}
                spaData={spaData}
                group={group}
                expanded={expanded}
                gridFilters={getCurrentFiltersConfig().currentFilters}
                setGridFilters={getCurrentFiltersConfig().setCurrentFilters}
              />
              {/* <SpaTable
            data={sortArrayByKeyWithTypeConversion(spaData, "SPA_sorting_order")}
            columns={columns}
          /> */}
            </div>
          ) : (
            <div className={styles.container}>
              <div className={styles.contentWrapper}>
                <Image src="/svg/contracts.svg" alt="contracts" width={200} height={200} />
                <TypographyField
                  variant={'bodyBold'}
                  className={styles.noRecordsText}
                  text="Looks like there are no SPA & Milestones records added!"
                />
                <AddCommercialButton
                  setIsOpenDrawer={setIsOpenDrawer}
                  isEditForUser={isEditForUser}
                  // setSelectedCommercials={setSelectedCommercials}
                  startIcon={<AddRoundedIcon />}
                />
              </div>
            </div>
          )}
        </>
      )}

      <Drawer anchor="right" open={isOpenDrawer} onClose={handleCloseDrawer}>
        <AddSpaDrawer
          onClose={handleCloseDrawer}
          onAddCommercial={handleAddCommercial}
          editSpa={editSpa}
          onUpdateCommercial={handleUpdateCommercial}
          selectedGroup={group}
          buttonDisable={buttonDisable}
          setButtonDisable={setButtonDisable}
        />
      </Drawer>
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDeleteAction(deleteModel as number)}
      />
    </div>
  )
}

export default SpaTab
