@import '/styles/color.scss';

.tableWrapper {
  padding: 16px 24px 16px 0px;
  position: relative;
  height: calc(100% - 41px);
}

.addButtonWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 0px;
}

.addButtonPosition {
  position: absolute;
  top: -44px;
  right: 0px;
}

.addButton {
  background-color: #495e69 !important;
  border-radius: 6px 18px 18px 6px !important;
  gap: 6px;
  padding: 0 0 0 9px !important;
  color: #fff !important;
}

.actionsTableBtn {
  // background-color: #495e69 !important;
  color: #fff !important;
}

.container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 92px;
  .contentWrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    .noRecordsText {
      font-weight: 600;
      font-size: 24px;
      line-height: 32px;
      color: $DARK_100;
      width: 358px;
    }
    .addCommercialBtn {
      margin-top: 16px;
    }
  }
}

.CorrectIcon {
  fill: green;
  path {
    fill: green;
  }
}
.loaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.mainContainer {
  height: 100%;
  width: 100%;
  height: calc(100% - 41px);
}
.textField {
  > div {
  }
  > div {
    width: 40px;
    > div > input {
      text-align: center;
      background: white !important;
      padding: 0px !important;
      color: $DARK;
      font-size: 12px !important;
    }
  }
}
.cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.inputField {
  > div {
  }
  > div {
    width: 100%;
    > div > input {
      text-align: center;
      background: white !important;
      padding: 0px !important;
      color: $DARK;
      font-size: 12px !important;
    }
  }
}
.button {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.editModeBtn {
  background-color: #808080 !important;
  color: white;
}
.groupToggle {
  z-index: 0;
  display: flex;
  background: $PULSE_GRAY;
  border-radius: 5px;
  padding: 3px;
  width: 120px;
  position: relative;
  transition: all 0.3s ease-in-out;
}

.toggleBtn {
  flex: 1;
  padding: 6px 0;
  text-align: center;
  border: none;
  background: transparent;
  cursor: pointer;
  font-family: Poppins;
  font-size: 12px;
  font-weight: 500;
  color: $DARK;
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

.toggleBtn.active {
  color: white;
}

/* Background Slider */
.slider {
  position: absolute;
  width: 50%;
  height: 100%;
  background: $PULSE_TURQUOISE;
  border-radius: 5px;
  transition: transform 0.3s ease;
  top: 0;
  left: 0;
}
.rightCorner {
  align-items: center;
  justify-content: center;
  display: flex;
  gap: 5px;
}
