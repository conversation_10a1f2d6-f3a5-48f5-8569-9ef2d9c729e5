import { ISpa } from '@/src/redux/spaMileston/interface'

export interface ICommercial {
  id?: number
  period?: string
  project_name: string
  description: string
  budget: number
  committed_cost: number
  vowd: number
  paid_amount: number
  forecast_at_completion?: number
  last_updated: string
}

export interface IAddCommercialButtonProps {
  setIsOpenDrawer: React.Dispatch<React.SetStateAction<boolean>>
  isEditForUser: boolean
  // setSelectedCommercials: React.Dispatch<React.SetStateAction<ICommercial | null>>
  className?: string
  startIcon?: React.ReactNode
  buttonColor?: 'primary' | 'inherit' | 'secondary' | 'success' | 'error' | 'info' | 'warning'
}

export interface ICommercialColumn {
  key: string
  label: string
  renderCell?: (value: any, row: Record<string, any>, rowIndex: number) => JSX.Element | undefined
}

export interface ICommercialsTableProps {
  columns: ICommercialColumn[]
  data: Record<string, any>[]
}

export interface TableHeaderProps {
  columns: ICommercialColumn[]
}

export interface TableBodyProps {
  data: Record<string, any>[]
  columns: ICommercialColumn[]
}

export interface TableRowProps {
  row: Record<string, any>
  rowIndex: number
  columns: ICommercialColumn[]
}

export interface TableCellProps {
  value: any
  column: ICommercialColumn
  rowIndex: number
  row: Record<string, any>
}

export interface ISpaTableProps {
  isEditTable: boolean
  setIsOpenDrawer: React.Dispatch<React.SetStateAction<boolean>>
  setEditSpa: React.Dispatch<React.SetStateAction<number | null>>
  setDeleteModel: React.Dispatch<React.SetStateAction<number | null>>
  setSpaData: React.Dispatch<React.SetStateAction<ISpa[]>>
  spaData: ISpa[]
  group: string
  expanded: boolean
  gridFilters: { colId: string; values: any }[]
  setGridFilters: (args: { colId: string; values: any }[]) => void
}

export interface ILetterReferenceCell {
  value: string
  attachments: { name: string; url?: string }[]
  onUpload: any
  onDownload: () => void
  isEditForUser?: boolean
}
