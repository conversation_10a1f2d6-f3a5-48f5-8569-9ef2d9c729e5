import React from 'react'
import { Tooltip } from '@mui/material'
import styles from './BannerDateData.module.scss'
import TypographyField from '@/src/component/shared/typography'
import CalendarIcon from '@/src/component/svgImages/calenderIcon'
import { formatDateString } from '@/src/utils/dateUtils'

const BannerDateData = ({ cutoff_date }: any) => {
  return (
    <>
      {cutoff_date && (
        <Tooltip
          title="Data Date"
          componentsProps={{
            tooltip: {
              sx: { fontFamily: 'poppins' },
            },
          }}
          arrow
        >
          <div className={styles.iconWrapper}>
            <CalendarIcon color="#ffffff" />
            <div className={styles.periodContainer}>
              <TypographyField
                variant="thin"
                style={{ color: '#b0b0b0', fontSize: '10px', lineHeight: '10px' }}
                text={'Data Date'}
              />
              <TypographyField
                variant="caption"
                style={{ color: '#ffffff', fontSize: '14px', lineHeight: '10px' }}
                text={cutoff_date ? formatDateString(cutoff_date) : ''}
              />
            </div>
          </div>
        </Tooltip>
      )}
    </>
  )
}

export default BannerDateData
