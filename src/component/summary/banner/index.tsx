import React from 'react'
import { Box } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import styles from './Banner.module.scss'
import BannerAvatars from './bannerAvatars'
import BannerPeriod from './bannerPeriod'
import BannerProjectInfo from './bannerProjectInfo'
import BannerTabs from './bannerTabs'
import PaginationButtons from './paginationButtons'
import RotateIcon from '../../svgImages/rotateIcon'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { getMasterOneProject } from '@/src/services/projects'

const Banner = ({
  setExpanded,
  expanded,
}: {
  setExpanded: React.Dispatch<React.SetStateAction<boolean>>
  expanded: boolean
}) => {
  const router = useRouter()
  const { currentPeriod } = useMasterPeriod()
  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data, // This extracts 'data' directly
    enabled: false,
  })

  const handleToggle = () => setExpanded((prev: boolean) => !prev)

  return (
    <>
      <Box
        className={`${styles.header} ${styles.darkTheme} ${styles.animateHeader}`}
        onClick={handleToggle}
        id="project-banner"
        // sx={{
        //   zIndex: (theme) => theme.zIndex.tooltip + 1,
        // }}
      >
        <div className={styles.topBar}>
          <div className={styles.topContainer}>
            <div className={styles.titleContainer}>
              <span className={styles.projectName}>{project?.project_name || ''}</span>
              <div className={styles.PeriodPaginationContainer}>
                <BannerPeriod />
                <PaginationButtons />
              </div>
            </div>
            <RotateIcon className={`${styles.rotateIcon} ${expanded ? styles.rotateDown : styles.rotateUp}`} />
          </div>
          <div className={styles.accordionSummary}>
            {expanded && (
              <>
                <BannerProjectInfo cutoff_date={project?.cutoff_date} />
              </>
            )}
            {expanded && <BannerAvatars expanded={expanded} />}
          </div>
        </div>
        <BannerTabs />
      </Box>
    </>
  )
}

export default Banner
