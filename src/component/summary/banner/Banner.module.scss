@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 13px 30px 0px 24px;
}

.darkTheme {
  background: url(../../../../public/svg/Banner.svg) no-repeat center center/cover;
  background-position: center center;
  background-size: 100% 100%;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 1.25rem;
  position: sticky;
  top: 0;
  margin-right: 1.25rem;
  z-index: 2;

  @include respond-to('mobile') {
    margin: 0 1.25rem;
    border-radius: 1.25rem;
  }
  @include respond-to('tablet') {
    margin: 0 1.25rem;
    border-radius: 1.25rem;
  }
  @include respond-to('laptop') {
    margin: 0 1.25rem 0 0;
    border-radius: 0.9375rem;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-1.25rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animateHeader {
  animation: slideDown 0.5s ease-out;
}

.topBar {
  width: 100%;
  align-items: center;

  .projectName {
    width: 100%;
    color: $WHITE;
    font-size: 1.25rem;
    overflow: hidden;
    white-space: pre-wrap;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .topContainer {
    display: flex;
    justify-content: space-between;
    .titleContainer {
      margin: 0 0 1rem 0;
      display: flex;
      align-items: flex-start;
      gap: 1.25rem;
      cursor: pointer;

      @include respond-to('mobile') {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
      }
      @include respond-to('laptop') {
        display: grid;
        grid-template-columns: 4.5fr 1fr;
        width: 100%;
        gap: 0.625rem;
      }
    }
  }

  .rotateIcon {
    height: 28px;
    width: 40px;
    margin-left: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .rotateUp {
    transform: rotate(90deg);
  }
  .rotateDown {
    transform: rotate(270deg);
  }
}

.avatarDetailContainer {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.PeriodPaginationContainer {
  display: flex;
  gap: 1.25rem;
  cursor: pointer;

  @include respond-to('mobile') {
    width: 100%;
    display: flex;
    flex-direction: row;
  }
  @include respond-to('laptop') {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
