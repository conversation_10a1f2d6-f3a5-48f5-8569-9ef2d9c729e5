import React, { useCallback } from 'react'
import ArrowBackIosSharpIcon from '@mui/icons-material/ArrowBackIosSharp'
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp'
import { Tooltip } from '@mui/material'
import { useRouter } from 'next/router'
import styles from './PaginationButtons.module.scss' // Create a new styles module for this component
import Button from '../../../shared/button'
import { Routes } from '@/src/constant/enum'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'

const PaginationButtons = () => {
  const router = useRouter()
  const { localFilteredData } = useProjectSummary()

  const isPreviousDisabled = router.query.slug === localFilteredData[0]?.project_name || router.query?.search === 'true'

  const isNextDisabled =
    localFilteredData.findIndex((project: any) => project?.project_name === router.query.slug) ===
      localFilteredData.length - 1 || router.query?.search === 'true'

  const handlePreviousPage = useCallback(
    (e: any) => {
      e.stopPropagation()
      const currentProjectIndex = localFilteredData.findIndex(
        (project: any) => project.project_name === router.query.slug,
      )
      if (currentProjectIndex > 0) {
        const previousProjectSlug = localFilteredData[currentProjectIndex - 1].project_name || ''
        router.push(`${Routes.SUMMARY}/${encodeURIComponent(previousProjectSlug)}`)
      }
    },
    [localFilteredData, router],
  )

  const handleNextProject = useCallback(
    (e: any) => {
      e.stopPropagation()
      const currentProjectIndex = localFilteredData.findIndex(
        (project: any) => project.project_name === router.query.slug,
      )
      if (currentProjectIndex < localFilteredData.length - 1) {
        const nextProjectSlug = localFilteredData[currentProjectIndex + 1].project_name || ''
        router.push(`${Routes.SUMMARY}/${encodeURIComponent(nextProjectSlug)}`)
      }
    },
    [localFilteredData, router],
  )
  return (
    <div className={styles.buttonPagination}>
      <div className={styles.buttonOfPagination} onClick={(e) => e.stopPropagation()}>
        <Tooltip
          title={
            isPreviousDisabled
              ? 'Navigation will be available after selecting a project from the list'
              : 'Previous Project'
          }
          PopperProps={{
            sx: { '& .MuiTooltip-tooltip': { maxWidth: 160 } },
          }}
          arrow
        >
          <div>
            <Button
              onClick={handlePreviousPage}
              disabled={isPreviousDisabled}
              className={`${styles.preNextBtn} ${isPreviousDisabled ? styles.disabled : styles.enable}`}
            >
              <ArrowBackIosSharpIcon fontSize="small" sx={{ color: '#FFF' }} />
            </Button>
          </div>
        </Tooltip>
        <Tooltip
          title={
            isNextDisabled ? 'Navigation will be available after selecting a project from the list' : 'Next Project'
          }
          PopperProps={{
            sx: { '& .MuiTooltip-tooltip': { maxWidth: 160 } },
          }}
          arrow
        >
          <div>
            <Button
              color="secondary"
              onClick={handleNextProject}
              disabled={isNextDisabled}
              className={`${styles.preNextBtn} ${isNextDisabled ? styles.disabled : styles.enable}`}
            >
              <ArrowForwardIosSharpIcon fontSize="small" sx={{ color: '#FFF' }} />
            </Button>
          </div>
        </Tooltip>
      </div>
    </div>
  )
}

export default PaginationButtons
