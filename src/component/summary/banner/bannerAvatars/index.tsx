import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import AvatarCard, { IAvatar } from './avatarCard'
import styles from './BannerAvatars.module.scss'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { getMasterOneProject } from '@/src/services/projects'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'

const BannerAvatars = ({ expanded }: { expanded: boolean }) => {
  const router = useRouter()
  const { currentPeriod } = useMasterPeriod()
  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  const sortingMangerOrder = (array: IAvatar[], key: string) => {
    const alphabeticOrder = Array.isArray(array) ? sortArrayByKeyWithTypeConversion([...array], key) : []
    const indexSorting = Array.isArray(alphabeticOrder)
      ? sortArrayByKeyWithTypeConversion([...alphabeticOrder], 'index', true)
      : []
    return indexSorting
  }

  const avatarCards = [
    {
      role: project?.MasterPortfolioManager?.portfolio_manager ?? '',
      occupation: 'Portfolio Manager',
      avatars: project?.MasterPortfolioManager ? [project.MasterPortfolioManager] : [],
    },
    {
      role:
        project?.DesignExecutiveDirectors?.map((item: any) =>
          item?.design_executive_director.replace(/^;/, '').split(';').join(', '),
        ) ?? '',
      occupation: 'Design Executive Director',
      avatars: Array.isArray(project?.DesignExecutiveDirectors)
        ? sortArrayByKeyWithTypeConversion([...project.DesignExecutiveDirectors], 'design_executive_director')
        : [],
    },
    {
      role: project?.DesignProjectManagers
        ? project.DesignProjectManagers?.map((item: any) =>
            item?.design_manager.replace(/^;/, '').split(';').join(', '),
          )
        : '',
      occupation: 'Design Manager',
      avatars: Array.isArray(project?.DesignProjectManagers)
        ? sortingMangerOrder([...project.DesignProjectManagers], 'design_manager')
        : [],
    },
    {
      role: project?.ProcurementManagers
        ? project.ProcurementManagers.map((item: any) =>
            item?.procurement_manager.replace(/^;/, '').split(';').join(', '),
          )
        : '',
      occupation: 'Procurement Manager',
      avatars: Array.isArray(project?.ProcurementManagers)
        ? sortingMangerOrder([...project.ProcurementManagers], 'procurement_manager')
        : [],
    },
    {
      role: project?.MasterControlManager?.control_manager ?? '',
      occupation: 'Controls Manager',
      avatars: project?.MasterControlManager ? [project.MasterControlManager] : [],
      // avatars: Array.isArray(project?.controls_manager_avatar)
      //   ? sortArrayByKeyWithTypeConversion([...project.controls_manager_avatar], 'id', true)
      //   : [],
    },
    {
      role: project?.ExecutiveDirectors
        ? project.ExecutiveDirectors.map((item: any) =>
            item?.executive_director.replace(/^;/, '').split(';').join(', '),
          )
        : '',
      occupation: 'Delivery Executive Director',
      avatars: Array.isArray(project?.ExecutiveDirectors)
        ? sortArrayByKeyWithTypeConversion([...project.ExecutiveDirectors], 'person_name')
        : [],
    },
    {
      role: project?.MasterDirector?.director ?? '',
      occupation: 'Delivery Director',
      avatars: project?.MasterDirector ? [project.MasterDirector] : [],
    },
    {
      role: project?.DeliveryProjectManagers
        ? project.DeliveryProjectManagers.map((item: any) =>
            item?.delivery_project_manager.replace(/^;/, '').split(';').join(', '),
          )
        : '',
      occupation: 'Delivery PM',
      avatars: Array.isArray(project?.DeliveryProjectManagers)
        ? sortingMangerOrder([...project?.DeliveryProjectManagers], 'delivery_project_manager')
        : [],
    },
  ]

  const managersToShow =
    project?.MasterProjectClassification?.project_classification?.trim() === 'Design'
      ? avatarCards?.slice(0, 3)
      : avatarCards

  return (
    <>
      <div className={`${!expanded ? styles.isAccordion : styles.detail}`}>
        <div className={styles.avatarDetailContainer}>
          {managersToShow?.map((card, index) => (
            <AvatarCard
              key={index}
              role={card.role as string}
              occupation={card.occupation}
              avatars={card?.avatars as IAvatar[]}
            />
          ))}
        </div>
      </div>
    </>
  )
}

export default BannerAvatars
