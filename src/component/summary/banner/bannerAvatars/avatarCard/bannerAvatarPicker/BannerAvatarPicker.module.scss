@import '/styles/color.scss';

.avatarPicker {
  display: flex;
}

.avatarStack {
  align-items: center;
  display: flex;
  justify-content: center;
  cursor: pointer;
  backdrop-filter: contrast(0.7);
  border-radius: 10px;
  padding: 4px 5px 0 5px;
  gap: 5px;
}

.avatarItem {
  cursor: pointer;
  display: inline-flex;
  transition: transform 0.2s;
  // &:hover {
  //   transform: scale(1.2);
  //   transition: transform 0.2s;
  // }
}

.avatarDetails {
  width: 150px;
  min-width: 150px;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  margin-left: 3px;
  margin-right: 3px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  .avatarRole {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block; // or inline-block, but block is safer for width
    width: 100%;

    font-size: 12px;
    font-weight: 600;
    line-height: 18px;
    color: $BLACK;
    opacity: 100%;
    // width: fit-content;
    // white-space: break-spaces;
  }

  .avatarOccupation {
    font-size: 10px;
    font-weight: 400;
    line-height: 15px;
    color: $NEUTRAL_400;
    width: fit-content;
  }
}
