import React, { useState } from 'react'
import { Box } from '@mui/material'
import Image from 'next/image'
import styles from './BannerAvatarPicker.module.scss'
import { fieldMapping } from '../../helper'
import { IBannerAvatarPickerProps } from '../../interface'
import AnimatedTooltip from '@/src/component/shared/animatedTooltip'
import TypographyField from '@/src/component/shared/typography'
import ManagerIcon from '@/src/component/svgImages/managerIcon'

const BannerAvatarPicker = ({ avatars, occupation }: IBannerAvatarPickerProps) => {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(0)
  const getRightMargin = (index: number, avatar: string | null) => {
    return selectedIndex !== index && index !== avatars?.length - 1 ? (avatar ? '0px' : '4px') : ''
  }

  const key = fieldMapping[occupation as keyof typeof fieldMapping]

  return (
    <div className={styles.avatarPicker}>
      <div className={styles.avatarStack}>
        {avatars?.map((avatar, index) => (
          <React.Fragment key={avatar?.name || `avatar-${index}`}>
            <Box
              className={styles.avatarItem}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                setSelectedIndex(index)
              }}
              sx={{
                zIndex: index,
                borderRadius: '25px',
                height: '45px',
                margin: avatar?.avatar_url ? '' : '3px 0 -4px',
                // marginRight: getRightMargin(index, avatar?.avatar_url),
              }}
            >
              <AnimatedTooltip text={avatar[key as keyof typeof avatar]}>
                {avatar?.avatar_url ? (
                  <Image
                    src={avatar.avatar_url}
                    height={40}
                    width={40}
                    style={{ borderRadius: '25px', marginTop: '-2px', marginLeft: '-3px', marginRight: '-3px' }}
                    alt="Avatar"
                  />
                ) : (
                  <ManagerIcon color="#ffffff" className={styles.avatarItem} />
                )}
              </AnimatedTooltip>
            </Box>
            {selectedIndex === index && (
              <div
                className={styles.avatarDetails}
                style={{
                  maxWidth: `${avatars?.length === 1 ? 'unset' : ''}`,
                  width: `${avatars?.length === 1 ? 'unset' : ''}`,
                }}
              >
                <TypographyField
                  style={{ color: '#fff' }}
                  variant="captionSemiBold"
                  className={styles.avatarRole}
                  text={`${avatar[key as keyof typeof avatar] || avatar?.name}`}
                />
                <TypographyField
                  style={{ color: '#fff' }}
                  variant="thin"
                  className={styles.avatarOccupation}
                  text={`${occupation}`}
                />
              </div>
            )}
          </React.Fragment>
        ))}
        {avatars?.length === 0 && (
          <>
            <div
              className={styles.avatarItem}
              style={{ margin: '2px 0 5px' }}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
              }}
            >
              <ManagerIcon color="#ffffff" className={styles.avatarItem} />
            </div>
            <div className={styles.avatarDetails}>
              <TypographyField
                style={{ color: '#fff' }}
                variant="thin"
                className={styles.avatarOccupation}
                text={`${occupation}`}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default BannerAvatarPicker
