@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.tabContainer {
  width: 100%;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 0rem;
    height: 0rem;
  }
  &::-webkit-scrollbar-track {
    border-radius: 0.3125rem;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 0.3125rem;
  }
}

.content {
  .tabLayoutContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}

.tabButtons {
  color: $WHITE;
  display: flex;
  gap: 0.5rem;
  padding-right: 1rem;
}
