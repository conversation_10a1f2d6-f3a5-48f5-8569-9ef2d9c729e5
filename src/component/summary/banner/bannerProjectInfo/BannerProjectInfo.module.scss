@import '/styles/color.scss';

.header {
  padding: 13px 0px 14px 24px;
  background: $WHITE;
  border-bottom: 1px solid $LIGHT_200;
  .headerTitle {
    font-size: 18px;
    font-weight: 600;
    line-height: 23px;
    color: $DARK;
  }
}

.content {
  padding: 16px 24px;
}

.infoContainer {
  // background-color: #444444 !important;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;
  .detailsContainer {
    width: fit-content;
    display: flex;
    justify-content: space-between;
    backdrop-filter: contrast(0.7);
    padding: 8px 12px;
    border-radius: 12px !important;
    gap: 45px;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 0px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
    }
    &::-webkit-scrollbar-thumb {
      background: transparent;
      border-radius: 5px;
    }
    .fieldDataWrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 7px;
      svg {
        height: 18px;
        width: 18px;
      }
      .fieldData {
        text-wrap: nowrap;
        display: flex;
        flex-direction: column;
        .fieldLabel {
          font-size: 10px;
          font-weight: 400;
          line-height: 15px;
          text-align: left;
          color: $DARK_200;
        }
      }
    }
  }
  .avatarDetailContainer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
    .avatarCard {
      text-wrap: nowrap;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8px;
      .avatarDetails {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        .avatarRole {
          font-size: 12px;
          font-weight: 600;
          line-height: 18px;
          color: $BLACK;
          opacity: 100%;
          width: fit-content;
        }
        .avatarOccupation {
          font-size: 10px;
          font-weight: 400;
          line-height: 15px;
          color: $NEUTRAL_400;
          width: fit-content;
        }
      }
    }
  }
}
.buttonOfPagination {
  display: flex;
  gap: 30px;
}

.buttonPagination {
  display: flex;
  gap: 30px;
  align-items: center;
  .preNextBtn {
    opacity: 100% !important;
    backdrop-filter: contrast(0.6) !important;
    filter: blur(0);
    background-color: transparent;
    border-color: transparent;
    border-radius: 50%;
    color: white;
    white-space: nowrap;
    min-width: unset;
    height: 30px;
    width: 30px;
  }
}

.iconWrapper {
  white-space: nowrap;
  height: fit-content;
  display: flex;
  align-items: center;
  gap: 7px;
}
.currentPeriod {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
