import React, { useEffect, useState } from 'react'
import AccountBalanceOutlinedIcon from '@mui/icons-material/AccountBalanceOutlined'
import { useQuery } from '@tanstack/react-query'
import { useRouter } from 'next/router'
import styles from './BannerProjectInfo.module.scss'
import { IFieldData } from './interface'
import TypographyField from '../../../shared/typography'
import ClipboardIcon from '../../../svgImages/clipboardIcon'
import LocationIcon from '../../../svgImages/locationIcon'
import PriceTagIcon from '../../../svgImages/priceTagIcon'
import TypologyIcon from '../../../svgImages/typologyIcon'
import BannerDateData from '../bannerDateData'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useCommercial from '@/src/redux/commercial/useCommercial'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { getMasterOneProject } from '@/src/services/projects'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'

const BannerProjectInfo = ({ cutoff_date }: any) => {
  const router = useRouter()
  const [commercial, setCommercial] = useState<any>()
  const { commercials, getCommercialApi } = useCommercial()
  const { currentPeriod } = useMasterPeriod()
  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  const fieldDataArray = [
    {
      field: 'Category',
      value: project?.MasterEntityCategory?.entity_category || null,
      icon: <AccountBalanceOutlinedIcon style={{ color: '#FFFFFF' }} />,
    },
    {
      field: 'Entity',
      value: project?.owning_entity || null,
      icon: <ClipboardIcon color="#ffffff" />,
    },
    {
      field: 'Type',
      value: project?.project_status || null,
      icon: <TypologyIcon color="#ffffff" />,
    },
    {
      field: 'Location',
      value: project?.location?.split(';')?.join(', ') || null,
      icon: <LocationIcon color="#ffffff" />,
    },
    {
      field: 'Budget',
      value: commercial?.budget ? formatNumberWithCommas(commercial.budget.toLocaleString()) : 'N/A',
      icon: <PriceTagIcon color="#ffffff" />,
    },
    // Uncomment and use this if needed
    // {
    //   field: 'DOF No.',
    //   value: project?.dof_number || '',
    //   icon: <DocumentIcon />,
    // },
  ]

  useEffect(() => {
    currentPeriod && getCommercialApi({ period: currentPeriod, project_name: router?.query?.slug as string })
  }, [currentPeriod])

  useEffect(() => {
    const currentCummercial: any = commercials.find((item) => {
      return item.project_name === router.query.slug
    })
    setCommercial(currentCummercial)
  }, [commercials])

  return (
    <div className={styles.infoContainer}>
      <div className={styles.detailsContainer}>
        {fieldDataArray.map(
          (item, index) =>
            item?.value && <FieldData key={index} field={item?.field} value={item?.value} icon={item?.icon} />,
        )}
      </div>
      <BannerDateData cutoff_date={cutoff_date} />
    </div>
  )
}

const FieldData = ({ field, value, icon }: IFieldData) => {
  return (
    <div className={styles.fieldDataWrapper}>
      {icon}
      <div className={styles.fieldData}>
        <TypographyField
          style={{ color: '#ffffff' }}
          // sx="captionSemiBold"
          text={value}
        />
        <TypographyField style={{ color: '#ffffff' }} className={styles.fieldLabel} text={field} />
      </div>
    </div>
  )
}

export default BannerProjectInfo
