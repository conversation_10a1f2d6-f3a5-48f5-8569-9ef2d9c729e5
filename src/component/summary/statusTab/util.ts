import { differenceInCalendarDays, parse } from 'date-fns'
import { IStatusForTable } from './interface'
import { PREDECESSOR_SUCCESSOR_SEPARATOR } from '@/src/constant/stageStatus'
import { isValidDDMMYYYYDate } from '@/src/utils/dateUtils'

/**
 * Determines if a forecast date cell should be highlighted based on comparison with a reference date.
 * A cell should be highlighted when the current forecast date is valid and either:
 * 1. The reference date is invalid/missing, or
 * 2. The reference date is different from the current forecast date
 *
 * @param currentForecastDate - The current forecast date to evaluate (usually in DD-MM-YYYY format)
 * @param referenceForecastDate - The reference date to compare against (usually last week's forecast date)
 * @returns boolean - true if the cell should be highlighted, false otherwise
 *
 * @example
 * ```typescript
 * shouldHighlightForecastDate('29-05-2025', '-')         // true (reference date invalid)
 * shouldHighlightForecastDate('29-05-2025', '30-05-2025') // true (dates are different)
 * shouldHighlightForecastDate('29-05-2025', '29-05-2025') // false (dates are same)
 * shouldHighlightForecastDate('-', '29-05-2025')         // false (current date invalid)
 * ```
 */
export const shouldHighlightForecastDate = (currentForecastDate: string, referenceForecastDate: string): boolean => {
  // Both dates must be valid and different to highlight
  const isCurrentDateValid = isValidDDMMYYYYDate(currentForecastDate)
  const isReferenceDateValid = isValidDDMMYYYYDate(referenceForecastDate)

  // Highlight only if both dates are valid and different
  return isCurrentDateValid && isReferenceDateValid && currentForecastDate !== referenceForecastDate
}

//* Helper to parse DD-MM-YYYY to Date
const parseDDMMYYYY = (str: string) => (isValidDDMMYYYYDate(str) ? parse(str, 'dd-MM-yyyy', new Date()) : null)

/**
 * Checks if there is any mismatch in the difference (in days) between forecastFinish and forecastCompletionLastWeek across all records.
 * Returns true if any difference is not the same, otherwise false.
 * @param data Array of objects with forecastFinish and forecastCompletionLastWeek fields (DD-MM-YYYY)
 */
export function hasForecastDateMismatch(data: IStatusForTable[]): boolean {
  // const filtered = data.filter((item) =>
  //   shouldHighlightForecastDate(item.forecastFinish ?? '', item.forecastCompletionLastWeek ?? ''),
  // )
  const filtered = [...data]

  //* Calculate all valid differences in days
  const differences = filtered
    .map((item) => {
      const forecastFinish = parseDDMMYYYY(item.forecastFinish ?? '')
      const forecastCompletionLastWeek = parseDDMMYYYY(item.forecastCompletionLastWeek ?? '')
      if (!forecastFinish || !forecastCompletionLastWeek) return null
      return differenceInCalendarDays(forecastFinish, forecastCompletionLastWeek)
    })
    .filter((diff) => diff !== null)

  if (differences.length === 0) return false

  //* Split into predecessorDiff and successorDiffList
  const predecessorDiff = differences[0]
  const successorDiffList = differences.slice(1)

  // If predecessorDiff is null or undefined, return false (shouldn't happen due to filter, but for type safety)
  if (predecessorDiff == null) return false

  //* If any successorDiff is different from predecessorDiff and less than predecessorDiff, return true
  const isDifference = successorDiffList.some((successDiff) => {
    return successDiff != null && predecessorDiff > successDiff && successDiff !== predecessorDiff
  })

  return isDifference

  //* old Code
  // const first = differences[0]
  // return differences.some((diff) => diff !== first)
}

export function shouldSuccessorHaveDifferentVariance(stages: IStatusForTable[]): boolean {
  //* Prevent record
  const data = stages?.filter(
    (item) => item.stageStatus !== 'DLP and Project Closeout' && item.stageStatus !== 'Construction',
  )

  //* Generate a unique key for each record of statuses
  const statusesKeys = data.map((item) => {
    const id = item.id
    const key = item.id
    return { id, key }
  })

  //* Successor validation
  for (let index = 0; index < data.length; index++) {
    const currentRecord = data[index]
    //* Check is current record have variance
    const isCurrentRecordForecastDateHighlighted = shouldHighlightForecastDate(
      currentRecord.forecastFinish ?? '',
      currentRecord.forecastCompletionLastWeek ?? '',
    )
    if (isCurrentRecordForecastDateHighlighted) {
      //* Find all the records from statusesKeys which are present in currentRecord.successor
      const successorKeys = statusesKeys
        .filter((item) => currentRecord?.successor?.includes(item.key))
        .map((item) => item.id)

      //* find all the records from the statuses which are present in successorKeys
      const successorRecords = data.filter((item) => successorKeys.includes(item.id))
      if (successorRecords.length > 0) {
        //*Pass Current record with it successorRecord to compare days difference
        const isAnyMisMatch = hasForecastDateMismatch([currentRecord, ...successorRecords])
        if (isAnyMisMatch) {
          return true //* Return true if any mismatch is found
        }
      }
    }
  }
  //* If no mismatches were found, return false
  return false
}
