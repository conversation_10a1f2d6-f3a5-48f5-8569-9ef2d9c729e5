import { IStageFormTable, IStageFormTablePayloadForValidation } from '../interface'
import {
  getPhaseWeightageTotalAndRequired,
  getDesignStageWeightageTotalAndRequired,
  cleanNumber,
} from '../validationError/weightageValidation'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { ExtraStatusFields, IStatus } from '@/src/redux/status/interface'
import { setMultiSelectedValueToWildCard } from '@/src/utils/arrayUtils'

export class CategoryValidator {
  static async isCategoryAvailable(
    values: Partial<IStageFormTablePayloadForValidation>,
    currentStatus: IStatus,
    statuses: IStageFormTable[],
  ): Promise<boolean> {
    if ('project_to_project_phase_category_ids' in values) {
      return (
        Array.isArray(values?.project_to_project_phase_category_ids) &&
        values?.project_to_project_phase_category_ids.length > 0
      )
    }
    return false
  }
}

export const checkValidationStatus = async (
  values: Partial<IStageFormTablePayloadForValidation>,
  currentStatus: IStatus,
  statuses: IStageFormTable[],
  combinationName?: string,
) => {
  const messages: any = []
  const modifiedStatues = statuses.map((item) => ({
    ...item,
    categoryIDs:
      item?.categoryIDsArray && item?.categoryIDsArray.length > 0
        ? setMultiSelectedValueToWildCard(item?.categoryIDsArray)
        : '',
    phaseIDs:
      item?.phaseIDsArray && item?.phaseIDsArray.length > 0 ? setMultiSelectedValueToWildCard(item.phaseIDsArray) : '',
  }))
  const modifiedCurrentRecord = {
    ...currentStatus,
    categoryIDs: currentStatus?.LookupProjectToProjectPhaseCategory
      ? setMultiSelectedValueToWildCard(currentStatus.LookupProjectToProjectPhaseCategory.map((cat) => cat.id))
      : '',
    phaseIDs: currentStatus?.LookupProjectToPhase
      ? setMultiSelectedValueToWildCard(currentStatus.LookupProjectToPhase.map((phase) => phase.id))
      : '',
    statusID: currentStatus?.MasterProjectStageStatus?.id?.toString() || '',
  }

  const isCategoryPass = await CategoryValidator.isCategoryAvailable(values, modifiedCurrentRecord, modifiedStatues)
  const isPhaseWeighagePass = checkPhaseWeighage(values, modifiedCurrentRecord, modifiedStatues)
  const isDesignStageWeighagePass = checkDesignStageWeighage(
    values,
    modifiedCurrentRecord,
    modifiedStatues,
    combinationName,
  )

  if (!isCategoryPass) {
    messages.push('At least one Category is required')
  }

  if (isPhaseWeighagePass) {
    messages.push(isPhaseWeighagePass)
  }

  if (isDesignStageWeighagePass) {
    messages.push(isDesignStageWeighagePass)
  }

  return messages || []
}

const checkPhaseWeighage = (
  values: Partial<IStageFormTablePayloadForValidation>,
  currentStage: IStatus & ExtraStatusFields,
  statuses: IStageFormTable[],
) => {
  const modifiedCurrentRecord: any = generateRecord(currentStage, values)
  const { total, required } = getPhaseWeightageTotalAndRequired<IStageFormTable>(
    statuses,
    modifiedCurrentRecord,
    keys,
    1,
  )
  const newValue = Number(values?.phase_weightage || 0)
  if (Number(newValue) === 0) return false
  if (Number(total) + Number(newValue) > 100) {
    const errorMessage =
      Number(required) > 0
        ? `Phase weightage across stages must not exceed 100 (Remaining value is: ${Number(required)})`
        : `Phase weightage across stages must not exceed 100 (Remaining value is: 0)`
    return errorMessage
  }
  return false
}

const keys = {
  phaseKey: 'phaseIDs',
  categoryKey: 'categoryIDs',
  stageStatusKey: 'statusID',
  weightageKey: 'phaseWeightage',
}

const designKeys = {
  phaseKey: 'phaseIDs',
  categoryKey: 'categoryIDs',
  stageStatusKey: 'statusID',
  weightageKey: 'designStageWeightage',
}

const checkDesignStageWeighage = (
  values: Partial<IStageFormTablePayloadForValidation>,
  currentStage: IStatus,
  statuses: IStageFormTable[],
  combinationName?: string,
) => {
  const modifiedCurrentRecord: any = generateRecord(currentStage, values)
  const { total, required } = getDesignStageWeightageTotalAndRequired(statuses, modifiedCurrentRecord, designKeys, 1)
  const newValue = Number(values?.design_stage_weightage || 0)
  if (Number(newValue) === 0) return false
  if (Number(total) + Number(newValue) > 100) {
    const errorMessage =
      Number(required) > 0
        ? `Design Stage weightage for ${combinationName ? `(${combinationName})` : 'this combination'} must not exceed 100 (Remaining value is: ${required})`
        : `Design Stage weightage for ${combinationName ? `(${combinationName})` : 'this combination'} must not exceed 100 (Remaining value is: 0)`
    return errorMessage
  }
  return false
}

const generateRecord = (currentStage: IStatus, values: Partial<IStageFormTablePayloadForValidation>) => {
  const modifiedCurrentRecord = {
    id: currentStage.id ? String(currentStage.id) : '',
    stageStatus: (values?.stage_status ? values.stage_status : currentStage.stage_status) || '',
    sortingOrder: currentStage.project_status_sorting_order || 0,
    designStageWeightage: values?.design_stage_weightage
      ? cleanNumber(Number(values.design_stage_weightage))?.toString()
      : currentStage.design_stage_weightage
        ? cleanNumber(Number(currentStage.design_stage_weightage) * 100)?.toString()
        : '',
    phaseWeightage: values?.phase_weightage
      ? cleanNumber(Number(values.phase_weightage))?.toString()
      : currentStage.phase_weightage
        ? cleanNumber(Number(currentStage.phase_weightage) * 100)?.toString()
        : '',
    predecessor: values?.predecessor ? values.predecessor : currentStage.predecessor || [],
    successor: values?.successor ? values.successor : currentStage.successor || [],
    categoryIDs: values?.project_to_project_phase_category_ids
      ? setMultiSelectedValueToWildCard(values?.project_to_project_phase_category_ids)
      : '',
    phaseIDs: values?.project_to_project_phase_ids
      ? setMultiSelectedValueToWildCard(values?.project_to_project_phase_ids)
      : '',
    statusID: values?.master_project_stage_status_id?.toString() || '',
  }
  return modifiedCurrentRecord
}
