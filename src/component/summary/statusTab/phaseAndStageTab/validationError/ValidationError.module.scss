@import '/styles/breakpoints.scss';

.sidebar {
  position: fixed;
  top: 0;
  left: -100%; /* Hidden by default on the left */
  height: 100%;
  max-width: calc(100vw - 1024px); /* Adjust width as per your need */
  width: calc(100vw - 1060px);
  background-color: #fff;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.3); /* Adjust shadow direction */
  z-index: 9999;
  transition: left 0.3s ease-in-out; /* Transition for the left property */
  @include respond-to('mobile') {
    min-width: 100%;
  }

  @include respond-to('tablet') {
    min-width: 450px;
  }

  @include respond-to('laptop') {
    width: calc(100vw - 1600px);
  }

  @include respond-to('desktop') {
    width: calc(100vw - 1400px);
  }

  &.open {
    left: 0; /* Slide in from the left */
  }

  .sidebarContent {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow-y: auto; /* Scrollable when content exceeds the height */
    padding: 16px;
  }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 100%; /* Ensure the container fits inside the sidebar */
  overflow-y: auto; /* Enables scrolling within the container */
  padding-right: 10px;
}

.errorItem {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.errorDetails {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.strong {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.field {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

.message {
  font-size: 14px;
  color: #555;
  width: calc(100% - 130px); /* Ensures proper alignment with the strong label */
}

.closeButton {
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 18px;
  color: #555;
  padding: 8px;
}

.warning {
  font-size: 16px;
  font-weight: bold;
  color: #d9534f; /* Change this color as needed for a warning style */
  margin-bottom: 16px; /* Spacing between warning and error items */
}
.bold {
  font-weight: bold;
}
