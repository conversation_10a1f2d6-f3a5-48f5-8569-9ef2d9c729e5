import React from 'react'
import styles from './ValidationError.module.scss'
import Typo<PERSON><PERSON><PERSON> from '@/src/component/shared/typography'
import CloseCircleIcon from '@/src/component/svgImages/closeCircleIcon'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'

interface ModalProps {
  validationModel?: any[]
  open: boolean
  onClose: () => void
  handleConfirm?: () => void // Optional since it's not used in this component
  onSkip?: () => void
}

const ValidationError: React.FC<ModalProps> = ({ validationModel = [], open, onClose, onSkip }) => {
  const handleClose = () => {
    onClose()
  }

  const phaseData = validationModel?.filter((item) => item.phase_weightage_field === 'Phase Weightage')
  const designData = validationModel?.filter((item) => item.design_stage_weightage_field === 'Design Stage Weightage')

  const filterPhase: any = phaseData?.reduce((acc: any[], item: any) => {
    const value = item?.stage_status

    // Check if the current item's stage_status already exists in the accumulator
    const exists = acc.some((existingItem: any) => existingItem?.stage_status === value)

    // If it doesn't exist, push the item into the accumulator
    if (!exists) {
      acc.push(item)
    }

    return acc // Return the accumulator for the next iteration
  }, [])

  const filterDesign: any = designData?.reduce((acc: any[], item: any) => {
    const value = item?.stage_status
    const phase = item?.phase

    // Check if the current item's stage_status already exists in the accumulator
    const exists = acc.some(
      (existingItem: any) => existingItem?.stage_status === value && phase === existingItem?.phase,
    )

    // If it doesn't exist, push the item into the accumulator
    if (!exists) {
      acc.push(item)
    }

    return acc // Return the accumulator for the next iteration
  }, [])

  const validErrorData = [...(filterPhase || []), ...(filterDesign || [])]

  // Function to render individual error items
  const renderErrorItem = (item: any) => (
    <div key={item.id} className={styles.errorItem}>
      {item.design_stage_weightage_field && (
        <div className={styles.field}>
          <div className={styles.strong}>Phase:</div>
          <div className={styles.message}>{item.phase?.replaceAll(MULTI_SELECT_SEPARATOR, ', ')}</div>
        </div>
      )}
      <div className={styles.field}>
        <div className={styles.strong}>Stage Status:</div>
        <div className={`${styles.message} ${styles.bold}`}>{item.stage_status}</div>
      </div>

      {item.phase_weightage_field && (
        <div className={styles.field}>
          <div className={styles.strong}>Field:</div>
          <div className={`${styles.message} ${styles.bold}`}>{item.phase_weightage_field}</div>
        </div>
      )}
      {item.phase_weightage_error && (
        <div className={styles.field}>
          <div className={styles.strong}>Error:</div>
          <div className={styles.message}>
            The sum of Phase Weightage <span className={styles.bold}>{item.phase_weightage_error}</span>
          </div>
        </div>
      )}
      {item.design_stage_weightage_field && (
        <div className={styles.field}>
          <div className={styles.strong}>Field:</div>
          <div className={`${styles.message} ${styles.bold}`}>{item.design_stage_weightage_field}</div>
        </div>
      )}
      {item.design_stage_weightage_error && (
        <div className={styles.field}>
          <div className={styles.strong}>Error:</div>
          <div className={styles.message}>
            The sum of Design Stage Weightage <span className={styles.bold}>{item.design_stage_weightage_error}</span>
          </div>
        </div>
      )}
    </div>
  )

  // Check for any weightage fields in the validationModel
  const hasPhaseWeightage = validationModel?.some((item) => item?.phase_weightage_field)
  const hasDesignStageWeightage = validationModel?.some((item) => item?.design_stage_weightage_field)

  const warningMessage = [
    hasPhaseWeightage ? 'Phase Weightage' : '',
    hasDesignStageWeightage ? 'Design Stage Weightage' : '',
  ]
    .filter(Boolean)
    .join(' or ')

  return (
    <div className={`${styles.sidebar} ${open ? styles.open : ''}`}>
      <div className={styles.sidebarContent}>
        <div className={styles.closeButton}>
          <CloseCircleIcon onClick={handleClose} />
        </div>
        <div className={styles.errorContainer}>
          {Array.isArray(validErrorData) && validErrorData.length > 0 ? (
            <>
              <div className={styles.warning}>
                There are invalid values for {warningMessage}. Please update the correct values before proceeding.
              </div>
              {/* <TypographyField
                align="right"
                text={'Skip For Now'}
                sx={{
                  color: 'black',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                }}
                onClick={onSkip}
              /> */}
              {validErrorData?.map(renderErrorItem)}
            </>
          ) : (
            <div>No validation errors found.</div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ValidationError
