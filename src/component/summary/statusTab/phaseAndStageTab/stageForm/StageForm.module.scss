@import '/styles/color.scss';

.buttons {
  display: flex;
  gap: 10px;
  padding-top: 10px;
  justify-content: space-between;
}

.tableButtons {
  display: flex;
  gap: 10px;
}

.phaseField {
  :global(.MuiFormHelperText-root) {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: unset !important;
    word-break: break-word;
    display: block !important;
  }

  // Style for disabled Mui input
  :global(.Mui-disabled) {
    cursor: not-allowed !important;
    opacity: 0.8 !important;
  }
}

.highlightedField {
  > div > div {
    border-radius: 5px;
    background: #f0f8ff !important;
    border: 1px solid rgb(40 101 220) !important;
  }
}
