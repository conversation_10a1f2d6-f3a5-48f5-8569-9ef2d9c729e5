import React, { useRef, useState } from 'react'
import { Button } from '@mui/material'
import CategoryTab from './categoryTab'
import { IPhaseAndStageTab, ITab } from './interface'
import styles from './phaseAndStageTab.module.scss'
import PhaseTab from './phaseTab'
import StageTab from './stageTab'
import TypographyField from '@/src/component/shared/typography'
import CloseCircleIcon from '@/src/component/svgImages/closeCircleIcon'
import { MOBILE } from '@/src/constant/breakpoint'
import { useBreakpoint } from '@/src/customeHook/useBreakPoint'

export const Tab: React.FC<ITab> = ({ name, onSelect, isActive, children }) => {
  const handleClick = () => {
    onSelect(name)
  }

  return (
    <div className={`${styles.tab} ${isActive ? styles.selectedTab : ''}`} onClick={handleClick}>
      <TypographyField variant={isActive ? 'h2' : 'h7'} text={children} />
    </div>
  )
}

const PhaseAndStageTab: React.FC<IPhaseAndStageTab> = ({
  onClose,
  isStageDrawer = false,
  setIsStageVisited,
  isStageVisited,
}) => {
  const breakpoint = useBreakpoint()
  const [gridFilter, setGridFilter] = useState<{ colId: string; values: any }[]>([])
  const [selectedTab, setSelectedTab] = useState<string>('Category')
  const formRef = useRef<HTMLDivElement>(null)

  const handleTabSelect = (tabName: string) => {
    setSelectedTab(tabName)
    // if (tabName === 'Stage') {
    //   !isStageVisited && setIsStageVisited && setIsStageVisited(true)
    // }
  }

  return (
    <div className={styles.container} ref={formRef}>
      <div className={styles.header}>
        <div className={styles.tabButtons}>
          {(['Category', 'Phase/Package', 'Stage'] as const).map((tabName) => (
            <Tab key={tabName} name={tabName} onSelect={handleTabSelect} isActive={selectedTab === tabName}>
              {tabName}
            </Tab>
          ))}
        </div>
        {breakpoint === MOBILE ? (
          <div className={styles.closeIcon}>
            <CloseCircleIcon onClick={onClose} />
          </div>
        ) : (
          <Button className={styles.closeButton} onClick={onClose}>
            X Close
          </Button>
        )}
      </div>
      <div className={styles.contain}>
        {selectedTab === 'Stage' && (
          <StageTab
            gridFilters={gridFilter}
            setGridFilters={setGridFilter}
            selectTab={selectedTab}
            handleScrollToTop={() => {
              formRef.current?.scrollIntoView({ behavior: 'smooth' })
            }}
            setIsStageVisited={setIsStageVisited}
            isStageVisited={isStageVisited}
          />
        )}
        {selectedTab === 'Phase/Package' && <PhaseTab isStageDrawer={isStageDrawer} />}
        {selectedTab === 'Category' && <CategoryTab isStageDrawer={isStageDrawer} />}
      </div>
    </div>
  )
}

export default PhaseAndStageTab
