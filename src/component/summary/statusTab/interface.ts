import { ILookupProjectToPhase, ILookupProjectToProjectPhaseCategory } from '@/src/redux/status/interface'

export interface IStatusData {
  id?: number
  phase: any
  stageStatus: any
  manager: any
  designStageWeightage: any
  phaseWeightage: any
  isSelect?: any
  isNewRabin?: boolean
  actualPlanPercentage?: string
  revPlanPercentage?: string
  baselinePlanFinish?: string
  revisedBaselineFinish?: string
  forecastFinish?: string
}

export interface IStatusColumn {
  key: string
  label: string
  filterCell?: () => JSX.Element | undefined
  renderHeader?: any
  renderCell?: (value: any, row: IStatusData, rowIndex: number) => JSX.Element | undefined
  cellStyle?: React.CSSProperties
  headerStyle?: React.CSSProperties
  isNewRabin?: boolean
}

export interface IStatusTableProps {
  columns: IStatusColumn[]
  data: IStatusData[]
  onRowClick: (row: Record<string, any>, rowIndex: number) => void
}

export interface TableHeaderProps {
  columns: IStatusColumn[]
}

export interface TableBodyProps {
  data: IStatusData[]
  columns: IStatusColumn[]
  onRowClick: (row: Record<string, any>, rowIndex: number) => void
}

export interface TableRowProps {
  row: IStatusData
  rowIndex: number
  columns: IStatusColumn[]
  onRowClick: (row: Record<string, any>, rowIndex: number) => void
}

export interface TableCellProps {
  value: any
  column: IStatusColumn
  rowIndex: number
  row: IStatusData
}

export interface IStatusForTable {
  id: number
  sortingOrder: number
  actualLWeek: number | string
  contractor: string
  pmcConsultant?: string
  planLWeek: number | string
  // project_phase_category: string
  // phase: string
  LookupProjectToPhase: ILookupProjectToPhase[]
  LookupProjectToProjectPhaseCategory: ILookupProjectToProjectPhaseCategory[]
  variance: string
  stageStatus: string
  subStatus: string
  pmc_consultant?: string
  designStageWeightage: number | string
  phaseWeightage: number | string
  isRibbon?: boolean
  design_manager?: string
  delivery_project_manager?: string
  procurement_manager?: string
  svp?: string
  baseLineDiff: number | null
  forecastDiff: number | null
  actualPlanPercentage?: string
  revPlanPercentage?: string
  baselinePlanFinish?: string
  revisedBaselineFinish?: string
  forecastFinish?: string
  forecastCompletionLastWeek?: string | null
  plan_start_date?: string | null
  forecast_start_date?: string | null
  forecast_duration?: number | null
  plan_duration?: number | null
  calculated_plan_progress?: string | null
  predecessor: number[]
  successor: number[]
  slippage_justification?: string
}

export interface IRowIdDataById {
  isBaselinePlanFinishEdit: boolean
}
