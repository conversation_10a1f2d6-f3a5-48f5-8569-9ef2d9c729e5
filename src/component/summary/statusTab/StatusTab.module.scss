@import '/styles/color.scss';

.contentWrapper {
  padding: 15px 24px 15px 0px;
  height: calc(100% - 41px);
  box-sizing: border-box;
}
.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .filterTab {
    padding-left: 10px !important;
    display: flex;
    flex-wrap: wrap-reverse;
    gap: 10px;
    width: 100%;
    justify-content: space-between;
    .tab {
      cursor: pointer;
      padding: 4px 10px;
      color: $DARK;
      height: fit-content;
      width: fit-content;
      border-radius: 30px;
      background-color: $WHITE;
    }
    .activeTab {
      color: $WHITE;
      background-color: $DARK;
    }
  }
}

.searchField {
  > div > div {
    padding-right: 11px !important;
    background-color: $WHITE !important;
  }
  > div > div > input {
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    letter-spacing: 0em !important;
    text-align: left !important;
    padding: 0px !important;
    padding-top: 11px !important;
    padding-bottom: 11px !important;
    padding-left: 8px !important;
    background-color: $WHITE;
    &::placeholder {
      font-size: 12px !important;
      font-weight: 400 !important;
      line-height: 18px !important;
      color: $DARK_200 !important;
    }
  }
}

.endAdornment {
  background-color: $WHITE;
  margin-right: 0px !important;
}

.endAdornmentIcon {
  height: 16px;
  width: 16px;
}

.dropDownFilter {
  > div > div {
    background: $WHITE !important;
  }
}

.focusDropDown {
  border: none;
}

.dropDownPlaceholder {
  text-align: left;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 18px !important;
  color: $DARK_100 !important;
}

.editRowIcon {
  cursor: pointer;
}

.loaderContainer {
  height: 100%;
  width: 100%;
}

.textField {
  width: fit-content !important;
  > div {
  }
  > div {
    width: 60px;
    > div > input {
      text-align: center;
      background: white !important;
      padding: 0px !important;
      color: $DARK;
      font-size: 12px !important;
    }
  }
}

.cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.icons {
  cursor: pointer;
  display: flex;
  justify-items: center;
  align-items: center;
  text-align: center;
}

.header {
  position: sticky;
  position: -webkit-sticky;
  top: 0; /* required */
  background-color: #444444;
  color: rgb(255, 255, 255);
  font-family: Poppins;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  .filter {
    color: rgb(255, 255, 255);
  }
}

.cellData {
  padding: 11px 10px;
  font-family: Poppins;
  display: flex;
  align-items: center;
  line-height: normal !important;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0em;
  text-align: left;
  color: #000000;
}
.subStatus {
  font-family: Poppins;
  color: #444444;
  font-size: 10px;
  font-weight: 400;
  line-height: 15px;
}

.progressBar {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tableWrapper {
  margin-top: 20px;
  position: relative;
  max-height: calc(100% - 30px);
  overflow: auto;
  @media (max-width: 1024px) {
    height: calc(100% - 50px);
  }
  @media (max-width: 768px) {
    height: calc(100% - 60px);
  }
}

.actionOfTable {
  margin-left: auto;
  // justify-content: end;
  // width: 100%;

  display: flex;
  gap: 10px;
}

// .expandTable {
//   max-height: calc(100vh - 270px) !important;
// }
.actionsTableBtn {
  background-color: #495e69 !important;
  color: #fff !important;
  width: 162px !important;
}

.editModeBtn {
  background-color: #808080 !important;
  color: white;
}
.phaseStageDrawer {
  display: block;
  background-color: #000000;
}
.editIcon {
  background-color: $PULSE_TURQUOISE;
  color: $WHITE;
  border-radius: 20px;
  width: 25px;
  height: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.infoContainer {
  display: flex;
  align-items: center;
  gap: 5px;

  .infoIcon {
    cursor: pointer;
    margin-top: 5px;
  }
}

.textEditor {
  margin-top: 15px;
  .title {
    font-family: Poppins;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: #808080;
  }
}
