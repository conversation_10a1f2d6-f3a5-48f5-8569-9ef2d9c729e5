@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.fields {
  display: flex;
  gap: 50px;
  justify-content: space-between;
  padding: 10px 12px;

  @include respond-to('mobile') {
    flex-direction: column;
    gap: 5px;
    padding-bottom: 0;
  }
  @include respond-to('tablet') {
    flex-direction: row;
    gap: 50px;
    padding: 10px 12px;
  }
}
.leftContent {
  width: 50%;
  overflow: hidden;
  @include respond-to('mobile') {
    width: 100%;
  }
  @include respond-to('tablet') {
    width: 50%;
  }
}

.rightContent {
  width: 50%;
  @include respond-to('mobile') {
    width: 100%;
  }

  @include respond-to('tablet') {
    width: 50%;
  }
}

.projectDetailsCollapse {
  padding: 20px 12px;
}

.textArea {
  width: 100%;
  border-radius: 0px;
  display: flex;
  :focus {
    border-radius: 0px;
  }
  > textarea {
    max-height: 85px !important;
    overflow-y: scroll !important;
    background: $FOCUS_TEXTFIELD_BG !important;
    border-bottom: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
    color: $DARK;
    // background: $WHITE !important;
    border-radius: 0px !important;
    padding: 6px 0 0px 8px !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    height: 78px !important;
    // border-bottom: 1px solid $DARK !important;
    // min-height: auto !important; // for do small
    // max-height: 100% !important;
    &::-webkit-scrollbar {
      width: 3px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #dddddd;
      border-radius: 5px;
      width: 3px;
    }
  }
}

.projectBriefText {
  background-color: $ATHENS_GRAY;
  padding: 12px 15px;
  border-radius: 8px;
  color: $DARK;
  width: 100%;
}

.projectBrifLabel {
  margin: 0px 5px 0px 12px;
  white-space: nowrap;
  font-size: 12px !important;
  color: #808080;
  display: flex;
  flex-flow: wrap;
  justify-content: space-between;
  svg {
    width: 15px;
    height: 15px;
  }
}

.textAreaValue {
  width: 100%;
  border-radius: 0px;
  display: flex;
  :focus {
    border-radius: 0px;
  }
  > textarea {
    background: $WHITE !important;
    border: 1px solid $LIGHT_200 !important;
    color: $DARK;
    // background: $WHITE !important;
    border-radius: 0px !important;
    padding: 6px 0 0px 8px !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    // border-bottom: 1px solid $DARK !important;
    // min-height: auto !important; // for do small
    // max-height: 100% !important;
  }
}

.textEditor {
  margin-bottom: 20px;
  margin-left: 13px;
  padding-right: 12px;
  > div {
    padding-top: 2px;
  }
}

.labelStyle {
  min-width: 50px;

  @include respond-to('mobile') {
    min-width: 100px !important;
    max-width: 120px !important;
    white-space: unset !important;
  }

  @include respond-to('tablet') {
    min-width: 130px !important;
    max-width: 140px !important;
    white-space: unset !important;
  }

  @include respond-to('laptop') {
    min-width: 140px !important;
    max-width: 150px !important;
    white-space: unset !important;
  }
}

.dotStyle {
  min-height: 23px;
  padding: 9px 0 6px 5px;
  border-bottom: 1px solid $LIGHT_200;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
}
