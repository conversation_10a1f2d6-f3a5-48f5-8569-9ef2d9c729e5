import React, { useMemo } from 'react'
import { useRouter } from 'next/router'
import { getValue } from './helper'
import styles from './SustainabilityCollapse.module.scss'
import ImageCard from '../imageCard'
import { ISustainabilityCollapse } from '../interface'
import useSustainability from '@/src/redux/sustainability/useSustainability'
import { formatNumberWithCommas, removeCommasAndConvert } from '@/src/utils/numberUtils'

const maxNumber = 999999999999999

const SustainabilityCollapse = ({ summaryEdit, formik }: ISustainabilityCollapse) => {
  const router = useRouter()
  const { sustainabilities } = useSustainability()

  const sustainability: any = useMemo(() => {
    const found = sustainabilities.find(
      (item: { project_name: string | string[] | undefined }) => item.project_name === router.query.slug,
    )
    return found
      ? {
          ...found,
          waste_recycled_percentage: found.waste_recycled_percentage ? found.waste_recycled_percentage : 0,
          reinvented_economy_percentage: found.reinvented_economy_percentage ? found.reinvented_economy_percentage : 0,
          reinvented_economy_value: found.reinvented_economy_value ? found.reinvented_economy_value : 0,
          pearl_rating_percentage: found.pearl_rating_percentage ? found.pearl_rating_percentage : 0,
          recycled_material_percentage: found.recycled_material_percentage ? found.recycled_material_percentage : 0,
          worker_welfare_compliance_percentage: found.worker_welfare_compliance_percentage
            ? found.worker_welfare_compliance_percentage
            : 0,
          renewable_energy: found.renewable_energy ? found.renewable_energy : 0,
          carbon_emissions: found.carbon_emissions ? found.carbon_emissions : 0,
          emissions_per_m2: found.emissions_per_m2 ? found.emissions_per_m2 : 0,
          number_of_grievances: found.number_of_grievances ? found.number_of_grievances : 0,
        }
      : undefined
  }, [sustainabilities, router.query.slug])

  const handleFieldChange = (field: string, value: any, max: number) => {
    // Check if the input is empty or has a valid number with at most 2 decimal places
    const decimalRegEx = /^(\d+(\.\d{0,2})?)?$/
    // Allow clearing the field by setting an empty value
    if (value === '' || value === null) {
      formik.setFieldValue(field, null)
      return
    }
    // Convert the value to a number
    const numericValue = parseFloat(value)
    // Validate if the value is between 0 and max, and matches the decimal pattern
    if (!isNaN(numericValue) && decimalRegEx.test(value) && numericValue >= 0 && numericValue <= max) {
      formik.setFieldValue(field, value)
    }
  }

  const handleNonDecimalChange = (value: string, fieldName: string) => {
    if (value === '') {
      formik.setFieldValue(fieldName, null)
      return
    }
    if (value?.length > 15) return
    const validValue = removeCommasAndConvert(value)
    const numValue = Number(validValue?.toString())
    if (!isNaN(numValue) && numValue >= 0) {
      formik.setFieldValue(fieldName, value === '' ? '' : numValue.toString())
    }
  }

  return (
    <div className={styles.cards} onClick={(e) => e.stopPropagation()}>
      <ImageCard
        label="Waste Recycled %"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'waste_recycled_percentage', !summaryEdit, true)}
        onChange={(value) => handleFieldChange('waste_recycled_percentage', value, 100)}
        imageSrc="/svg/recycleSymbol.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="waste_recycled_percentage"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />
      <ImageCard
        formik={formik}
        label="Recycled Material %"
        value={
          getValue(formik, summaryEdit, sustainability, 'recycled_material_percentage', !summaryEdit, true)
          // (!sustainability && !summaryEdit) || !sustainability
          //   ? ''
          // :
          // !summaryEdit
          //   ? formik?.values?.recycled_material_percentage
          //     ? `${formatNumberWithCommas(formik?.values?.recycled_material_percentage)}%` || ''
          //     : ''
          //   : formik?.values?.recycled_material_percentage.toString()
          //     ? formik?.values?.recycled_material_percentage
          //     : ''
        }
        onChange={(value) => handleFieldChange('recycled_material_percentage', value ? value : null, 100)}
        imageSrc="/svg/recycledMaterial.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName={'recycled_material_percentage'}
        summaryEdit={summaryEdit}
        isEditable={true}
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Pearl Rating"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'pearl_rating_percentage')}
        onChange={(value) => handleFieldChange('pearl_rating_percentage', value, 5)}
        imageSrc="/svg/pearlRating.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="pearl_rating_percentage"
        summaryEdit={summaryEdit}
        isEditable
        isFormattedNumber
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Renewable Energy"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'renewable_energy')}
        onChange={(value) => handleFieldChange('renewable_energy', value, maxNumber)}
        imageSrc="/svg/renewable-energy.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="renewable_energy"
        summaryEdit={summaryEdit}
        isEditable
        isFormattedNumber
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Carbon Emissions"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'carbon_emissions')}
        onChange={(value) => handleFieldChange('carbon_emissions', value, maxNumber)}
        imageSrc="/svg/carbon-emissions.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="carbon_emissions"
        summaryEdit={summaryEdit}
        isEditable
        isFormattedNumber
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Emissions per m2"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'emissions_per_m2')}
        onChange={(value) => handleFieldChange('emissions_per_m2', value, maxNumber)}
        imageSrc="/svg/emissions-per-m2.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="emissions_per_m2"
        summaryEdit={summaryEdit}
        isEditable
        isFormattedNumber
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Workers Welfare Compliance %"
        formik={formik}
        value={getValue(
          formik,
          summaryEdit,
          sustainability,
          'worker_welfare_compliance_percentage',
          !summaryEdit,
          true,
        )}
        onChange={(value) => handleFieldChange('worker_welfare_compliance_percentage', value, 100)}
        imageSrc="/svg/manhour.svg"
        imageWidth={65}
        imageHeight={50}
        fieldName="worker_welfare_compliance_percentage"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="No. of Grievances"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'number_of_grievances')}
        onChange={(value) => handleNonDecimalChange(value, 'number_of_grievances')}
        imageSrc="/svg/no-of-grievances.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="number_of_grievances"
        summaryEdit={summaryEdit}
        isEditable
        isFormattedNumber
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Reinvested In The Economy %"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'reinvented_economy_percentage', !summaryEdit, true)}
        onChange={(value) => handleFieldChange('reinvented_economy_percentage', value, 100)}
        imageSrc="/svg/reinvestInPercentage.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="reinvented_economy_percentage"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />

      <ImageCard
        label="Reinvested In The Economy(AED)"
        formik={formik}
        value={getValue(formik, summaryEdit, sustainability, 'reinvented_economy_value', false, false, true)}
        onChange={(value) => handleNonDecimalChange(value, 'reinvented_economy_value')}
        imageSrc="/svg/reinvestInAED.svg"
        imageWidth={50}
        imageHeight={50}
        fieldName="reinvented_economy_value"
        summaryEdit={summaryEdit}
        isEditable
        className={summaryEdit ? styles.blueBorder : ''}
      />
    </div>
  )
}

export default SustainabilityCollapse
