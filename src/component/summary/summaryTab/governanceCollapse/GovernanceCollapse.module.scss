@import '/styles/breakpoints.scss';

.cards {
  display: flex;
  flex-wrap: wrap;
  gap: 9px;
  padding: 20px 12px;
  @include respond-to('mobile') {
    justify-content: center;
    padding: 20px 8px;
  }
  @include respond-to('tablet') {
    justify-content: unset;
    padding: 20px 30px;
  }
}
// TODO:
// .leftContent {
//   width: 50%;
// }
// .rightContent {
//   width: 50%;
// }
.highlight {
  border: 1px solid rgb(40 101 220) !important;
  background-color: #f0f8ff !important;
}
