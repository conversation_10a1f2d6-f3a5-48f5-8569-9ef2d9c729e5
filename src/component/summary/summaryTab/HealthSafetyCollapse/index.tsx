import React from 'react'
import styles from './HealthSafetyCollapse.module.scss'
import ImageCard from '../imageCard'
import { IHealthSafetyCollapse } from '../interface'
import { removeCommasAndConvert } from '@/src/utils/numberUtils'

const formatNumberWithCommas = (number: any) => {
  return number.toLocaleString('en-US')
}

// Utility function to handle numeric validation and formatting
const handleNumericChange = (value: string, fieldName: string, formik: any) => {
  if (value === '') {
    formik.setFieldValue(fieldName, 0)
    return
  }
  if (value?.length > 15) return
  const validDecimalPattern = /^0(\.\d{0,2})?$|^1(\.0{0,2})?$/
  const validValue = removeCommasAndConvert(value)

  if (fieldName === 'lti') {
    // Apply validation for 'lti' (between 0 and 1 with up to 2 decimal places)
    if (value === '' || validDecimalPattern.test(value)) {
      formik.setFieldValue(fieldName, parseInt(value))
    }
  } else {
    // Allow any non-negative numeric value for other fields
    const numValue = Number(validValue?.toString())
    if (value === '' || (!isNaN(numValue) && numValue >= 0)) {
      formik.setFieldValue(fieldName, value === '' ? 0 : numValue)
    }
  }
}

const HealthSafetyCollapse = ({ summaryEdit, formik }: IHealthSafetyCollapse) => {
  const isEdit = summaryEdit && !formik.values.is_external_lookup_for_health_and_safety
  return (
    <div className={styles.cards} onClick={(e) => e.stopPropagation()}>
      <ImageCard
        label="No of LTI’s"
        formik={formik}
        summaryEdit={isEdit}
        fieldName="incidents"
        value={
          !isEdit
            ? formik?.values?.incidents?.toString()
              ? formatNumberWithCommas(Number(formik?.values?.incidents))
              : 0
            : formik?.values?.incidents
        }
        imageSrc="/svg/incidents.svg"
        imageWidth={45}
        imageHeight={50}
        onChange={(value: string) => handleNumericChange(value, 'incidents', formik)}
        isEditable={true}
        className={isEdit ? styles.blueBorder : ''}
      />
      <ImageCard
        label="Total Manhours"
        fieldName="man_hours"
        formik={formik}
        isFormattedNumber={true}
        summaryEdit={isEdit}
        value={
          !isEdit
            ? formik?.values?.man_hours?.toString()
              ? formatNumberWithCommas(Number(formik?.values?.man_hours))
              : 0
            : formik?.values?.man_hours
        }
        onChange={(value: string) => handleNumericChange(value, 'man_hours', formik)}
        imageSrc="/svg/manhourFilled.svg"
        imageWidth={65}
        imageHeight={50}
        isEditable={true}
        className={isEdit ? styles.blueBorder : ''}
      />
      <ImageCard
        label="Planned Manpower"
        fieldName="planned_manpower"
        formik={formik}
        isFormattedNumber={true}
        summaryEdit={isEdit}
        value={
          !isEdit
            ? formik?.values?.planned_manpower?.toString()
              ? formatNumberWithCommas(Number(formik?.values?.planned_manpower))
              : 0
            : formik?.values?.planned_manpower
        }
        onChange={(value: string) => handleNumericChange(value, 'planned_manpower', formik)} // Restrict to 0-1
        imageSrc="/svg/manhourFilled.svg"
        imageWidth={65}
        imageHeight={50}
        isEditable={true}
        className={isEdit ? styles.blueBorder : ''}
      />
      <ImageCard
        label="Actual Manpower"
        fieldName="actual_manpower"
        formik={formik}
        isFormattedNumber={true}
        summaryEdit={isEdit}
        value={
          !isEdit
            ? formik?.values?.actual_manpower?.toString()
              ? formatNumberWithCommas(Number(formik?.values?.actual_manpower))
              : 0
            : formik?.values?.actual_manpower
        }
        onChange={(value: string) => handleNumericChange(value, 'actual_manpower', formik)} // Restrict to 0-1
        imageSrc="/svg/manhourFilled.svg"
        imageWidth={65}
        imageHeight={50}
        isEditable={true}
        className={isEdit ? styles.blueBorder : ''}
      />
      <ImageCard
        label="LTI Ratio (Per 1M Mnhrs)"
        fieldName="lti"
        formik={formik}
        isFormattedNumber={true}
        // summaryEdit={isEdit}
        summaryEdit={false}
        value={
          (!isEdit
            ? formik?.values?.lti
              ? formatNumberWithCommas(Number(formik?.values?.lti))
              : formik?.values?.lti
            : formik?.values?.lti) || (formik?.values?.lti === null ? '' : formik?.values?.lti)
        }
        onChange={(value: string) => handleNumericChange(value, 'lti', formik)}
        imageSrc="/svg/LTI.svg"
        imageWidth={75}
        imageHeight={60}
        isEditable={true}
        // className={isEdit ? styles.blueBorder : ''}
      />
    </div>
  )
}

export default HealthSafetyCollapse
