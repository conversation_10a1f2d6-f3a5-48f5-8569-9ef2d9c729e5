import { parse as parseDate, isValid, parse } from 'date-fns'
import { IPotentialBudgetUpliftScope } from '@/src/services/potentialBudgetUpliftScope/interface'

export const generateTempId = () => {
  return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export const getTableData = (array: IPotentialBudgetUpliftScope[]) => {
  const data = array?.map((res) => {
    return {
      id: res?.id,
      current_scope: res?.current_scope,
      amended_scope: res?.amended_scope,
      cost_impact: res?.cost_impact,
    }
  })
  return data || []
}

export const getMinDateForApprovalDate = (firstSubmissionDate: string, lastSubmissionDate: string) => {
  const last =
    typeof lastSubmissionDate === 'string'
      ? parse(lastSubmissionDate, 'dd-MM-yyyy', new Date())
      : new Date(lastSubmissionDate)

  const first =
    typeof firstSubmissionDate === 'string'
      ? parse(firstSubmissionDate, 'dd-MM-yyyy', new Date())
      : new Date(firstSubmissionDate)

  if (isValid(last) && isValid(first)) {
    return last > first ? last : first // Return the maximum date
  } else if (isValid(last)) {
    return last // Return the available date
  } else if (isValid(first)) {
    return first // Return the available date
  } else {
    return null // Return null if neither date is valid
  }
}
