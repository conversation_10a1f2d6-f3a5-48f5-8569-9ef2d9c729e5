import React, { useRef, useState } from 'react'
import { AddOutlined } from '@mui/icons-material'
import { IconButton, Tooltip } from '@mui/material'
import ScopeDetailModal from '../scopeDetailModal'

export interface IScopeDetailTable {
  edit: boolean
}

const ScopeDetailTable: React.FC<IScopeDetailTable> = ({ edit }) => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
      <Tooltip title="Scope Details" arrow>
        <IconButton
          onClick={() => setIsModalOpen(true)}
          sx={{
            fontFamily: 'Poppins',
            color: 'black',
            transition: 'transform 0.2s ease-in-out',
            '&:hover': { transform: 'scale(1.2)', color: '#007bff' },
          }}
        >
          <AddOutlined sx={{ fontSize: 20 }} />
        </IconButton>
      </Tooltip>
      {isModalOpen ? <ScopeDetailModal edit={edit} open={isModalOpen} onClose={() => setIsModalOpen(false)} /> : null}
    </div>
  )
}

export default ScopeDetailTable
