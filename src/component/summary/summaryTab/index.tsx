import React, { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { useQuery } from '@tanstack/react-query'
import { FormikValues, useFormik } from 'formik'
import { useRouter } from 'next/router'
import { toast, Toaster } from 'sonner'
import CollapsibleSection from './collapsibleSection'
import DiscardModel from './discardModel'
import GovernanceCollapse from './governanceCollapse'
import HealthSafetyCollapse from './HealthSafetyCollapse'
import { calculateTotalGovernanceSum, getInitialValues, summarySubmit } from './helper'
import { ISummaryTab, IValidationState } from './interface'
import ProjectDetailsCollapse from './projectDetailsCollapse'
import ProjectManagementCollapse from './projectManagementCollapse'
import styles from './SummaryTab.module.scss'
import SustainabilityCollapse from './sustainabilityCollapse'
import PulseActionButtons from '../../shared/button/PulseActionButtons'
import Loader from '../../shared/loader'
import PulseModel from '../../shared/pulseModel'
import ArchiveCheckIcon from '../../svgImages/archiveCheckIcon'
import GovernanceIcon from '../../svgImages/governance'
import MedicalKitIcon from '../../svgImages/medicalKitIcon'
import ProjectDetails from '../../svgImages/projectDetails'
import UserShieldIcon from '../../svgImages/userShieldIcon'
import ValidationModel from '../../updateProgress/progressForm/validationModel'
import { PROJECT_QUERY_KEY, useUpdateProject } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useGovernance from '@/src/redux/governance/useGovernance'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useStatus from '@/src/redux/status/useStatus'
import useSustainability from '@/src/redux/sustainability/useSustainability'
import { getMasterOneProject } from '@/src/services/projects'
import { getLastUpdatedTime } from '@/src/utils/dateUtils'
import { canEditUser } from '@/src/utils/userUtils'

const SummaryTab: React.FC<ISummaryTab> = ({ expanded }) => {
  const router = useRouter()
  // react-hook
  const [loader, setLoader] = useState(false)
  const [summaryEdit, setSummaryEdit] = useState<boolean>(false)
  const [isDiscard, setIsDiscard] = useState(false)
  const [collapsed, setCollapsed] = useState<string[]>(['Project Details'])
  const [validation, setValidation] = useState<IValidationState>({ open: false, message: [] })
  //custom-hook
  const { statuses } = useStatus()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { sustainabilities, updateSustainabilityApi, getSustainabilitiesApi, addSustainabilityApi } =
    useSustainability()
  const { governances, getGovernanceApi, addGovernanceApi, updateGovernanceApi } = useGovernance()

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const {
    data: project,
    refetch: refetchProject,
    isFetching: isProjectLoading,
  } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  const { mutate: updateProject, isPending: isLoading } = useUpdateProject()

  const onSubmit = async (updatedFields: any, formValues: any) => {
    return await summarySubmit(
      updatedFields,
      formValues,
      formik,
      currentPeriod,
      statuses,
      project,
      updateProject,
      sustainabilities,
      addSustainabilityApi,
      updateSustainabilityApi,
      getSustainabilitiesApi,
      governances,
      getGovernanceApi,
      addGovernanceApi,
      updateGovernanceApi,
      setLoader,
      setSummaryEdit,
      router,
      validation,
      setValidation,
    )
  }

  const sustainability: any = useMemo(() => {
    return sustainabilities?.find((item: any) => {
      return item?.project_name === router.query.slug
    })
  }, [sustainabilities])

  const governance: any = useMemo(() => {
    return governances?.find((item: any) => {
      return item?.project_name === router.query.slug
    })
  }, [governances])

  const initialValues: any = useMemo(() => {
    return getInitialValues(project, sustainability, governance, statuses)
  }, [project, statuses, sustainability, governance])

  const formik = useFormik<FormikValues>({
    initialValues,
    enableReinitialize: true,
    onSubmit: async (values) => {
      const updatedFields = Object.keys(values).reduce((acc: any, key: any) => {
        if (values[key] !== initialValues[key]) {
          acc[key] = values[key] // Keep only updated fields
        }
        return acc
      }, {})
      const res: any = await onSubmit(updatedFields, values)
      // const governanceTotal = [
      //   formik?.values?.initiation,
      //   formik?.values?.ldc_appointment,
      //   formik?.values?.design,
      //   formik?.values?.contractor_appointment,
      //   formik?.values?.construction,
      //   formik?.values?.handover,
      // ]
      // const governanceTotalPercentage = governanceTotal.reduce(
      //   (acc: number, val: string) => acc + (parseFloat(val) || 0),
      //   0,
      // )
      // // Call onSubmit with the updated fields only
      // if (governanceTotalPercentage !== 100) {
      //   toast.error('The total sum of governance must be 100%')
      //   return
      // }
      return Promise.resolve(res)
    },
  })

  useEffect(() => {
    if (
      formik?.values?.initiation ||
      formik?.values?.ldc_procurement ||
      formik?.values?.design ||
      formik?.values?.contractor_procurement ||
      formik?.values?.construction ||
      formik?.values?.handover
    ) {
      formik.setValues({ ...formik.values })
    } else if (formik.values.owning_entity === 'Aldar Development') {
      formik.setValues({
        ...formik.values,
        initiation: 0,
        ldc_procurement: 0,
        design: 0,
        contractor_procurement: 0,
        construction: 100,
        handover: 0,
      })
    } else if (formik.values?.MasterProjectClassification?.project_classification) {
      switch (formik.values?.MasterProjectClassification?.project_classification) {
        case 'Design-Bid-Build':
          formik.setValues({
            ...formik.values,
            initiation: 2,
            ldc_procurement: 3,
            design: 8,
            contractor_procurement: 4,
            construction: 81,
            handover: 2,
          })
          break
        case 'Bid-Build':
          formik.setValues({
            ...formik.values,
            initiation: 2,
            ldc_procurement: 0,
            design: 0,
            contractor_procurement: 5,
            construction: 91,
            handover: 2,
          })
          break
        case 'Design':
          formik.setValues({
            ...formik.values,
            initiation: 5,
            ldc_procurement: 5,
            design: 90,
            contractor_procurement: 0,
            construction: 0,
            handover: 0,
          })
          break
        case 'Design & Build':
          formik.setValues({
            ...formik.values,
            initiation: 2,
            ldc_procurement: 0,
            design: 0,
            contractor_procurement: 6,
            construction: 90,
            handover: 2,
          })
          break
        default:
          break
      }
    }
  }, [formik.values.MasterProjectClassification?.project_classification])

  const totalSumOfGovernance = useMemo(() => {
    return calculateTotalGovernanceSum(formik?.values)
  }, [governance, formik?.values])

  const hasError = useMemo(() => totalSumOfGovernance !== 100, [totalSumOfGovernance])

  const handleCollapse = (collapse: string) => {
    if (collapsed.includes(collapse)) {
      setCollapsed(collapsed.filter((item) => item !== collapse))
    } else {
      setCollapsed([...collapsed, collapse])
    }
  }

  const isCollapsed = (collapse: string) => {
    return collapsed.includes(collapse)
  }

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const sections = [
    {
      permission: 'Summary-Project Details',
      title: 'Project Details',
      icon: <ProjectDetails />,
      updatedBy: project?.updated_by,
      lastUpdated: project?.last_updated,
      isCollapsed: isCollapsed('Project Details'),
      onToggle: () => handleCollapse('Project Details'),
      content: <ProjectDetailsCollapse summaryEdit={summaryEdit} formik={formik} />,
    },
    {
      permission: 'Summary-Project Management',
      title: 'Project Management',
      icon: <UserShieldIcon />,
      updatedBy: project?.project_management_updated_by,
      lastUpdated: project?.project_management_last_updated,
      isCollapsed: isCollapsed('Project Management'),
      onToggle: () => handleCollapse('Project Management'),
      content: (
        <ProjectManagementCollapse
          project={project}
          summaryEdit={summaryEdit}
          formik={formik}
          refetchProject={refetchProject}
          isProjectLoading={isProjectLoading}
        />
      ),
    },
    {
      permission: 'Summary-Health And Safety',
      title: 'Health & Safety',
      icon: <MedicalKitIcon />,
      updatedBy: project?.health_safety_updated_by,
      lastUpdated: project?.health_safety_last_updated,
      isCollapsed: isCollapsed('Health & Safety'),
      onToggle: () => handleCollapse('Health & Safety'),
      content: <HealthSafetyCollapse summaryEdit={summaryEdit} formik={formik} />,
    },
    {
      permission: 'Summary-Sustainability',
      title: 'Sustainability',
      icon: <ArchiveCheckIcon />,
      updatedBy: sustainability?.updated_by,
      lastUpdated: sustainability?.last_updated,
      isCollapsed: isCollapsed('Sustainability'),
      onToggle: () => handleCollapse('Sustainability'),
      content: <SustainabilityCollapse summaryEdit={summaryEdit} formik={formik} />,
    },
    {
      permission: 'Governance',
      title: 'Governance',
      icon: <GovernanceIcon />,
      updatedBy: governance?.updated_by,
      lastUpdated: governance?.last_updated,
      isCollapsed: isCollapsed('Governance'),
      onToggle: () => handleCollapse('Governance'),
      hasError,
      content: <GovernanceCollapse summaryEdit={summaryEdit} formik={formik} />,
    },
  ]

  return (
    <div className={styles.collapse}>
      {loader || isLoading || isProjectLoading ? (
        <Loader />
      ) : (
        <>
          <div className={`${styles.actionHeader} ${!expanded ? `${styles.expandedHeader}` : ``}`}>
            <PulseActionButtons
              edit={summaryEdit}
              onEdit={() => {
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                  return
                }
                setSummaryEdit(!summaryEdit)
              }}
              onDiscard={() => setIsDiscard(true)}
              onSave={() => formik.handleSubmit()}
              editDisable={summaryEdit && formik.dirty}
              discardDisable={!summaryEdit}
              saveDisable={!summaryEdit}
            />
          </div>

          <PulseModel
            style={{
              position: 'absolute' as 'absolute',
              maxWidth: '402px',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 'fitContent',
              bgcolor: 'background.paper',
              borderRadius: '12px',
              boxShadow: 24,
              pt: '20px',
              px: '20px',
              pb: '20px',
            }}
            open={isDiscard}
            closable={false}
            onClose={() => setIsDiscard(false)}
            content={
              <DiscardModel
                onClose={() => setIsDiscard(false)}
                onDiscard={() => {
                  formik.resetForm()
                  setSummaryEdit(false)
                }}
              />
            }
          />
          <div className={styles.container}>
            {sections.map((section, index) =>
              currentUser.role?.view_permissions.includes(section.permission) ? (
                <CollapsibleSection
                  key={index}
                  edit={summaryEdit}
                  title={section.title}
                  icon={section.icon}
                  isCollapsed={section.isCollapsed}
                  toggleCollapse={section.onToggle}
                  lastUpdatedText={getLastUpdatedTime(section.lastUpdated)}
                  updatedBy={section.updatedBy}
                  hasError={section.hasError}
                  onChange={() => {
                    formik.setFieldValue(
                      'is_external_lookup_for_health_and_safety',
                      !formik.values.is_external_lookup_for_health_and_safety,
                    )
                  }}
                  checked={formik.values.is_external_lookup_for_health_and_safety}
                >
                  {section.content}
                </CollapsibleSection>
              ) : null,
            )}
          </div>
          <PulseModel
            closable={false}
            style={{ width: 'fitContent' }}
            open={validation?.open}
            onClose={() => setValidation({ ...validation, open: false })}
            content={
              <ValidationModel
                messages={validation?.message}
                onClose={() => setValidation({ ...validation, open: false })}
              />
            }
          />
        </>
      )}
    </div>
  )
}

export default SummaryTab
