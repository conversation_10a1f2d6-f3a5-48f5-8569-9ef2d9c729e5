@import '/styles/color.scss';

.card {
  padding: 5px 12px;
  background: $LIGHT;
  border-radius: 10px;
  min-height: 90px;
  min-width: 290px;
  border: 1px solid $LIGHT;
  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    .label {
      font-size: 12px;
      font-weight: 400;
      color: $BLACK;
    }
    .value {
      font-size: 24px;
      font-weight: 600;
      color: $DARK;
    }
    .emptyValue {
      font-size: 24px;
      font-weight: 600;
      color: $DARK;
      text-align: center;
      position: absolute;
      left: 39%;
    }
    .innerContent {
      display: flex;
      align-items: center;
      gap: 6px;
      justify-content: space-between;
    }
    .cardImage {
      // position: absolute;
      // bottom: -5px;
      // right: 0;
      display: block;
      margin-left: auto;
    }
  }
}
.textField {
  > div {
    height: 56px !important;
  }
  > div {
    width: 186px;
    > div > input {
      padding: 0px !important;
      color: $DARK;
      background-color: #f0f8ff;
      font-size: 24px !important;
      font-weight: 600 !important;
      line-height: 56px !important;
    }
  }
}
