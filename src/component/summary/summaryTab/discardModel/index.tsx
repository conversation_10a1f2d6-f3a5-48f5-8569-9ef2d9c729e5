import React from 'react'
import { Box, Modal, Typography } from '@mui/material'
import Image from 'next/image'
import styles from './DiscardModel.module.scss'
import aldar<PERSON>ogo from '../../../../../public/svg/aldarLogo.svg'
import { IDiscardModel } from '../interface'
import Button from '@/src/component/shared/button'
import TypographyField from '@/src/component/shared/typography'
import CloseCircleIcon from '@/src/component/svgImages/closeCircleIcon'
import CorrectIcon from '@/src/component/svgImages/correctIcon'
import EditIcon from '@/src/component/svgImages/editIcon'

const DiscardModel = ({ onClose, onDiscard }: IDiscardModel) => {
  return (
    <>
      <div className={styles.content}>
        <div className={styles.discardHeader}>
          <div>
            <TypographyField variant="body1" text={'Are you sure you want to discard all changes?'} />
          </div>
          <CloseCircleIcon className={styles.cursorPointer} onClick={() => onClose()} />
        </div>
        <div className={styles.buttons}>
          <Button color="secondary" startIcon={<EditIcon />} onClick={() => onClose()}>
            Keep Editing
          </Button>
          <Button
            startIcon={<CorrectIcon />}
            onClick={() => {
              onDiscard()
              onClose()
            }}
          >
            Confirm
          </Button>
        </div>
      </div>
    </>
  )
}

export default DiscardModel
