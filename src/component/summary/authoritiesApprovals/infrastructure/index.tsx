import React, { useMemo } from 'react'
import { format, parseISO } from 'date-fns'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './Infrastructure.module.scss'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import DatePicker from '@/src/component/shared/dataPicker'
import TextInputField from '@/src/component/shared/textInputField'
import CheckIcon from '@/src/component/svgImages/checkIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import { showCustomToast } from '@/src/component/toast/ToastManager'

import useAuthorityApproval from '@/src/redux/authorityApproval/useAuthorityApproval'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'
import { convertDDMMYYYYToLongDate, DDMMYYYYformate, payloadDateFormate } from '@/src/utils/dateUtils'

interface AddCommercialProps {
  onClose: () => void
  editId: any
  phase: string
  drawerContent: string
}

const NUMBER_INPUT_STYLE = {
  '& .Mui-focused': {
    background: '#f4f4fc',
    // border: '1px solid #2333C2BF',
  },
}

const infrastructureComponents = [
  'Earthwork',
  'Road Works',
  'Sewer Network',
  'Storm water network',
  'Subsurface Drainage',
  'Potable Water',
  'Irrigation',
  'Recycled Water',
  'LV Network',
  'MV Network',
  'FOC Network',
  'Telecom Network',
  'Street light Network',
  'Gas Network',
  'MCC Network',
]

const Infrastructure: React.FC<AddCommercialProps> = ({ onClose, editId, phase, drawerContent }) => {
  const { currentPeriod } = useMasterPeriod()
  const isEditMode = useMemo(() => !!editId, [editId])
  const router = useRouter()
  const { authorities, updateAuthorityApprovalApi, addAuthorityApprovalApi } = useAuthorityApproval()

  const authority: any = authorities.find((item) => {
    return item.id === editId
  })
  const initialValues = useMemo(() => {
    return {
      phase: authority?.lookup_project_to_phase_id || phase || '',
      type: drawerContent || '',
      description: authority?.description || '',
      design_enoc_forecast_date: authority?.design_enoc_forecast_date
        ? DDMMYYYYformate(authority?.design_enoc_forecast_date)
        : null,
      design_enoc_status: authority?.design_enoc_status || '',
      tpd_forecast_date: authority?.tpd_forecast_date ? DDMMYYYYformate(authority?.tpd_forecast_date) : null,
      tpd_status: authority?.tpd_status || '',
      enoc_status: authority?.enoc_status || '',
      enoc_forecast_date: authority?.enoc_forecast_date ? DDMMYYYYformate(authority?.enoc_forecast_date) : null,
    }
  }, [authority])

  const handleSubmit = async (values: any) => {
    const payload = {
      ...values,
      enoc_forecast_date: payloadDateFormate(values?.enoc_forecast_date),
      design_enoc_forecast_date: payloadDateFormate(values?.design_enoc_forecast_date),
      tpd_forecast_date: payloadDateFormate(values?.tpd_forecast_date),
      type: drawerContent,
      period: currentPeriod,
      project_name: router.query.slug,
      phase: values.phase,
    }
    if (editId) {
      const res: Record<string, any> = await updateAuthorityApprovalApi({
        ...payload,
        id: editId,
      })
      if (!res.payload.success) {
        showCustomToast('Error', 'error')
        return
      }
      formik.resetForm()
    } else {
      const res: Record<string, any> = await addAuthorityApprovalApi(payload)
      if (!res.payload.success) {
        showCustomToast('Error', 'error')
        return
      }
    }
    formik.resetForm()
    onClose()
  }

  const formik = useFormik({
    initialValues: initialValues,
    enableReinitialize: true,
    onSubmit: (values: any) => {
      handleSubmit(values)
      formik.resetForm()
    },
  })

  const { values } = formik

  const controlsManagersOptions = useMemo(() => populateDropdownOptions(authorities, 'description'), [authorities])
  const availableOptions = infrastructureComponents.filter((item) => !controlsManagersOptions.includes(item))

  return (
    <div className={styles.container}>
      <form className={styles.form} onSubmit={formik.handleSubmit}>
        <div className={styles.header}>
          <div className={styles.headerTitle}>{isEditMode ? 'Edit Details' : 'Add Details'}</div>
          <div className={styles.actionButtons}>
            <Button startIcon={isEditMode ? <CheckIcon /> : <SaveIcon />} type="submit">
              {isEditMode ? 'Update' : 'Save'}
            </Button>
            <Button className={styles.closeButton} color="secondary" onClick={onClose}>
              X Close
            </Button>
          </div>
        </div>
        <div className={styles.content}>
          {/* <TextInputField
            name="type"
            labelText="Type"
            placeholder="Type something ..."
            value={values.type}
            className={styles.textArea}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            variant={"outlined"}
          /> */}

          <ComboBox
            options={availableOptions}
            labelText={'Description'}
            placeholder="Type of search..."
            value={
              formik.values.description
                ? {
                    label: formik.values.description,
                    value: formik.values.description,
                  }
                : null
            }
            clearIcon={true}
            onChange={(val) =>
              formik.setValues({
                ...formik.values,
                description: val?.value || '',
              })
            }
            disabled={editId ? true : false}
          />
          {drawerContent === 'Infrastructure' && (
            <>
              <DatePicker
                name="design_enoc_forecast_date"
                labelText="Design ENOC Forecast Date"
                placeholder="DD/MM/YY"
                value={convertDDMMYYYYToLongDate(formik.values.design_enoc_forecast_date) || null}
                onChange={(value) => {
                  if (value === 'Invalid date') {
                    formik.setFieldValue('design_enoc_forecast_date', null)
                    return null
                  }
                  const selectedDate = value && value.toISOString()
                  const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                  formik.setFieldValue('design_enoc_forecast_date', date)
                }}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                name="design_enoc_status"
                labelText="Design ENOC Status"
                placeholder="Type something ..."
                value={values.design_enoc_status}
                className={styles.textArea}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                variant={'outlined'}
              />
              <DatePicker
                name="tpd_forecast_date"
                labelText="TPD Forecast Date"
                placeholder="DD/MM/YY"
                value={convertDDMMYYYYToLongDate(formik.values.tpd_forecast_date) || null}
                onChange={(value) => {
                  const selectedDate = value && value.toISOString()
                  const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                  formik.setFieldValue('tpd_forecast_date', date)
                }}
                onBlur={formik.handleBlur}
              />
              <TextInputField
                name="tpd_status"
                labelText="TPD Status"
                placeholder="Type something ..."
                value={values.tpd_status}
                className={styles.textArea}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                variant={'outlined'}
              />
            </>
          )}
          {drawerContent === 'Buildings' && (
            <>
              <TextInputField
                name="enoc_status"
                labelText="ENOC Status"
                placeholder="Type something ..."
                value={values.enoc_status}
                className={styles.textArea}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                variant={'outlined'}
              />
              <DatePicker
                name="enoc_forecast_date"
                labelText="ENOC Forecast Date"
                placeholder="DD/MM/YY"
                value={convertDDMMYYYYToLongDate(formik.values.enoc_forecast_date) || null}
                onChange={(value) => {
                  const selectedDate = value && value.toISOString()
                  const date = selectedDate && format(parseISO(selectedDate), 'dd-MM-yyyy')
                  formik.setFieldValue('enoc_forecast_date', date)
                }}
                onBlur={formik.handleBlur}
              />
            </>
          )}
        </div>
      </form>
    </div>
  )
}

export default Infrastructure
