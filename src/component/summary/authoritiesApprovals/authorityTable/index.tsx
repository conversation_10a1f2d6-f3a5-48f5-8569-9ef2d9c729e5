import React, { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import styles from './AuthorityTable.module.scss'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import DragCell from '@/src/component/customCells/dragCell'
import Loader from '@/src/component/shared/loader'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import TypographyField from '@/src/component/shared/typography'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import useAuthorityApproval from '@/src/redux/authorityApproval/useAuthorityApproval'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { ILookupProjectToPhase } from '@/src/redux/status/interface'
import { canEditUser } from '@/src/utils/userUtils'

const AuthorityTable = ({
  phase,
  setPhase,
  handleEdit,
  handleDelete,
}: {
  phase: ILookupProjectToPhase | null
  setPhase: (phase: ILookupProjectToPhase | null) => void
  handleEdit: (id: string, type: string) => void
  handleDelete: (id: any, type: string) => void
}) => {
  const router = useRouter()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { getAuthorityApprovalApi, authorities, deleteAuthorityApprovalApi } = useAuthorityApproval()
  const [loader, setLoader] = useState<boolean>(false)
  const [deleteModel, setDeleteModel] = useState<{
    id: any
    type: string
  } | null>(null)
  const [isAllDelete, setIsAllDelete] = useState<string | null>(null)

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const filteredInfrastructureData: any = authorities.filter((item: any) => {
    return (
      item.LookupProjectToPhase?.id === phase?.id &&
      item.project_name === router.query.slug &&
      item.type === 'Infrastructure'
    )
  })

  const filteredBuildingData: any = authorities.filter((item: any) => {
    return (
      item.LookupProjectToPhase?.id === phase?.id &&
      item.project_name === router.query.slug &&
      item.type === 'Buildings'
    )
  })

  const infrastructureColumns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'dragCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'center',
    },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }) => (
        <>
          <div className={styles.deleteIcon}>
            <EditIcon
              onClick={() => {
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  // handleEdit(row?.id, 'Infrastructure')
                }
              }}
              className={styles.icon}
            />
            <DeleteIcon
              onClick={() => {
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  // setDeleteModel({ id: row?.id, type: 'Infrastructure' })
                }
              }}
              className={styles.icon}
            />
          </div>
        </>
      ),
      size: 70,
    },
    { accessorKey: 'description', header: 'Description', flex: 1 },
    {
      accessorKey: 'design_enoc_forecast_date',
      header: 'Enoc Forecast Date',
      size: 200,
      // // flex: 1,
    },
    { accessorKey: 'design_enoc_status', header: 'Enoc Status', size: 200 },
    {
      accessorKey: 'tpd_forecast_date',
      header: 'Forecast Date',
      size: 150,
      // flex: 1,
    },
    {
      accessorKey: 'tpd_status',
      header: 'Tpd Status',
      size: 150,
      // flex: 1,
    },
  ]

  const buildingColumns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'dragCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'center',
    },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }) => {
        return (
          <>
            {isEditForUser && (
              <div className={styles.deleteIcon}>
                <EditIcon
                  onClick={() => {
                    if (!isEditForUser) {
                      toast(`The current reporting period is locked`, {
                        icon: <WarningAmberOutlined />,
                      })
                    } else {
                      // handleEdit(row?.id, 'Buildings')
                      // setPhase(row?.original?.phase)
                    }
                  }}
                  className={styles.icon}
                />
                <DeleteIcon
                  onClick={() => {
                    if (!isEditForUser) {
                      toast(`The current reporting period is locked`, {
                        icon: <WarningAmberOutlined />,
                      })
                    } else {
                      // setDeleteModel({ id: row?.id, type: 'Buildings' })
                    }
                  }}
                  className={styles.icon}
                />
              </div>
            )}
          </>
        )
      },
      size: 70,
    },
    { accessorKey: 'description', header: 'Description', flex: 1 },
    {
      accessorKey: 'enoc_status',
      header: 'Enoc Status',
      size: 200,
      // flex: 1,
    },
    {
      accessorKey: 'enoc_forecast_date',
      header: 'Enoc Forecast Date',
      size: 200,
    },
  ]

  const handleDeleteInfra = async () => {
    if (isEditForUser) {
      setLoader(true)
      try {
        await Promise.all(filteredInfrastructureData.map((item: any) => deleteAuthorityApprovalApi(item.id as number)))
        await getAuthorityApprovalApi({ period: currentPeriod })
      } catch (error) {
        console.error('Error deleting infrastructure data:', error)
      } finally {
        setLoader(false)
        setDeleteModel(null)
      }
    }
    setDeleteModel(null)
  }

  const handleDeleteBuilding = async () => {
    if (isEditForUser) {
      setLoader(true)
      try {
        await Promise.all(filteredBuildingData.map((item: any) => deleteAuthorityApprovalApi(item.id as number)))
        await getAuthorityApprovalApi({ period: currentPeriod })
      } catch (error) {
        console.error('Error deleting building data:', error)
      } finally {
        setLoader(false)
        setDeleteModel(null)
      }
    }
    setDeleteModel(null)
  }

  return (
    <div>
      {loader ? (
        <Loader />
      ) : (
        <>
          {filteredInfrastructureData.length >= 1 && (
            <div>
              <div className={styles.titleHeader}>
                <TypographyField text={'Infrastructure'} />
                {isEditForUser && (
                  <button
                    className={`${styles.deleteTable}`}
                    onClick={() => {
                      setIsAllDelete('Infrastructure')
                    }}
                    disabled={true}
                  >
                    Delete <DeleteIcon fill="#ffffff" />
                  </button>
                )}
              </div>
              <div className={styles.tableDivide}>
                <TanStackTable rows={filteredInfrastructureData} columns={infrastructureColumns} />
              </div>
            </div>
          )}
          <div className={styles.divide}>
            {filteredBuildingData.length >= 1 && (
              <div>
                <div className={styles.titleHeader}>
                  <TypographyField text={'Building'} />
                  {isEditForUser && (
                    <button
                      className={`${styles.deleteTable}`}
                      onClick={() => setIsAllDelete('Building')}
                      disabled={true}
                    >
                      Delete <DeleteIcon fill="#ffffff" />
                    </button>
                  )}
                </div>
                <div className={styles.tableDivide}>
                  <TanStackTable rows={filteredBuildingData} columns={buildingColumns} />
                </div>
              </div>
            )}
          </div>
        </>
      )}
      {deleteModel && (
        <ConfirmDeleteModal
          open={Boolean(deleteModel)}
          onClose={() => setDeleteModel(null)}
          handleConfirm={() => {
            handleDelete(deleteModel.id as any, deleteModel.type)
            setDeleteModel(null)
          }}
        />
      )}
      <ConfirmDeleteModal
        open={Boolean(isAllDelete)}
        onClose={() => setIsAllDelete(null)}
        handleConfirm={() => {
          if (isAllDelete === 'Infrastructure') {
            handleDeleteInfra()
          } else {
            handleDeleteBuilding()
          }
          setIsAllDelete(null)
        }}
      />
    </div>
  )
}

export default AuthorityTable
