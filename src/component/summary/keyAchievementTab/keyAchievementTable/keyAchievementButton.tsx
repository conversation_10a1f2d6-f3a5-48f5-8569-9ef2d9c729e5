import { WarningAmberOutlined } from '@mui/icons-material'
import { toast } from 'sonner'
import styles from './KeyAchievementTable.module.scss'
import Button from '@/src/component/shared/button'

export interface KeyAchievementButtonProps {
  setIsOpenDrawer: React.Dispatch<React.SetStateAction<boolean>>
  isEditForUser: boolean
  // setSelectedCommercials: React.Dispatch<React.SetStateAction<ICommercial | null>>
  className?: string
  startIcon?: React.ReactNode
  buttonColor?: 'primary' | 'inherit' | 'secondary' | 'success' | 'error' | 'info' | 'warning'
}

const KeyAchievementButton: React.FC<KeyAchievementButtonProps> = ({
  setIsOpenDrawer,
  isEditForUser = true,
  className,
  startIcon,
  buttonColor,
}) => {
  const handleButtonClick = () => {
    if (!isEditForUser) {
      return toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    }
    setIsOpenDrawer(true)
  }

  return (
    <Button
      startIcon={startIcon}
      onClick={handleButtonClick}
      className={`${styles.addCommercialBtn} ${className}`}
      color={buttonColor ? buttonColor : 'primary'}
    >
      Add Key Achievements
    </Button>
  )
}

export default KeyAchievementButton
