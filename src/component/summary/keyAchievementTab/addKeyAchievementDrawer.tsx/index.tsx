import React, { useEffect, useMemo, useState } from 'react'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './AddKeyAchievementDrawer.module.scss'
import { useDropdownOption } from './useDropdownOption'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import CustomComboBox from '@/src/component/shared/combobox/combobox'
import PhaseMultiChip from '@/src/component/shared/phaseMultiChip'
import Textarea from '@/src/component/shared/textArea'
import CheckIcon from '@/src/component/svgImages/checkIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import { createPostAndUpdateKeyAchievementPayload } from '@/src/component/updateProgress/progressForm/keyAchievements/helper'
import { StageStatus, SubStage } from '@/src/redux/areaOfConcern/interface'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import { IKeyAchievement } from '@/src/redux/keyAchievement/interface'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IStatus } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import { IProjects } from '@/src/services/projects/interface'
import { getMultiValues, getValue } from '@/src/utils/arrayUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

interface AddCommercialProps {
  onClose: () => void
  editKeyAchievementIndex: number | null
  keyAchievement: IKeyAchievement[]
  project?: IProjects
  statuses?: IStatus[]
}

const AddKeyAchievementDrawer: React.FC<AddCommercialProps> = ({
  onClose,
  editKeyAchievementIndex,
  keyAchievement,
  project,
  statuses,
}) => {
  //react-hook
  const [disable, setDisable] = useState(false)
  const [isStatusChanged, setIsStatusChanged] = useState(false)
  //custom-hook
  const { currentUser } = useAuthorization()
  const { keyAchievements, getKeyAchievementsApi, addKeyAchievementsApi, updateKeyAchievementsApi } =
    useKeyAchievement()
  const { currentPeriod } = useMasterPeriod()

  const router = useRouter()

  const keyAchievementData: any = useMemo(() => {
    return keyAchievements.find((item: any) => item.id === editKeyAchievementIndex)
  }, [keyAchievements, editKeyAchievementIndex])

  const initialValues = useMemo(() => {
    return {
      key_achievements_last_period: (editKeyAchievementIndex && keyAchievementData.key_achievements_last_period) || '',
      plans_next_period: (editKeyAchievementIndex && keyAchievementData.plans_next_period) || '',
      lookup_project_to_phase_id: (editKeyAchievementIndex && keyAchievementData?.LookupProjectToPhase?.id) || '',
      stage_status:
        (editKeyAchievementIndex && keyAchievementData?.MasterProjectStageStatus?.project_stage_status) || '',
      sub_stage: (editKeyAchievementIndex && keyAchievementData?.MasterProjectSubStage?.project_sub_stage) || '',
      master_project_stage_status_id:
        (editKeyAchievementIndex && keyAchievementData.master_project_stage_status_id) || 0,
      master_project_sub_stage_id: (editKeyAchievementIndex && keyAchievementData.master_project_sub_stage_id) || 0,
    }
  }, [editKeyAchievementIndex, keyAchievementData])

  useEffect(() => {
    formik.resetForm({ values: initialValues })
  }, [initialValues])

  const onSubmit = async (values: any) => {
    setDisable(true)
    try {
      const todayDate = new Date()
      const payload: IKeyAchievement = createPostAndUpdateKeyAchievementPayload(
        values,
        project?.project_name,
        todayDate,
        null,
        currentPeriod,
      )

      let response: Record<string, any>

      if (editKeyAchievementIndex) {
        response = await updateKeyAchievementsApi({
          id: editKeyAchievementIndex,
          data: payload,
        })
      } else {
        const keyAchievementSort = keyAchievement
          ?.slice()
          .sort((a: any, b: any) => a.key_achievement_sorting_order - b.key_achievement_sorting_order)

        const sort = keyAchievementSort.length
          ? Number(keyAchievementSort[keyAchievementSort.length - 1].key_achievement_sorting_order) + 1
          : 1
        const newPayload: IKeyAchievement = createPostAndUpdateKeyAchievementPayload(
          values,
          project?.project_name,
          todayDate,
          sort,
          currentPeriod,
        )
        response = await addKeyAchievementsApi(newPayload)
      }

      if (response?.payload?.success) {
        if (currentPeriod) {
          await getKeyAchievementsApi({ period: currentPeriod })
        }
        formik.resetForm()
        onClose && onClose()
        successToast(response?.payload?.message || 'Key Achievement added successfully')
      } else {
        errorToast(response?.payload?.response?.data?.message || 'An unexpected error occurred. Please try again.')
      }
    } catch (error) {
      console.error('Error occurred:', error)
      errorToast('An unexpected error occurred. Please try again.')
    } finally {
      setDisable(false)
    }
  }

  const formik: any = useFormik({
    initialValues,
    enableReinitialize: true,
    onSubmit,
  })

  // Update form values and handle status change if `isStatusChanged` is true
  const handleStatusUpdate = (phaseStatusOptions: any[]) => {
    setIsStatusChanged(false)
  }

  const getPhaseStageAssociations = (records: any[]) => {
    const phaseStageMap = new Map()

    records.forEach((record: any) => {
      const phaseArray = record.phases // now an array of objects
      const stageStatusValue = record.stage_status

      const subStageValue =
        typeof record.sub_stage?.project_sub_stage === 'string' && record.sub_stage?.project_sub_stage.trim() !== ''
          ? record.sub_stage
          : null

      // Skip invalid stage_status
      if (
        !stageStatusValue ||
        typeof stageStatusValue?.project_stage_status !== 'string' ||
        stageStatusValue?.project_stage_status.trim() === ''
      ) {
        return
      }

      const stageInfo = {
        stageStatus: stageStatusValue,
        subStage: subStageValue,
      }

      const stageInfoString = JSON.stringify(stageInfo)

      // Process phase array of objects
      if (Array.isArray(phaseArray) && phaseArray.length > 0) {
        phaseArray.forEach((phaseObj: any) => {
          // if (!phaseObj?.phase) return // skip if missing phase name

          const phaseKey = JSON.stringify(phaseObj) // use entire object as key

          if (!phaseStageMap.has(phaseKey)) {
            phaseStageMap.set(phaseKey, new Set())
          }

          phaseStageMap.get(phaseKey).add(stageInfoString)
        })
      }
    })

    // Convert Map to desired result
    const result: Array<{ phases: any; stages: any[] }> = []
    for (const [phaseKey, stagesSet] of phaseStageMap.entries()) {
      result.push({
        phases: JSON.parse(phaseKey),
        stages: Array.from(stagesSet).map((s: any) => JSON.parse(s)),
      })
    }

    return result
  }

  const { phaseOptions, stageStatusOptions, subStageOptions, matchingPhasesItem } = useDropdownOption(
    statuses || [],
    formik.values.lookup_project_to_phase_id,
    currentUser,
  )

  console.log('sub_stage: ', subStageOptions)
  console.log('stage_status: ', stageStatusOptions)
  console.log('phases: ', phaseOptions)
  //   [
  //     {
  //         "label": "Multi-phase",
  //         "value": [
  //             18193,
  //             18194
  //         ],
  //         "tooltip": "Overall# test 11"
  //     },
  //     {
  //         "label": "Overall",
  //         "value": 18193,
  //         "tooltip": ""
  //     },
  //     {
  //         "label": "test 11",
  //         "value": 18194,
  //         "tooltip": ""
  //     }
  // ]

  return (
    <div className={styles.container}>
      <form className={styles.form} onSubmit={formik.handleSubmit}>
        <div className={styles.header}>
          <div className={styles.headerTitle}>
            {editKeyAchievementIndex ? 'Edit Key Achievement' : 'Add Key Achievement'}
          </div>
          <div className={styles.actionButtons}>
            {/* disabled={!formik.dirty || buttonDisable} */}
            <Button
              type="submit"
              disabled={
                disable ||
                !formik.dirty ||
                formik.isSubmitting ||
                !formik.values.key_achievements_last_period ||
                !formik.values.plans_next_period ||
                !formik.values.master_project_stage_status_id
              }
              startIcon={editKeyAchievementIndex ? <CheckIcon /> : <SaveIcon />}
            >
              {editKeyAchievementIndex ? 'Update' : 'Save'}
            </Button>
            <Button
              className={styles.closeButton}
              color="secondary"
              onClick={() => {
                formik.resetForm()
                onClose()
              }}
            >
              X Close
            </Button>
          </div>
        </div>
        <div className={styles.content}>
          <PhaseMultiChip currentPhase={matchingPhasesItem} />
          <div className={styles.dropDownContainer}>
            <CustomComboBox
              options={phaseOptions}
              labelText={'Phase/Package'}
              placeholder="Type of search..."
              className={styles.comboBoxInput}
              // disabled={!edit}
              value={
                // formik.values?.phase
                //   ? {
                //       label: formik.values?.phase?.includes(MULTI_SELECT_SEPARATOR)
                //         ? formik.values?.phase?.split(MULTI_SELECT_SEPARATOR)?.length > 1
                //           ? 'Multi phases'
                formik.values.project_to_project_phase_ids
                  ? getMultiValues(phaseOptions, formik.values.project_to_project_phase_ids)
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  project_to_project_phase_ids: val?.value
                    ? Array.isArray(val?.value)
                      ? val.value
                      : [val.value]
                    : null,
                  stage_status: val?.label || null,
                  master_project_stage_status_id: val?.value || null,
                })
              }}
            />
            <ComboBox
              options={stageStatusOptions}
              labelText={'Stage Status *'}
              placeholder="Type of search..."
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              value={
                formik.values?.master_project_stage_status_id
                  ? !!getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)
                    ? getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)
                    : { label: formik.values?.stage_status, value: formik.values?.master_project_stage_status_id }
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  master_project_stage_status_id: val?.value || null,
                  master_project_sub_stage_id: null,
                })
              }}
              isCustomSorting={true}
            />
          </div>
          {(getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)?.label ||
            formik.values?.stage_status) === 'Design' && (
            <ComboBox
              options={subStageOptions}
              labelText={'Sub Stage'}
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              placeholder="Type of search..."
              value={
                formik.values?.master_project_sub_stage_id
                  ? !!getValue(subStageOptions, formik.values?.master_project_sub_stage_id)
                    ? getValue(subStageOptions, formik.values?.master_project_sub_stage_id)
                    : { label: formik.values?.sub_stage, value: formik.values?.master_project_sub_stage_id }
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  master_project_sub_stage_id: val?.value || '',
                })
              }}
              isCustomSorting={true}
            />
          )}
          <div>
            <Textarea
              name="key_achievements_last_period"
              placeholder="Type Something..."
              labelText={'Key  Achievements (Last Period) *'}
              onChange={formik.handleChange}
              value={formik.values.key_achievements_last_period}
              onBlur={formik.handleBlur}
            />
            <Textarea
              name="plans_next_period"
              placeholder="Type Something..."
              labelText={'Plans (Next Period) *'}
              onChange={formik.handleChange}
              value={formik.values?.plans_next_period}
              onBlur={formik.handleBlur}
            />
          </div>
        </div>
      </form>
    </div>
  )
}

export default AddKeyAchievementDrawer
