import { useMemo } from 'react'
import { generateMultiSelectOptionsFromPhases } from '@/src/component/updateProgress/helper'
import { IStatus } from '@/src/redux/status/interface'
import {
  populateDropdownOptions,
  prepareDropdownOptionsFromObject,
  sortArrayByKeyWithTypeConversion,
  getUniqueArrayByKey,
  setMultiSelectedValueToWildCard,
} from '@/src/utils/arrayUtils'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'

export const useDropdownOption = (statuses: IStatus[], phaseIds: number[], currentUser: any) => {
  return useMemo(() => {
    const currentPhaseIDs = phaseIds?.length > 0 ? setMultiSelectedValueToWildCard(phaseIds) : ''
    const modifyRecord = statuses.map((item) => ({
      ...item,
      phaseIDs:
        item?.LookupProjectToPhase && item?.LookupProjectToPhase?.length > 0
          ? setMultiSelectedValueToWildCard(item.LookupProjectToPhase.map((phase) => phase.id))
          : '',
    }))

    const validRecord = modifyRecord.filter((item) =>
      currentPhaseIDs === null || currentPhaseIDs === '' || !currentPhaseIDs
        ? item?.phaseIDs === '' || item?.phaseIDs === null
        : item.phaseIDs === currentPhaseIDs,
    )

    const statusesByPermission = statuses.filter((item: IStatus) =>
      getStageStatusByPermission(currentUser.role).includes(item.stage_status),
    )
    const sortedStatusesByPermission = sortArrayByKeyWithTypeConversion(
      statusesByPermission.map((status) => ({
        ...status,
        project_status_sorting_order: Number(status.project_status_sorting_order),
      })),
      'project_status_sorting_order',
      true,
    )

    const phases = populateDropdownOptions(sortedStatusesByPermission, 'LookupProjectToPhase')
    const uniquePhases = generateMultiSelectOptionsFromPhases(phases)?.map((item) => ({
      ...item,
      strPhaseIDs: setMultiSelectedValueToWildCard(Array.isArray(item.value) ? item.value : [item.value]),
    })) //* Options with include no-phase
    const sorted = sortArrayByKeyWithTypeConversion(validRecord, 'project_status_sorting_order')

    const stage_status_option = getUniqueArrayByKey(
      prepareDropdownOptionsFromObject(validRecord, 'MasterProjectStageStatus', 'project_stage_status'),
      'label',
    ) //* Options with include no-phase

    const stageStatusOptions = currentPhaseIDs ? [stage_status_option] : [] //* Set stage Stage null when there is no phase
    const phaseOptions = currentPhaseIDs ? [uniquePhases] : [] //* Set stage Stage null when there is no phase

    const sub_stage = getUniqueArrayByKey(
      [...prepareDropdownOptionsFromObject(sorted, 'MasterProjectSubStage', 'project_sub_stage')],
      'label',
    )

    // const matchingPhases = phaseOptions.filter((item) => {
    //   const phaseId = formik.values.lookup_project_to_phase_id
    //   // If lookup_project_to_phase_id is an array
    //   if (Array.isArray(phaseId)) {
    //     if (phaseId.length === 1) {
    //       // Single value in array: match primitive value
    //       return item.value === phaseId[0]
    //     } else if (phaseId.length > 1 && Array.isArray(item.value)) {
    //       // Multiple values: match array regardless of order, no sorting
    //       const setA = new Set(item.value)
    //       const setB = new Set(phaseId)
    //       if (setA.size !== setB.size) return false
    //       for (const v of setA) {
    //         if (!setB.has(v)) return false
    //       }
    //       return true
    //     }
    //     // If item.value is not array, skip
    //     return false
    //   }
    //   // If lookup_project_to_phase_id is primitive, match primitive value
    //   return item.value === phaseId
    // })

    const matchingPhasesItem = uniquePhases.find((item) => item.strPhaseIDs === currentPhaseIDs)

    return {
      phaseOptions: uniquePhases,
      stageStatusOptions,
      subStageOptions: sub_stage,
      matchingPhasesItem,
    }
  }, [statuses, phaseIds, currentUser?.role])
}
