import React, { useState } from 'react'
import styles from './dataDateModal.module.scss'
import Button from '@/src/component/shared/button'
import DatePicker from '@/src/component/shared/dataPicker'
import PulseModel from '@/src/component/shared/pulseModel'

interface IDataDateModal {
  open: boolean
  onClose: () => void
  handleConfirm: (date: any) => void
  cutOffSelectedDate: string
}

const DataDateModal: React.FC<IDataDateModal> = ({ open, onClose, handleConfirm, cutOffSelectedDate }) => {
  const [selectedDataDate, setSelectedDataDate] = useState(cutOffSelectedDate ? cutOffSelectedDate : new Date())

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '400px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    pt: '20px',
    px: '20px',
    pb: '10px',
    zIndex: 1,
    maxHeight: '80vh', // New: Limit to 80% of viewport height for small screens
    overflowY: 'auto' as 'auto', // New: Enable vertical scrolling if content overflows
  }

  return (
    <PulseModel
      style={style}
      open={open}
      closable={false}
      onClose={onClose}
      content={
        <div className={styles.confirmButtonContent}>
          <div className={styles.confirmContent}>Data Date</div>
          <DatePicker
            name="actual_forecast_milestone_completion"
            labelText="Please select date"
            placeholder="DD-MM-YY"
            value={selectedDataDate || null}
            onChange={(date) => setSelectedDataDate(date)}
            slotProps={{
              popper: {
                placement: 'right',
              },
            }}
          />
          <div className={styles.reassignButtons}>
            <Button onClick={() => handleConfirm(selectedDataDate)} disabled={!selectedDataDate}>
              Add
            </Button>
            <Button color="secondary" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </div>
      }
    />
  )
}

export default DataDateModal
