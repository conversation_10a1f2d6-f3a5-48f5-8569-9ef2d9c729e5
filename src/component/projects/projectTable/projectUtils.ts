import { getYear, parse } from 'date-fns'
import { IProjectTableRecord } from './interface'
import { isWithinLast7DaysInArray } from '../../summary/statusTab/statusService'
import { IProjects, ISortListOfProject } from '@/src/services/projects/interface'
import { arrayToStringForTable, getUniqueValuesFromArray } from '@/src/utils/arrayUtils'

export const getMaxValue = (a: number, b: number): number => Math.max(a, b)
export const getMinValue = (a: number, b: number): number => Math.min(a, b)
export const isBetween = (value: number, min: number, max: number): boolean => value >= min && value <= max

export const findItemById = <T extends { id: string }>(items: T[], id: string): T | undefined =>
  items.find((item) => item.id == id)

export const filterProjectsByEntity = (projects: IProjects[], owningEntity: string): IProjects[] =>
  projects.filter((project) => project.owning_entity === owningEntity)

export const preparePayloadForSortOrder = (
  projects: IProjects[],
  dragOrder: number,
  dropOrder: number,
): ISortListOfProject[] => {
  const reorderSortingArray = (array: IProjects[], fromIndex: number, toIndex: number): IProjects[] => {
    if (fromIndex < 0 || fromIndex >= array.length || toIndex < 0 || toIndex >= array.length) {
      return array // Return the original array if indices are out of bounds
    }

    const result = [...array]
    const [movedItem] = result.splice(fromIndex, 1) // Remove the dragged item from its original position
    result.splice(toIndex, 0, movedItem) // Insert the dragged item at the new position

    return result
  }

  // Find indices of the projects to drag and drop
  const fromIndex = projects.findIndex((project) => Number(project.project_sorting_order) === dragOrder)
  const toIndex = projects.findIndex((project) => Number(project.project_sorting_order) === dropOrder)

  // Reorder projects array
  const orderedProjects = reorderSortingArray(projects, fromIndex, toIndex)

  // Filter projects that are within the range of drag and drop orders
  const maxOrder = Math.max(dragOrder, dropOrder)
  const minOrder = Math.min(dragOrder, dropOrder)

  const filteredProjects = orderedProjects.filter((project) => {
    const currentOrder = Number(project.project_sorting_order)
    return currentOrder >= minOrder && currentOrder <= maxOrder
  })

  // Create payload with updated sorting order
  const payload: ISortListOfProject[] = filteredProjects.map((project, index) => {
    // Ensure the order is unique and incremental
    const newOrder = minOrder + index
    return {
      project_name: project.project_name as string,
      project_sorting_order: newOrder,
    }
  })

  return payload
}

export const getUniqueValuesOfProjects = (projectData: any) => {
  const keysToProcess = [
    'owningEntity',
    'project_classification',
    'project_status',
    'project_type',
    'location',
    'design_project_manager',
    'portfolio_manager',
    'controls_manager',
    'delivery_manager',
    'procurement_manager',
  ]

  const uniqueValues: Record<string, any[]> = {}

  keysToProcess.forEach((key) => {
    uniqueValues[key] = getUniqueValuesFromArray(projectData.map((item: any) => item?.[key])).filter(
      (item: any) => item?.length > 0,
    )
  })

  return uniqueValues
}

export const convertToProjectTableRecord = (
  projects: IProjects[],
  // lastUpdateOfProgress: any,
  // statuses: any,
): IProjectTableRecord[] => {
  return projects?.map((project: IProjects, index: number): IProjectTableRecord => {
    // const projectIs7Day = isWithinLast7DaysInArray([
    //   project?.last_updated as any,
    //   project?.health_safety_last_updated,
    //   project?.project_management_last_updated,
    // ])

    // const isProjectProgressAvailable: any = lastUpdateOfProgress
    //   ?.filter((progress: any) => {
    //     return progress.projectName === project.project_name
    //   })
    //   .find((item: any) => {
    //     return item.is7Days
    //   })

    // const statusOfProject = statuses.filter((item: any) => {
    //   return item.project_name === project.project_name
    // })

    // const designManagerValue = statusOfProject.map((item: any) => {
    //   return item.design_manager
    // })

    return {
      cutoff_date: project.cutoff_date ?? '',
      project: project.project_name ?? '',
      project_name: project.project_name ?? '',
      // entity_category: project.MasterEntityCategory?.entity_category ?? '',
      owningEntity: project.owning_entity ?? '',
      dof_number: project.dof_number ?? '',
      project_type: project.project_type ?? '',
      project_status: project.project_status ?? '',
      project_classification: project.MasterProjectClassification?.project_classification ?? '',
      location:
        project.Locations && project?.Locations?.length > 0
          ? arrayToStringForTable(project?.Locations, 'location')
          : '',
      Sub_location:
        project?.SubLocations && project?.SubLocations?.length > 0
          ? arrayToStringForTable(project?.SubLocations, 'sub_location')
          : '', // Join array into a single string separated by commas,
      district: project.MasterDistrict?.district ?? '',
      district_weight: project.district_weight ? project.district_weight.toString() : '',
      controls_manager: project.MasterControlManager?.control_manager ?? '',
      delivery_manager:
        project.ExecutiveDirectors && project?.ExecutiveDirectors?.length > 0
          ? arrayToStringForTable(project?.ExecutiveDirectors, 'executive_director')
          : '',
      procurement_manager:
        project?.ProcurementManagers && project?.ProcurementManagers?.length > 0
          ? arrayToStringForTable(project?.ProcurementManagers, 'procurement_manager')
          : '',
      design_project_manager:
        project.DesignProjectManagers && project?.DesignProjectManagers?.length > 0
          ? arrayToStringForTable(project?.DesignProjectManagers, 'design_manager')
          : '',
      delivery_project_manager:
        project.DeliveryProjectManagers && project?.DeliveryProjectManagers?.length > 0
          ? arrayToStringForTable(project?.DeliveryProjectManagers, 'delivery_project_manager')
          : '',
      design_executive_director:
        project.DesignExecutiveDirectors && project?.DesignExecutiveDirectors?.length > 0
          ? arrayToStringForTable(project?.DesignExecutiveDirectors, 'design_executive_director')
          : '',
      portfolio_manager: project.MasterPortfolioManager?.portfolio_manager ?? '',
      project_id: project?.project_id ?? '',
      cpds_project_id: project?.cpds_project_id ?? '',
      director: project?.MasterDirector?.director?.trim() ?? '',
      id: index + 1,
      // designMangerByStatus: [...new Set(designManagerValue)].filter(Boolean).join(', '),
      sortNumber: project.project_sorting_order ? Number(project.project_sorting_order) : 0,
      last_updated: project.last_updated ?? '',
      is_refurbishment_project: project.is_refurbishment_project ?? false,
      is_musanada_project: project.is_musanada_project ?? false,
      budget_uplift_submitted: project.budget_uplift_submitted ?? false,
      scope_change: project.scope_change ?? false,
      is_subject_to_rebaseline: Boolean(project.is_subject_to_rebaseline),
      tip_submitted: project.tip_submitted ?? false,
      eot_submitted: project.eot_submitted ?? false,
      potential_budget_uplift_submitted: project.potential_budget_uplift_submitted ?? false,
      // isRibbon: isProjectProgressAvailable?.is7Days || projectIs7Day,
      isRibbon: project?.is7Days ?? false,
      startYear: project?.decree_date
        ? getYear(parse(project?.decree_date.toString(), 'yyyy-MM-dd', new Date())).toString()
        : '',
      executive_sorting_order: project?.executive_sorting_order ?? null,
      is_executive_project: project?.is_executive_project ?? false,
    }
  })
}
