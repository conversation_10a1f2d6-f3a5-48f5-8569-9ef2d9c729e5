// components/Toast.module.scss
.toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  border-radius: 4px;
  color: white;
  opacity: 0;
  z-index: 100000;
  animation:
    fadeIn 0.3s forwards,
    fadeOut 0.3s forwards 2.7s;

  &.success {
    background-color: green;
  }

  &.error {
    background-color: red;
  }

  &.info {
    background-color: blue;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
