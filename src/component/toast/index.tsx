// components/Toast.tsx
import React, { useEffect } from 'react'
import styles from './Toast.module.scss'

interface ToastProps {
  message: string
  type: 'success' | 'error' | 'info'
  duration?: number
  onClose: () => void
}

const Toast: React.FC<ToastProps> = ({ message, type, duration = 3000, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose()
    }, duration)

    return () => {
      clearTimeout(timer)
    }
  }, [duration, onClose])

  return <div className={`${styles.toast} ${styles[type]}`}>{message}</div>
}

export default Toast
