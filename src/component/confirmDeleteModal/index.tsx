import React from 'react'
import styles from './ConfirmDeleteModal.module.scss'
import PulseModel from '../shared/pulseModel'
import Button from '@/src/component/shared/button'

interface IConfirmDeleteModal {
  open: boolean
  onClose: () => void
  handleConfirm: () => void
  loading?: boolean
  message?: string | JSX.Element
}

const ConfirmDeleteModal: React.FC<IConfirmDeleteModal> = ({ open, onClose, handleConfirm, loading, message }) => {
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    // width: '200px',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    // boxShadow: 24,
    pt: '20px',
    px: '20px',
    pb: '10px',
    zIndex: 1,
  }

  return (
    <PulseModel
      style={style}
      open={open}
      closable={false}
      onClose={onClose}
      content={
        <div className={styles.confirmButtonContent}>
          <div className={styles.confirmContent}>{message || 'Are you sure want to delete?'}</div>
          <div className={styles.reassignButtons}>
            <Button onClick={handleConfirm} disabled={loading}>
              Yes
            </Button>
            <Button color="secondary" onClick={onClose}>
              No
            </Button>
          </div>
        </div>
      }
    />
  )
}

export default ConfirmDeleteModal
