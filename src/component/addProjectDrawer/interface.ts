import { DrawerStates } from '../home/<USER>'

export interface IAddProjectValue {
  project_name: string
  owning_entity: string
  project_pricing_type: string
  procurement_manager: string
  design_project_manager: string
  project_type: string
  portfolio_manager: string
  entity_category: string
  controls_manager: string
  location: string
  sub_location: string
  project_classification: string
  executive_director: string
  director: string
  delivery_project_manager: string
  design_executive_director: string
}

export interface IAddProjectInterface {
  onClose: () => void
  drawerStates: DrawerStates
  project?: any
  setProject?: any
}

export interface IProjectColumn {
  key: string
  label: string
  filterCell?: () => JSX.Element | undefined
  renderCell?: (value: any, row: Record<string, any>, rowIndex: number, isHover?: boolean) => JSX.Element | undefined
  cellStyle?: React.CSSProperties
  headerStyle?: React.CSSProperties
  isRabin?: boolean
}

export interface IProjectTableProps {
  columns: IProjectColumn[]
  data: Record<string, any>[]
}

export interface TableHeaderProps {
  columns: IProjectColumn[]
}

export interface TableBodyProps {
  data: Record<string, any>[]
  columns: IProjectColumn[]
}

export interface TableRowProps {
  row: Record<string, any>
  rowIndex: number
  columns: IProjectColumn[]
}

export interface TableCellProps {
  value: any
  column: IProjectColumn
  rowIndex: number
  row: Record<string, any>
  isHover?: boolean
}
