import { sortData } from '../shared/tanStackTable/helper'
import { IProjects } from '@/src/services/projects/interface'

export const getProjectTableData = (projects: IProjects[]) => {
  const array =
    projects?.map((res) => {
      return {
        ...res,
        portfolio_manager: res?.MasterPortfolioManager?.portfolio_manager || '',
        controls_manager: res?.MasterControlManager?.control_manager || '',
        design_executive_director:
          res?.DesignExecutiveDirectors?.map((item: any) => item?.design_executive_director).join(', ') || '',
        executive_director: res?.ExecutiveDirectors?.map((item: any) => item?.executive_director).join(', ') || '',
        project_classification: res?.MasterProjectClassification?.project_classification || '',
        director: res?.MasterDirector?.director || '',
        /*   design_project_owner: res?.design_project_owner
          ? res?.design_project_owner
              ?.split(';') // Split by semicolons
              .map((val: string) => val.replace(/\d+/g, '').trim())
              .filter((val) => val) // Remove empty strings
              .join(', ')
          : '',
        executive_director: res?.executive_director
          ? res.executive_director
              .split(';') // Split by semicolons
              .map((val: string) => val.replace(/\d+/g, '').trim())
              .filter((val) => val) // Remove empty strings
              .join(', ')
          : '', */
      }
    }) || []

  return sortData(array, 'project_sorting_order', true)
}
