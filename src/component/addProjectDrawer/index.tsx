import React, { useEffect, useMemo, useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { useQuery } from '@tanstack/react-query'
import { useFormik } from 'formik'
import { toast, Toaster } from 'sonner'
import styles from './AddProjectDrawer.module.scss'
import AddProjectForm from './addProjectForm'
import { getProjectTableData } from './helper'
import { IAddProjectInterface, IAddProjectValue } from './interface'
import ConfirmDeleteModal from '../confirmDeleteModal'
import Button from '../shared/button'
import Loader from '../shared/loader'
import Tabs, { ITabPanels } from '../shared/tabs'
import TanStackTable from '../shared/tanStackTable'
import { sortData } from '../shared/tanStackTable/helper'
import { CustomColumnDef } from '../shared/tanStackTable/interface'
import TypographyField from '../shared/typography'
import DeleteIcon from '../svgImages/deleteIcon'
import EditIcon from '../svgImages/editIcon'
import { showCustomToast } from '../toast/ToastManager'
import { LAPTOP, MOBILE, TABLET } from '@/src/constant/breakpoint'
import { ERROR_MESSAGE } from '@/src/constant/enum'
import { useBreakpoint } from '@/src/customeHook/useBreakPoint'
import {
  PROJECTS_QUERY_KEY,
  useAddProject,
  useDeleteProject,
  useUpdateProject,
  useUpdateProjectName,
} from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useGovernance from '@/src/redux/governance/useGovernance'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import { getProjects } from '@/src/services/projects'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

const tableHeights: Record<string, string> = {
  mobile: 'calc(100vh - 290px)',
  tablet: 'calc(100vh - 14.9375rem)',
  laptop: 'calc(100vh - 12.9375rem)',
  desktop: 'calc(100vh - 36.700rem)',
  wideScreen: 'calc(100vh - 36.25rem)',
}
const pageSizes: Record<string, number> = {
  mobile: 12,
  tablet: 12,
  laptop: 12,
  desktop: 8,
  wideScreen: 12,
}

const defaultFields = {
  project_name: '',
  owning_entity: '',
  project_type: '',
  project_pricing_type: '',
  design_executive_director: '',
  design_project_manager: '',
  // procurement_manager: '',
  portfolio_manager: '',
  controls_manager: '',
  executive_director: '',
  entity_category: '',
  project_classification: '',
  location: '',
  sub_location: '',
  delivery_project_manager: '',
  director: '',
}

const AddProjectDrawer: React.FC<IAddProjectInterface> = ({ onClose, drawerStates }) => {
  const { addProject } = drawerStates
  const [loader, setLoader] = useState(false)
  const [editRow, setEditRow] = useState<any | null>(null)
  const [addState, setAddSetTime] = useState(false)
  const [activeTab, setActiveTab] = useState<string>('Form')
  const [deleteModel, setDeleteModel] = useState<string | null>(null)
  const [initialValues, setInitialValues] = useState<any>(defaultFields)
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { governances, getGovernanceApi, addGovernanceApi, updateGovernanceApi } = useGovernance()
  const breakpoint = useBreakpoint()
  const { recentProject, setRecentProject } = useProjectSummary()
  const tableHeight = tableHeights[breakpoint] || '100%' // Default to full height
  const pageSize = pageSizes[breakpoint] || 10 // Default to full height
  const { currentUser } = useAuthorization()
  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )
  //REACT_QUERY
  const { mutate: renameProject } = useUpdateProjectName()
  const { mutate: addProjectRecord } = useAddProject()
  const { mutate: updateProjectRecord } = useUpdateProject()
  const { mutate: deleteProjectRecord } = useDeleteProject()
  const { data: projects, isLoading: isProjectsLoading } = useQuery({
    queryKey: [PROJECTS_QUERY_KEY],
    queryFn: () => getProjects({ period: currentPeriod }),
    enabled: addProject,
    select: (response) => response.data, // This extracts 'data' directly
  })

  // Fetch projects when addProject is true
  useEffect(() => {
    if (addProject) {
      getGovernanceApi({ period: currentPeriod })
    }
  }, [addProject, currentPeriod])

  const updateGovernance = async (projectClassification: any, projectName: any) => {
    const classificationValues: Record<
      any,
      {
        initiation: string
        ldc_procurement: string
        design: string
        contractor_procurement: string
        construction: string
        handover: string
      }
    > = {
      'Design-Bid-Build': {
        initiation: '2',
        ldc_procurement: '3',
        design: '8',
        contractor_procurement: '4',
        construction: '81',
        handover: '2',
      },
      'Bid-Build': {
        initiation: '2',
        ldc_procurement: '',
        design: '',
        contractor_procurement: '5',
        construction: '91',
        handover: '2',
      },
      Design: {
        initiation: '5',
        ldc_procurement: '5',
        design: '90',
        contractor_procurement: '',
        construction: '',
        handover: '',
      },
      'Design & Build': {
        initiation: '2',
        ldc_procurement: '',
        design: '',
        contractor_procurement: '6',
        construction: '90',
        handover: '2',
      },
    }
    const governance = governances.find((item) => {
      return item.project_name === projectName
    })
    const formatPercentage = (value: string) => Number(value) / 100
    const governanceBaseOnClassification = classificationValues[`${projectClassification?.trim()}`]
      ? classificationValues[`${projectClassification?.trim()}`]
      : {
          initiation: '',
          ldc_procurement: '',
          design: '',
          contractor_procurement: '',
          construction: '',
          handover: '',
        }
    const governancePayload: any = {
      period: currentPeriod,
      project_name: projectName,
      initiation: formatPercentage(governanceBaseOnClassification.initiation),
      ldc_procurement: formatPercentage(governanceBaseOnClassification.ldc_procurement),
      design: formatPercentage(governanceBaseOnClassification.design),
      contractor_procurement: formatPercentage(governanceBaseOnClassification.contractor_procurement),
      construction: formatPercentage(governanceBaseOnClassification.construction),
      handover: formatPercentage(governanceBaseOnClassification.handover),
    }
    if (governance) {
      governancePayload['last_updated'] = new Date().toISOString()
      const payload = {
        id: governance.id as string,
        ...governancePayload,
      }
      const responseOfSustainability: Record<string, any> = await updateGovernanceApi(payload)
      if (responseOfSustainability.payload.success === true) return true
    } else {
      const responseOfSustainability: Record<string, any> = await addGovernanceApi(governancePayload)
      if (responseOfSustainability.payload.success === true) return true
    }
  }

  const formik = useFormik<IAddProjectValue>({
    initialValues,
    enableReinitialize: true,
    onSubmit: (values: any) => {
      const updatedFields = Object.keys(values).reduce((acc: any, key: any) => {
        if (values[key] !== initialValues[key]) {
          acc[key] = values[key]
        }
        return acc
      }, {})
      if (!isEditForUser) {
        toast(`The current reporting period is locked`, {
          icon: <WarningAmberOutlined />,
        })
        return
      }
      handleSubmit({ ...updatedFields, project_name: values.project_name }, initialValues)
    },
  })

  const filteredProjectByEntity = useMemo(
    () => projects?.filter((item: any) => item?.owning_entity === formik.values.owning_entity),
    [projects, formik.values.owning_entity],
  )

  const onDiscard = () => {
    setEditRow(null)
    setInitialValues({
      project_name: '',
      owning_entity: '',
      project_type: '',
      project_pricing_type: '',
      design_executive_director: '',
      design_project_manager: '',
      // procurement_manager: '',
      portfolio_manager: '',
      controls_manager: '',
      executive_director: '',
      entity_category: '',
      project_classification: '',
      location: '',
      sub_location: '',
      delivery_project_manager: '',
      director: '',
    })
  }

  const handleSubmit = async (values: any, initialValues: any) => {
    // Helper function to remove unnecessary fields
    const removeFields = (data: any, fields: string[]) => {
      fields.forEach((field) => delete data[field])
      return data
    }

    // Prepare the data for submission
    const prepareData = (values: any) => {
      const data: any = {
        ...values,
        project_name: values?.project_name,
        last_updated: new Date().toISOString(),
        period: currentPeriod,
        section: 'normal',
      }

      // List of fields to be removed
      const fieldsToRemove = [
        'updated_by',
        'project_management_updated_by',
        'health_safety_updated_by',
        'portfolio_manager',
        'entity_category',
        'project_pricing_type',
        'project_classification',
        'controls_manager',
        'procurement_manager',
        'director',
      ]

      return removeFields(data, fieldsToRemove)
    }

    try {
      const data = prepareData(values)
      if (editRow) {
        if (
          //TODO:-we need this functionality only on staging . whenever we want this things in production that time we are remove this condition
          values['project_name'] !== initialValues['project_name']
        ) {
          // Construct the full URL based on the environment
          const environment = window.location.href.includes('pulse') ? 'production' : 'staging' //TODO: We need to set up the different env file so we can do like that

          const updateNamePayload = {
            project_name: initialValues['project_name'],
            new_project_name: values?.project_name,
            environment,
          }
          renameProject(updateNamePayload, {
            onSuccess: () => {
              const renamedProjectName = values?.project_name

              if (recentProject?.projectName) {
                setRecentProject({
                  projectName: renamedProjectName?.toString() as string,
                })
              }

              successToast('Successfully renamed project!')
            },
            onError: (err: any) => {
              errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
            },
          })
        }
        // Update existing project

        const updatedDataKeys = [
          'owning_entity',
          'project_type',
          'master_pricing_type_id',
          'design_project_manager',
          'design_executive_director',
          'master_procurement_manager_id',
          'master_portfolio_manager_id',
          'master_control_manager_id',
          'executive_director_ids',
          'master_entity_category_id',
          'master_project_classification_id',
          'location_ids',
          'sub_location_ids',
          'design_executive_director_ids',
          'delivery_project_manager',
          'master_director_id',
          'svp',
        ]

        const updatedFields = Object.keys(values).reduce((acc: any, key: any) => {
          if (values[key] !== initialValues[key] && updatedDataKeys.includes(key)) {
            acc[key] = values[key]
          }
          return acc
        }, {})

        const length = Object.keys(updatedFields).length
        if (length) {
          const updateProjectPayload = { id: editRow?.project_name, data }
          updateProjectRecord(updateProjectPayload, {
            onSuccess: () => {
              onDiscard()
              successToast('Project successfully updated!')
            },
            onError: (err: any) => {
              errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
            },
          })
          // Handle project classification update
          if (values?.project_classification && editRow.project_classification !== values?.project_classification) {
            await updateGovernance(data.project_classification, data.project_name)
          }
        }
        onDiscard()
      } else {
        // Add new project
        delete data.last_updated
        data.design_executive_director = data?.design_executive_director
        data.project_sorting_order =
          filteredProjectByEntity.length === 0
            ? 1
            : Number(filteredProjectByEntity[filteredProjectByEntity.length - 1]?.project_sorting_order) + 1

        addProjectRecord(data, {
          onSuccess: () => {
            onDiscard()
            updateGovernance(data.project_classification, data.project_name)
            successToast('Project successfully added')
          },
          onError: (err: any) => {
            errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
          },
        })
      }

      // Reset form after successful submission
      formik.resetForm()
    } catch (error) {
      console.error('Error submitting data:', error)
      showCustomToast('An error occurred while submitting the data', 'error')
    } finally {
      // onClose(); // Uncomment if you need to close a modal or perform another action
    }
  }

  useEffect(() => {
    const form = document.getElementById('form-element')
    if (form && addState) {
      form.scrollIntoView({ behavior: 'smooth' })
      setAddSetTime(false)
    }
  }, [addState])

  const getExistingProject = (project_name: any, projects: any) => {
    const editProject = projects?.find((project: any) => project.project_name === project_name)
    if (editProject) {
      return {
        project_name: editProject.project_name,
        owning_entity: editProject.owning_entity,
        project_type: editProject.project_type,
        master_pricing_type_id: editProject?.MasterPricingType?.id,
        design_executive_director_ids: editProject?.DesignExecutiveDirectors?.map((item: any) => item?.id),
        design_project_manager: editProject.DesignProjectManagers?.map(
          (item: any) => item?.design_project_manager,
        ).join(', '),
        master_procurement_manager_id: editProject?.master_procurement_manager_id,
        master_portfolio_manager_id: editProject?.MasterPortfolioManager?.id,
        master_control_manager_id: editProject?.MasterControlManager?.id,
        executive_director_ids: editProject?.ExecutiveDirectors?.map((item: any) => item?.id),
        master_entity_category_id: editProject?.MasterEntityCategory?.id,
        master_project_classification_id: editProject?.master_project_classification_id,
        location_ids: editProject?.Locations?.map((item: any) => item?.id),
        sub_location_ids: editProject?.SubLocations?.map((item: any) => item?.id),
        delivery_project_manager: editProject?.DeliveryProjectManagers?.map(
          (item: any) => item?.delivery_project_manager,
        ).join(', '),
        master_director_id: editProject?.MasterDirector?.id,
        svp: editProject.svp,
      }
    }

    return {}
  }

  const handleDeleteProject = async (row: string) => {
    if (!isEditForUser) {
      return toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    }
    deleteProjectRecord(
      { projectName: row, period: currentPeriod },
      {
        onSuccess: () => {
          if (initialValues.project_name === row) {
            formik.resetForm()
            setInitialValues(defaultFields)
            setEditRow(null)
            setRecentProject({ projectName: '' })
          } else if (!!editRow?.project_name) {
            const updatedData = getExistingProject(editRow?.project_name, projects)
            setInitialValues(updatedData)
            setEditRow(editRow)
          } else {
            setEditRow(null)
            setRecentProject({ projectName: '' })
          }
          successToast('Project successfully deleted')
        },
        onError: (err: any) => {
          errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
        },
      },
    )
    setDeleteModel(null)
  }

  const handleEditClick = (row: Record<string, any>, projects: any[]) => {
    if (!isEditForUser) {
      return toast(`The current reporting period is locked`, {
        icon: <WarningAmberOutlined />,
      })
    }
    const editProject = projects?.find((project) => project.project_name === row.row.original.project_name)
    const updatedData = getExistingProject(row.row.original.project_name, projects)
    setEditRow(editProject)
    setInitialValues(updatedData)
    setAddSetTime(true)
    setActiveTab('Form')
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'actionCol',
      header: 'Action',
      size: 100,
      align: 'left',
      cell: (row: Record<string, any>, isHover?: boolean) => {
        return (
          <div className={styles.projectCell}>
            <div className={styles.actionButtons}>
              {isHover && (
                <>
                  <EditIcon className={styles.cursorPointer} onClick={() => handleEditClick(row, projects)} />
                  <DeleteIcon
                    className={styles.cursorPointer}
                    onClick={() => {
                      setDeleteModel(row.row.original?.project_name)
                    }}
                  />
                </>
              )}
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'project_name',
      header: 'Project Name',
      size: 500,
      cell: (row: Record<string, any>) => {
        return (
          <div className={styles.projectCell}>
            <span className={styles.projectName}>{row.row.original?.project_name || ''}</span>
          </div>
        )
      },
    },
    {
      accessorKey: 'owning_entity',
      header: 'Owning Entity',
      size: 200,
    },

    {
      accessorKey: 'portfolio_manager',
      header: 'Portfolio Manager',
      size: 200,
    },
    {
      accessorKey: 'design_executive_director',
      header: 'Design Executive Director',
      size: 200,
      filterType: 'multiSelect',
    },
    {
      accessorKey: 'controls_manager',
      header: 'Control Manager',
      size: 200,
    },
    {
      accessorKey: 'executive_director',
      header: 'Delivery Executive Director',
      size: 200,
      filterType: 'multiSelect',
      sortAlphabetically: true,
    },
    {
      accessorKey: 'project_classification',
      header: 'Project Classification',
      size: 200,
    },
    {
      accessorKey: 'director',
      header: 'Delivery Director',
      size: 200,
    },
  ]

  const tabPanels: ITabPanels[] = [{ label: 'Form' }, { label: 'Table' }]

  return (
    <div className={styles.container}>
      <div className={styles.header} id="form-element">
        <TypographyField variant={'subheadingSemiBold'} className={styles.headerTitle} text={'Create New Project'} />
        <div className={styles.actionButtons}>
          <Button
            className={styles.closeButton}
            onClick={() => {
              onDiscard()
              onClose()
            }}
          >
            X Close
          </Button>
        </div>
      </div>

      <div className={styles.content}>
        {loader || isProjectsLoading ? (
          <Loader />
        ) : (
          <>
            {breakpoint === MOBILE || breakpoint === TABLET || breakpoint === LAPTOP ? (
              <div>
                <Tabs
                  tabPanels={tabPanels}
                  className="custom-classname"
                  activeTab={activeTab === 'Form' ? 0 : 1}
                  selectedTab={(index: number) => setActiveTab(tabPanels[index].label)}
                >
                  <>
                    <AddProjectForm formik={formik} editRow={editRow} onDiscard={onDiscard} isEnable={addProject} />
                  </>
                  <>
                    <div className={styles.table}>
                      <TanStackTable
                        rows={(getProjectTableData(projects) as any) ?? []}
                        columns={columns}
                        showPagination={true}
                        isOverflow={true}
                        tableHeight={tableHeight}
                        pageSize={pageSize}
                        stickyColumnCount={1}
                      />
                    </div>
                  </>
                </Tabs>
              </div>
            ) : (
              <>
                <AddProjectForm formik={formik} editRow={editRow} onDiscard={onDiscard} isEnable={addProject} />
                <div className={styles.table}>
                  <TanStackTable
                    rows={(getProjectTableData(projects) as any) ?? []}
                    columns={columns}
                    showPagination={true}
                    tableHeight={tableHeight}
                    pageSize={pageSize}
                    isOverflow={true}
                    stickyColumnCount={3}
                  />
                </div>
              </>
            )}
          </>
        )}
      </div>
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDeleteProject(deleteModel as string)}
      />
    </div>
  )
}

export default AddProjectDrawer
