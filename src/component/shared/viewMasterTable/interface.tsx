export interface IViewMasterData {
  fieldName: any
  value: any
}
export interface IViewMasterColumn {
  key: string
  label: string
  filterCell?: () => JSX.Element | undefined
  renderCell?: (value: any, row: Record<string, any>, rowIndex: number) => JSX.Element | undefined
  cellStyle?: React.CSSProperties
  headerStyle?: React.CSSProperties
  isRabin?: boolean
}

export interface IViewMasterTableProps {
  columns: IViewMasterColumn[]
  data: Record<string, any>[]
}

export interface TableHeaderProps {
  columns: IViewMasterColumn[]
}

export interface TableBodyProps {
  data: Record<string, any>[]
  columns: IViewMasterColumn[]
}

export interface TableRowProps {
  row: Record<string, any>
  rowIndex: number
  columns: IViewMasterColumn[]
}

export interface TableCellProps {
  value: any
  column: IViewMasterColumn
  rowIndex: number
  row: Record<string, any>
}
