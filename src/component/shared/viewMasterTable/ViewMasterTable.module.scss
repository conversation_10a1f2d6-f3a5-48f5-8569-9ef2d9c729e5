@import '/styles/color.scss';

.tableContainer {
  width: 100%;
  margin-top: 10px;
}
.table {
  border-collapse: collapse;
  width: 100%;
  .tableHeadRow {
    background: $DARK;
    th {
      border-right: 1px solid $DARK_100;
      padding-left: 8px;
      padding: 11px 10px;
      font-family: Poppins;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      text-align: left;
      color: $WHITE;
    }
  }
  .tableRow {
    border: 1px solid $LIGHT_200;
    .tableData {
      padding: 9px 10px;
      border-right: 1px solid $LIGHT_200;
      background: $WHITE;
      font-family: Poppins;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      letter-spacing: 0em;
      text-align: left;
      color: $BLACK;
      position: relative;
      overflow: hidden;
    }
  }
}

.filterRow {
  border: 1px solid $LIGHT_200;

  .filterCell {
    border-right: 1px solid $LIGHT_200;
  }
}
.ribbon {
  background-color: $GREEN;
  color: $WHITE;
  position: absolute;
  padding: 2px;
  height: 10px;
  display: flex;
  align-items: center;
  transform: rotate(45deg);
  top: 4px;
  right: -21px;
  width: 60px;
  justify-content: center;
  font-size: 9px;
}
