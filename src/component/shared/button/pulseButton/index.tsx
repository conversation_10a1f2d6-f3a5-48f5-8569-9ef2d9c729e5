// CommonButton.tsx
import React from 'react'
import Button from '..'
import styles from './PulseButton.module.scss'

interface IPulseButton {
  onClick: () => void
  label: string
  icon?: React.ReactNode
  disabled?: boolean
  className?: string
}

const PulseButton: React.FC<IPulseButton> = ({ onClick, label, icon, disabled = false, className = '' }) => {
  return (
    <Button onClick={onClick} className={`${styles.addButton} ${className}`} disabled={disabled}>
      {icon && <div className={styles.icon}>{icon}</div>}
      <span className={styles.label}>{label}</span>
    </Button>
  )
}

export default PulseButton
