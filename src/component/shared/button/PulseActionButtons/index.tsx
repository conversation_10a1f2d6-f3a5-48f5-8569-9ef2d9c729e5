import React from 'react'
import Button from '..'
import styles from './PulseActionButtons.module.scss'
import CancelRoundedIcon from '@/src/component/svgImages/cancelRoundedIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import { BLACK, WHITE } from '@/src/constant/color'

const PulseActionButtons = ({
  edit,
  onEdit,
  onDiscard,
  onSave,
  editLabel = 'Edit Info',
  discardLabel = 'Discard',
  saveLabel = 'Save',
  editDisable = false,
  discardDisable = false,
  saveDisable = false,
}: any) => {
  return (
    <div className={styles.buttonContainer}>
      <Button
        startIcon={<EditIcon fill={edit ? BLACK : WHITE} />}
        variant={edit ? 'outlined' : 'contained'}
        onClick={onEdit}
        disabled={editDisable}
      >
        {editLabel}
      </Button>

      <Button
        startIcon={<CancelRoundedIcon color={edit ? WHITE : BLACK} />}
        variant={edit ? 'contained' : 'outlined'}
        onClick={onDiscard}
        disabled={discardDisable}
      >
        {discardLabel}
      </Button>

      <Button
        startIcon={<SaveIcon color={edit ? WHITE : BLACK} />}
        variant={edit ? 'contained' : 'outlined'}
        onClick={onSave}
        disabled={saveDisable}
      >
        {saveLabel}
      </Button>
    </div>
  )
}

export default PulseActionButtons
