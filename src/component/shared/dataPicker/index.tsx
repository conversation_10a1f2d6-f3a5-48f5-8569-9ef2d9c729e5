import React, { ReactElement, useEffect, useMemo, useRef, useState } from 'react'
import { KeyboardArrowDownRounded, KeyboardArrowLeftRounded, KeyboardArrowRightRounded } from '@mui/icons-material'
import { DateView, LocalizationProvider, PickersActionBarProps } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { DatePicker as MuiDatePicker } from '@mui/x-date-pickers/DatePicker'
import styles from './DatePicker.module.scss'
import CalendarIcon from '../../svgImages/calenderIcon'
import Button from '../button'
import Typo<PERSON><PERSON>ield from '../typography'
import { DARK_200 } from '@/src/constant/color'
import { parseDateFromString } from '@/src/utils/dateUtils'

interface MuiDatePickerProps {
  labelText?: string
  value: string | number | Date | null
  onChange?: (date: any) => void
  className?: any
  minDate?: string | number | Date | null
  maxDate?: string | number | Date
  error?: boolean
  helperText?: string
  disableEditable?: boolean
  onAccept?: (value: any) => void
  placeholder?: string
  onError?: (reason?: string | null) => void
  shouldDisableDate?: any
  disabled?: boolean
  required?: boolean
  name?: string
  sx?: any
  onBlur?: any
  actionButtons?: any
  CustomDay?: any
  shouldCloseOnSelect?: boolean
  format?: string
  views?: DateView[]
  slotProps?: any
}

const DatePicker: React.FC<MuiDatePickerProps> = ({
  labelText,
  value,
  onChange = () => null,
  className,
  minDate,
  maxDate,
  error = false,
  helperText,
  disableEditable = false,
  onAccept,
  onBlur,
  placeholder = '',
  onError = () => null,
  shouldDisableDate,
  actionButtons = false,
  disabled = false,
  required = false,
  name = '',
  sx,
  CustomDay,
  format = 'dd-MM-yyyy',
  views,
  slotProps,
  ...props
}): ReactElement => {
  const dateValue = useMemo(() => (value ? parseDateFromString(value as string) : null), [value])
  const pickerRef = useRef<HTMLDivElement>(null)
  const [isOpen, setIsOpen] = useState(false)
  const [tempDate, setTempDate] = useState<Date | null>(dateValue)
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 768) // New: Detect small screen (adjust threshold as needed)

  // New: Listen for window resize to update screen size dynamically
  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 768) // e.g., < 768px = small screen
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize) // Cleanup
  }, [])

  //* Sync tempDate with value prop changes
  useEffect(() => {
    setTempDate(dateValue)
  }, [dateValue])

  const handleOpen = () => {
    setTempDate(dateValue)
    setIsOpen(true)
  }

  const handleAdd = () => {
    setIsOpen(false)
    if (onChange) onChange(tempDate)
    if (onAccept) onAccept(tempDate)
  }

  const handleCancel = () => {
    setIsOpen(false)
    setTempDate(dateValue)
  }

  const handleClose = () => {
    setIsOpen(false)
    setTempDate(dateValue)
  }

  const handleTempDateChange = (val: any, context?: any) => {
    if (!val) {
      setTempDate(null)
      // If changed via direct input, apply immediately
      if (context?.validationError === null && !isOpen) {
        if (onChange) onChange(null)
        if (onAccept) onAccept(null)
      }
      return
    }

    let parsedDate: Date | null = null
    if (val instanceof Date) {
      parsedDate = val
    } else {
      parsedDate = parseDateFromString(val)
    }

    setTempDate(parsedDate)

    // If changed via direct input (picker is not open) and no validation error, apply immediately
    if (!isOpen && context?.validationError === null) {
      if (onChange) onChange(parsedDate)
      if (onAccept) onAccept(parsedDate)
    }
  }

  const CustomActionBar = (props: PickersActionBarProps) => {
    return (
      <div className={styles.buttonActions} style={{ display: 'flex', gap: '10px' }}>
        <Button color="secondary" onClick={handleCancel}>
          Cancel
        </Button>
        <Button onClick={handleAdd}>Add</Button>
      </div>
    )
  }

  return (
    <div style={{ width: '100%' }} className={`${styles.datePickerWrapper} ${className}`} ref={pickerRef}>
      {labelText && (
        <div>
          <TypographyField style={{ color: DARK_200 }} variant="caption" text={labelText} />
        </div>
      )}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <MuiDatePicker
          {...props}
          open={isOpen}
          onClose={handleClose}
          format={format}
          views={views}
          closeOnSelect={false}
          className={className}
          minDate={minDate}
          value={tempDate || null}
          disabled={disabled}
          onChange={handleTempDateChange}
          slots={{
            actionBar: CustomActionBar,
            openPickerIcon: () => <CalendarIcon />,
            leftArrowIcon: () => <KeyboardArrowLeftRounded />,
            rightArrowIcon: () => <KeyboardArrowRightRounded />,
            switchViewIcon: () => <KeyboardArrowDownRounded />,
            day: CustomDay && CustomDay,
          }}
          slotProps={{
            layout: { sx: { display: 'block', textAlign: 'end' } },
            textField: {
              sx: { '& .Mui-error': { border: '1px solid #ffffff' }, ...sx },
            },
            openPickerButton: {
              onClick: handleOpen,
              onMouseDown: (e) => {
                e.preventDefault()
              },
            },
            ...slotProps,
          }}
        />
      </LocalizationProvider>
    </div>
  )
}

export default DatePicker
