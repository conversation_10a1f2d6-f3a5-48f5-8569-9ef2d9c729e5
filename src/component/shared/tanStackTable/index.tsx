import React, { CSSProperties, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { DndContext, closestCenter } from '@dnd-kit/core'
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Tooltip } from '@mui/material'
import {
  Header,
  SortingState,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { excludeColumns } from './constant'
import EditableCell from './editableCell'
import { applyFilters, dateSortingFn, sortColumnsByOrder } from './helper'
import { useColumnVisibility } from './hooks/useColumnVisibility'
import { useDragAndDrop } from './hooks/useDragAndDrop'
import { useFilteredData } from './hooks/useFilteredData'
// import { areObjectsIdentical } from './hooks/useTableConfig'
import { IRowData, TanStackTableProps } from './interface'
import ManageColumnsModal from './manageColumnsModal'
import NoData from './noData'
import Pagination from './pagination'
import styles from './TanStackTable.module.scss'
import HeaderCell from '../../customCells/headerCell'
import { FilterValue } from '../../customCells/headerCell/interface'
import ValidationModel from '../../updateProgress/progressForm/validationModel'
import Loader from '../loader'
import PulseModel from '../pulseModel'
import TableTooltip from '../tableTooltip'
import { getTooltipFormulaStageTableToColumn } from '@/src/helpers/helpers'
import { useOutsideClick } from '@/src/hooks/useOutsideClick'
import { dateNonRequiredFields } from '@/src/utils/arrayUtils'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'

const TanStackTable: React.FC<TanStackTableProps> = ({
  rows,
  columns,
  onDragEnd,
  handleRowClick,
  gridFilters,
  setGridFilters,
  setFilterRows,
  columnVisibilities,
  isResizeColumn = false,
  setColumnVisibilitiesApi,
  colWidth,
  setColWidth,
  columnOrder,
  setColOrder,
  isCount = false,
  gridName = '',
  tableHeight = '',
  showPagination = false,
  isOverflow = showPagination,
  pageSize,
  className,
  isLoading,
  stickyColumnCount = 4, // Add default value for stickyColumnCount
  enableSticky = true, // Add default value for enableSticky
  persistConfig = false, // Add default value for persistConfig
}) => {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnWidth, setColumnWidth] = useState(colWidth || {})
  const [cols, setCols] = useState(columns)
  const [filterValue, setFilterValue] = useState<FilterValue[]>(gridFilters || [])
  const [manageColumnsAnchorEl, setManageColumnsAnchorEl] = useState<HTMLElement | null>(null)
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  useEffect(() => {
    if (columnOrder && Array.isArray(columnOrder)) {
      const sortedColumns = sortColumnsByOrder(columnOrder, columns)
      setCols(sortedColumns)
    } else {
      setCols(columns)
    }
    // if (!areObjectsIdentical(colWidth, columnWidth)) {
    //   setColumnWidth(colWidth || {})
    // }
  }, [columns, columnOrder])

  useEffect(() => {
    if (pageSize && showPagination) {
      setPagination((prev) => ({
        ...prev,
        pageIndex: 0, // Reset page index to avoid out-of-bounds issue
        pageSize,
      }))
    }
  }, [pageSize])

  useEffect(() => {
    setColumnWidth(colWidth || {})
  }, [colWidth])

  // Use the useColumnVisibility hook for managing column visibility
  const { visibleColumns, columnVisibility, toggleColumnVisibility } = useColumnVisibility(
    cols,
    columnVisibilities,
    setColumnVisibilitiesApi,
    persistConfig,
  )

  //TODO : Prevent rerender if not change
  const withStickyColumns = useMemo(() => {
    if (!enableSticky) return visibleColumns
    return visibleColumns.map((item, index) => {
      if (visibleColumns.length > 8 && index < stickyColumnCount) {
        item.pin = true
      } else {
        item.pin = false
      }
      return item
    })
  }, [visibleColumns, stickyColumnCount, enableSticky])

  // Filtered data using the custom hook
  const filteredData = useFilteredData(rows, filterValue, withStickyColumns, setFilterRows)

  const filteredDataOptions = (filterColumn: string) => {
    const filterValueArray = filterValue?.filter((res) => res?.colId !== filterColumn)
    const tableRows = rows ?? []
    const filteredData = applyFilters(tableRows, filterValueArray, withStickyColumns)
    return filteredData
  }

  const table = useReactTable({
    data: filteredData || [],
    columns: withStickyColumns || [],
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    columnResizeMode: 'onChange',
    onSortingChange: setSorting,
    state: showPagination ? { sorting, pagination } : { sorting }, // Conditionally include pagination
    getRowId: (row) => row?.id,
    onPaginationChange: showPagination ? setPagination : undefined, // Disable pagination updates if not needed
    getPaginationRowModel: showPagination ? getPaginationRowModel() : undefined, // Disable pagination model if not needed
    sortingFns: { dateSorting: dateSortingFn },
  })

  const { sensors, handleRowDragEnd } = useDragAndDrop(rows, onDragEnd)

  useEffect(() => {
    setFilterValue(gridFilters || [])
  }, [gridFilters])

  const onColumnReorder = (dragId: any, dropId: any) => {
    const filterColumns = cols.filter((column) => !excludeColumns.includes(column?.accessorKey))

    const reorderedColumns = Array.from(filterColumns)
    const [removed] = reorderedColumns.splice(dragId, 1)
    reorderedColumns.splice(dropId, 0, removed)

    const finalColumns: any = cols.map((column) => {
      return excludeColumns.includes(column.accessorKey) ? column : reorderedColumns.shift()
    })
    setCols(finalColumns)
    if (setColOrder) {
      const finalColumnsOrder = finalColumns?.map((res: any, index: number) => {
        return { column: res?.accessorKey, order: index + 1 }
      })
      setColOrder(finalColumnsOrder)
    }
  }

  const TanStackRow: React.FC<{
    row: IRowData
    onRowClick?: (row: IRowData) => void
    columnWidths?: Record<string, number>
    gridName?: string
  }> = ({ row, onRowClick, columnWidths, gridName }) => {
    const [activeEditableCell, setActiveEditableCell] = useState<{ rowId: string; columnId: string } | null>(null)
    const [editableCellValue, setEditableCellValue] = useState<string | number | null>(null)
    const [isSaving, setIsSaving] = useState(false)
    const [isFieldEmpty, setIsFieldEmpty] = useState(false)
    const [currentEditableField, setCurrentEditableField] = useState('')

    const { transform, transition, setNodeRef, isDragging } = useSortable({
      id: row?.id, // Use row.id instead of row?.original?.id
    })

    const isMounted = useRef(true)

    React.useEffect(() => {
      isMounted.current = true
      return () => {
        isMounted.current = false
      }
    }, [])

    const rowStyles: CSSProperties = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.8 : 1,
      zIndex: isDragging ? 1 : 0,
      position: 'relative',
    }

    const enableCellEditing = useCallback(
      (columnId: string, cell: any) => {
        if (isSaving || (row?.original?.id === activeEditableCell?.rowId && columnId === activeEditableCell?.columnId))
          return
        const value = cell.column.columnDef?.customEditValue
          ? cell.column.columnDef.customEditValue(cell, row?.original)
          : row?.original[columnId]?.toString() || ''
        setActiveEditableCell({ rowId: row?.original?.id, columnId })
        setEditableCellValue(value || '')
      },
      [activeEditableCell, isSaving, row],
    )

    const saveEditedCell = useCallback(
      async (cell: any) => {
        if (!activeEditableCell) return

        setIsSaving(true)
        try {
          const res = await cell?.column?.columnDef.onEditCell(activeEditableCell, editableCellValue, row?.original)
          if (res && isMounted.current) {
            setActiveEditableCell(null)
            // setIsSaving(false)
          }
          setIsSaving(false)
        } catch (error) {
          console.error('Error updating cell:', error)
          setIsSaving(false)
        }
      },
      [activeEditableCell, editableCellValue, row],
    )

    const saveEditedDateCell = useCallback(
      async (cell: any, value: any) => {
        if (!activeEditableCell) return

        setIsSaving(true)
        try {
          const res = await cell?.column?.columnDef.onEditCell(activeEditableCell, value, row?.original)
          if (res && isMounted.current) {
            setActiveEditableCell(null)
            // setIsSaving(false)
          }
          setIsSaving(false)
        } catch (error) {
          console.error('Error updating date cell:', error)
          setIsSaving(false)
        }
      },
      [activeEditableCell, row],
    )

    const ref = useOutsideClick<HTMLTableCellElement>({
      onOutsideClick: () => {
        if (activeEditableCell?.rowId !== row.id) {
          setActiveEditableCell(null)
        }
      },
      enabled: !!activeEditableCell,
    })

    const isTextEmpty = (value: string) => {
      if (!value) return true // If value is null, undefined, or empty string
      // Create a temporary DOM element to parse HTML
      const tempDiv = document.createElement('p')
      tempDiv.innerHTML = value
      // Extract text content while ignoring HTML tags
      const text = tempDiv.innerText.trim()
      return text.length === 0 // If text is empty, return true
    }

    return (
      <>
        {/* <div ref={setNodeRef} style={rowStyles} className={styles.dataRow} onClick={() => onRowClick?.(row)}> */}
        <tr ref={setNodeRef} onClick={() => onRowClick?.(row)} className={styles.dataRowWrapper}>
          <div style={rowStyles} className={styles.dataRow}>
            {row?.getVisibleCells()?.map((cell: any) => {
              const isEditableCell =
                typeof cell.column.columnDef.isEditableCell === 'function'
                  ? cell.column.columnDef.isEditableCell(cell, row)
                  : cell.column.columnDef.isEditableCell
              const isEditable = isEditableCell || false
              const isEditing = activeEditableCell?.rowId === row.id && activeEditableCell?.columnId === cell.column.id
              const cellWidth = columnWidths?.[cell?.column?.id] || cell?.column?.getSize()
              const isRequire = cell?.column?.columnDef?.require
              const isTextExist =
                cell.column.columnDef?.editableType === 'richTextEditor'
                  ? !isTextEmpty(editableCellValue as string)
                  : (editableCellValue && editableCellValue?.toString()?.trim().length > 0) || false
              const formateType = cell?.column?.columnDef?.editableFieldFormatType || ''
              const isCommaSeparated = !!(formateType === 'comma-separated')

              return (
                <td
                  ref={ref}
                  key={cell?.id}
                  className={`${styles.rowCell}`}
                  style={{
                    width: cellWidth,
                    textAlign: cell?.column?.columnDef?.align || 'left',
                    flex: cell?.column?.columnDef?.flex,
                    cursor: isSaving ? 'not-allowed' : cell?.column?.columnDef?.cursor,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    ...getCellStyle(cell.column.id, 'row'),
                  }}
                  onClick={() => enableCellEditing(cell.column.id, cell)}
                >
                  <div className={`${isEditable ? styles.highlightEditableCell : ''}`}>
                    {cell.column.id === 'isRibbon' ? (
                      <div
                        className={styles.verticalRibbon}
                        style={{ backgroundColor: row.original.isRibbon ? 'green' : 'red' }}
                      />
                    ) : isSaving && isEditing ? (
                      <div className={styles.loaderContainer}>
                        <Loader smallLoader={true} />
                      </div>
                    ) : isEditable && isEditing ? (
                      <>
                        <EditableCell
                          value={editableCellValue}
                          defaultValue={row?.original[activeEditableCell?.columnId]?.toString() || ''}
                          cell={cell}
                          row={row}
                          column={cell.column}
                          onBlur={() => {
                            setCurrentEditableField(cell?.column?.columnDef.header)
                            // if value not exist and field is require , show warning model
                            isTextExist || !isRequire ? saveEditedCell(cell) : setIsFieldEmpty(true)
                          }}
                          onDatePickerBlur={(value) => {
                            saveEditedDateCell(cell, value)
                          }}
                          setValue={setEditableCellValue}
                          setActiveEditableCell={setActiveEditableCell}
                        />
                      </>
                    ) : (
                      <>
                        {
                          // dateNonRequiredFields(cell?.column?.columnDef.accessorKey, cell?.column.id?.toString())
                          //   ? cell.renderValue() != 'null' && !!cell.renderValue()
                          //     ? cell.renderValue()
                          //     : cell.column.columnDef?.editableType == 'date' && isEditable
                          //       ? 'DD/MM/YYYY'
                          //       : ''
                          //   :
                          isEditable
                            ? cell.column.columnDef?.editableType === 'number'
                              ? cell.getValue() !== null && cell.getValue() !== undefined
                                ? isCommaSeparated
                                  ? formatNumberWithCommas(Number(cell.getValue()))
                                  : cell.getValue()
                                : '-'
                              : cell.renderValue()
                                ? flexRender(cell.column.columnDef.cell, cell.getContext())
                                : cell.column.columnDef?.editableType === 'date'
                                  ? 'DD-MM-YYYY'
                                  : cell.column.columnDef?.editableType == 'multiDropDown' //TODO : Check for multiDropDown condition
                                    ? flexRender(cell.column.columnDef.cell, cell.getContext())
                                    : '-'
                            : isCommaSeparated
                              ? formatNumberWithCommas(cell.getValue())
                              : flexRender(cell.column.columnDef.cell, cell.getContext())
                        }
                      </>
                    )}
                  </div>
                </td>
              )
            })}
            <PulseModel
              closable={false}
              style={{ width: 'fitContent' }}
              open={isFieldEmpty}
              onClose={() => {
                setActiveEditableCell(null)
                setIsFieldEmpty(false)
              }}
              content={
                <ValidationModel
                  messages={[`The ${currentEditableField} field is required in ${gridName}. Please enter a value.`]}
                  onClose={() => {
                    setActiveEditableCell(null)
                    setIsFieldEmpty(false)
                  }}
                />
              }
            />
          </div>
        </tr>
      </>
    )
  }

  if (!table) return null

  // Get row model and check if rows exist
  const rowModel = table.getRowModel()
  const tableRows = rowModel?.rows || []

  // Get pinned columns
  const pinnedColumns = withStickyColumns.filter((col) => col.pin)

  // Calculate left position for pinned withStickyColumns
  const getLeftPosition = (columnId: string) => {
    const column = withStickyColumns.find((col) => col.accessorKey === columnId)
    if (!column?.pin) return 0

    const pinnedIndex = pinnedColumns.findIndex((col) => col.accessorKey === columnId)
    if (pinnedIndex === -1) return 0

    return pinnedColumns
      .slice(0, pinnedIndex)
      .reduce(
        (sum, col) =>
          sum + ((columnWidth.hasOwnProperty(col?.accessorKey) ? columnWidth[col?.accessorKey] : col.size) ?? 0),
        0,
      )
  }

  // Get cell styling
  const getCellStyle = (columnId: string, type: 'header' | 'row') => {
    if (!columnId) return {}
    const column = withStickyColumns.find((col) => col.accessorKey === columnId)
    if (!column) return {}

    const isPinned = column?.pin || false
    if (!isPinned) return {}

    const isLastPinned = isPinned && pinnedColumns[pinnedColumns.length - 1]?.accessorKey === columnId

    const zIndex = type === 'header' ? 11 : 10
    const backgroundColor = type === 'header' ? '#D1D1D1' : ''
    const width = columnWidth.hasOwnProperty(columnId) ? columnWidth[columnId] : column?.size

    return {
      width: `${width}px`,
      minWidth: `${width}px`,
      maxWidth: `${width}px`,
      position: isPinned ? ('sticky' as 'sticky') : ('relative' as 'relative'),
      left: isPinned ? `${getLeftPosition(columnId)}px` : undefined,
      zIndex: isPinned ? zIndex : 1,
      backgroundColor: isPinned ? backgroundColor : '',
      boxShadow: isLastPinned ? '3px 0px 5px -4px rgba(0,0,0,0.12)' : 'none',
      borderRight: isLastPinned ? (type === 'header' ? `1px solid #b9b9b9` : `1px solid #f8f3f3`) : '0',
      // marginRight: isPinned ? '-1px' : '0',
    }
  }

  return (
    <>
      {rows?.length ? (
        <>
          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleRowDragEnd}>
            <div
              className={`${styles.tableContainer} ${className || ''}`}
              style={{ height: tableHeight ? `${tableHeight}` : 'auto', overflow: isOverflow ? 'auto' : '' }}
            >
              <div className={styles.tanStackGrid}>
                {/* Render Table Headers */}
                {table?.getHeaderGroups()?.map((headerGroup) => (
                  <div key={headerGroup.id} className={styles.headerRow}>
                    {headerGroup.headers.map((header: Header<IRowData, unknown>) => {
                      return (
                        // <div style={{ ...getCellStyle(header.column.id, 'header') }}>
                        <HeaderCell
                          isResizeColumn={isResizeColumn}
                          columnWidth={columnWidth}
                          setColumnWidth={(data: any) => {
                            setColumnWidth(data)
                            setColWidth?.(data)
                          }}
                          table={table}
                          key={header?.id}
                          // rows={rows}
                          rows={filteredDataOptions(header.column.id)}
                          header={header}
                          setManageColumnsAnchorEl={setManageColumnsAnchorEl}
                          filterValue={filterValue}
                          setFilterValue={setFilterValue}
                          setGridFilters={setGridFilters}
                          style={{ ...getCellStyle(header.column.id, 'header') }}
                        />
                        // </div>
                      )
                    })}
                  </div>
                ))}

                {/* Render Table Rows */}
                <SortableContext items={rows?.map((row) => row?.id)} strategy={verticalListSortingStrategy}>
                  {/* table.getRowModel().rows */}
                  {tableRows.map((row, index) => (
                    <TanStackRow
                      key={`${row.id}-${index}`}
                      row={row}
                      onRowClick={handleRowClick}
                      columnWidths={columnWidth}
                      gridName={gridName}
                    />
                  ))}
                </SortableContext>
              </div>
            </div>
          </DndContext>

          {/* Manage Columns Modal */}
          <ManageColumnsModal
            anchorEl={manageColumnsAnchorEl}
            onClose={() => setManageColumnsAnchorEl(null)}
            cols={cols}
            columnVisibility={columnVisibility}
            onColumnVisibility={toggleColumnVisibility}
            onColumnReorder={onColumnReorder}
          />

          {showPagination && <Pagination table={table} />}
          {/* Display Row Count */}
          {/* {isCount && <span className={styles.count}>Count: {filteredData.length}</span>} */}
        </>
      ) : (
        <NoData isLoading={isLoading} />
      )}
    </>
  )
}

export default TanStackTable
