import { useEffect, useState } from 'react'
import { CustomColumnDef } from '../interface'

export const useColumnVisibility = (
  columns: CustomColumnDef<any>[],
  initialVisibility: Record<string, boolean> = {},
  setColumnVisibilitiesApi?: (visibilities: Record<string, boolean>) => void,
  persistConfig?: boolean,
) => {
  const initialVisibleCols = Object?.fromEntries(columns.map((col) => [col.accessorKey, col.visible ?? true]))
  const [columnVisibility, setColumnVisibility] = useState(
    initialVisibility !== null && Object?.keys(initialVisibility).length ? initialVisibility : initialVisibleCols,
  )

  const visibleColumns = columns.filter((col) => columnVisibility[col.accessorKey])

  const toggleColumnVisibility = (columnId: string) => {
    const updatedVisibility = { ...columnVisibility, [columnId]: !columnVisibility[columnId] }
    setColumnVisibility(updatedVisibility)
    setColumnVisibilitiesApi?.(updatedVisibility)
  }

  useEffect(() => {
    if (persistConfig) {
      const visCol =
        initialVisibility !== null && Object?.keys(initialVisibility).length ? initialVisibility : initialVisibleCols
      setColumnVisibility(visCol)
    }
  }, [initialVisibility])

  return { visibleColumns, columnVisibility, toggleColumnVisibility }
}
