import { Row } from '@tanstack/react-table'
import { parse, isValid, compareAsc, compareDesc } from 'date-fns'
import { CustomColumnDef, IRowData } from './interface'
import { FilterValue } from '../../customCells/headerCell/interface'
import { MULTI_SELECT_SEPARATOR, PREDECESSOR_SUCCESSOR_SEPARATOR } from '@/src/constant/stageStatus'
import { ILookupProjectToPhase, ILookupProjectToProjectPhaseCategory } from '@/src/redux/status/interface'
import { prepareDropdownOptions, prepareMultiPhaseCategoryDropdownOptions } from '@/src/utils/arrayUtils'
import { naturalSort } from '@/src/utils/sortingUtils'

export const applyFilters = (rows: IRowData[], filters: FilterValue[], visibleColumns: CustomColumnDef<any>[]) => {
  if (!filters?.length) return rows

  return rows.filter((row) => filters.every((filter) => applyFilter(row, filter, visibleColumns)))
}

const isBlankValue = (value: any): boolean => {
  return value == null || value === '-' || value.toString().trim() === ''
}

const ManageFilterForMultiPhasecategory = (
  row: IRowData,
  filter: FilterValue,
  visibleColumn?: CustomColumnDef<any> | null,
) => {
  const [lookupKey, masterKey] = visibleColumn?.childAccessorKey ?? []

  switch (filter.type) {
    case 'phase/package':
    case 'wildcard-multi-select':
    case 'wildcard-multi-association':
      return !(
        (masterKey
          ? prepareMultiPhaseCategoryDropdownOptions(row?.[lookupKey], masterKey, filter.colId)
          : prepareDropdownOptions(row?.[lookupKey], filter.colId)
        ).length > 0
      )
    case 'wildcard-single-association':
      return !(masterKey ? row?.[lookupKey]?.[masterKey]?.[filter.colId] : row?.[lookupKey]?.[filter.colId])
    default:
      return isBlankValue(row[filter.colId])
  }
}

// Handler for the filter functionality when "Blanks" is selected
const handleBlanksFilter = (
  row: IRowData,
  filter: FilterValue,
  visibleColumn?: CustomColumnDef<any> | null,
): boolean => {
  return filter?.values?.some((mainValue) => {
    // Check if the main value matches blank
    const mainMatch = ManageFilterForMultiPhasecategory(row, filter, visibleColumn)
    // const mainMatch = isBlankValue(row[filter.colId])

    // If there are sub-values to consider
    if (mainValue?.subValues) {
      const { colId: subColId, values: subValues } = mainValue?.subValues
      if (!subValues?.length) return mainMatch

      const subValueMatch = subValues
        .map((subVal) => subVal?.value?.toLowerCase())
        .includes((row[subColId]?.toString() || '').toLowerCase())

      return mainMatch && subValueMatch
    }

    return mainMatch
  })
}

//  Handler for the filter functionality when boolean is selected
const handleBooleanFilter = (row: IRowData, filter: FilterValue): boolean => {
  return filter.values.some((mainValue) => {
    return row[filter.colId] === mainValue?.value
  })
}

// Generic filter handler
const applyFilter = (row: IRowData, filter: FilterValue, visibleColumns: CustomColumnDef<any>[]): boolean => {
  if (!filter.values || !filter.values.length) return true
  let visibleColumn = visibleColumns.find((col) => col.accessorKey === filter.colId) ?? null

  switch (filter.type) {
    case 'progress':
      return handleProgressFilter(row, filter, visibleColumns)
    case 'multiSelect':
    case 'semiColon':
      return handleMultiSelectOrSemiColonFilter(row, filter)
    case 'phase/package':
    case 'wildcard-multi-select':
    case 'wildcard-multi-association':
      return handleMultiSelectForWildCard(row, filter, visibleColumn)
    case 'wildcard-single-association':
      return handleMultiSelectForWildCard(row, filter, visibleColumn)
    case 'wildcard-predecessor-successor':
      return handlePredecessorSuccessor(row, filter)
    default:
      return handleDefaultFilter(row, filter)
  }
}

// Helper for "progress" filter
const handleProgressFilter = (row: IRowData, filter: FilterValue, visibleColumns: CustomColumnDef<any>[]): boolean => {
  const isInRange = (value: number, range: string): boolean => {
    const [start, end] = range.split('-').map((item) => parseFloat(item.replace('%', '').trim()))
    return value >= start && value <= end
  }

  return filter.values.some((filterValue) => {
    if (filterValue.value === 'Blanks') {
      return handleBlanksFilter(row, filter)
    }
    const mainValueToCompare = filterValue.value
    const mainMatch = row[filter.colId]

    // Direct match
    if (mainMatch && isInRange(Number(mainMatch), mainValueToCompare)) return true

    // Check additional keys from visibleColumns
    const col = visibleColumns.find((column) => column.accessorKey === filter.colId)
    if (col?.filterRow) {
      return col.filterRow.some((filterKey) => {
        const fieldValue = row[filterKey]
        return isInRange(fieldValue, mainValueToCompare)
      })
    }

    return false
  })
}

// Helper for "multiSelect" or "semiColon" filter
const handleMultiSelectOrSemiColonFilter = (row: IRowData, filter: FilterValue): boolean => {
  return filter.values.some((mainValue) => {
    const mainValueToCompare = mainValue.value.toLowerCase()
    const mainMatch = (row[filter.colId]?.toString() || '').toLowerCase().includes(mainValueToCompare)

    if (mainValue.value === 'Blanks') {
      return handleBlanksFilter(row, filter)
    }

    if (mainValue?.subValues) {
      const { colId: subColId, values: subValues } = mainValue.subValues
      if (!subValues.length) return mainMatch

      const subValueMatch = subValues.some(
        (subVal) => (row[subColId]?.toString() || '').toLowerCase() === subVal.value.toLowerCase(),
      )

      return mainMatch && subValueMatch
    }

    return mainMatch
  })
}

/**
 *  Helper for wildcard separator
 *  1.MULTI_SELECT_SEPARATOR ($@)
 *  2.PREDECESSOR_SUCCESSOR_SEPARATOR ($#)
 */
//TODO: Generated old version of handleMultiSelectForWildcard function
/* const handleMultiSelectForWildCard = (
  row: IRowData,
  filter: FilterValue,
  visibleColumn: CustomColumnDef<any> | null,
): boolean => {
  return filter.values.some((mainValue) => {
    const mainValueToCompare = mainValue.value
    // const mainValueToCompare = mainValue.value?.toLowerCase()?.trim()

    if (mainValue.value === 'Blanks') {
      return handleBlanksFilter(row, filter, visibleColumn)
    }

    const rowValue = (row[filter.colId]?.toString() || '').toLowerCase()

    //* Check if the row value contains any of the wildcard separators
    if (rowValue.includes(MULTI_SELECT_SEPARATOR) || rowValue.includes(PREDECESSOR_SUCCESSOR_SEPARATOR)) {
      //* Split the row value by both separators to get all possible values
      const separatedValues =
        rowValue
          ?.split(MULTI_SELECT_SEPARATOR)
          ?.flatMap((val: string) => val?.split(PREDECESSOR_SUCCESSOR_SEPARATOR))
          ?.map((val: string) => val?.toLowerCase()?.trim())
          ?.filter(Boolean) || [] // Remove empty values

      // Check if any of the separated values match the mainValueToCompare
      const mainMatch = separatedValues.includes(mainValueToCompare)

      if (mainValue?.subValues) {
        const { colId: subColId, values: subValues } = mainValue.subValues
        if (!subValues.length) return mainMatch

        const subValueMatch = subValues.some(
          (subVal) => (row[subColId]?.toString() || '').toLowerCase() === subVal.value.toLowerCase(),
        )

        return mainMatch && subValueMatch
      }
      return mainMatch
    }

    //* If no wildcards present, do a simple check
    if (mainValue?.subValues) {
      const mainMatch = rowValue.includes(mainValueToCompare)
      const { colId: subColId, values: subValues } = mainValue.subValues
      if (!subValues.length) return mainMatch

      const subValueMatch = subValues.some(
        (subVal) => (row[subColId]?.toString() || '').toLowerCase() === subVal.value.toLowerCase(),
      )

      return mainMatch && subValueMatch
    } else {
      const mainMatch = rowValue === mainValueToCompare?.toLowerCase()
      return mainMatch
    }
  })
} */
// TODO: Generated version 1.0 For handleMultiSelectForWildcard function
/* const handleMultiSelectForWildCard = (
  row: IRowData,
  filter: FilterValue,
  visibleColumn: CustomColumnDef<any> | null,
): boolean => {
  return filter.values.some((mainValue) => {
    const mainValueToCompare = mainValue.value
    // const mainValueToCompare = mainValue.value?.toLowerCase()?.trim()

    if (mainValue.value === 'Blanks') {
      return handleBlanksFilter(row, filter, visibleColumn)
    }

    const [lookupKey, masterKey] = visibleColumn?.childAccessorKey ?? []
    let matchValue: any = (
      masterKey
        ? prepareMultiPhaseCategoryDropdownOptions(row?.[lookupKey], masterKey, filter.colId)
        : prepareDropdownOptions(row?.[lookupKey], filter.colId)
    ).find((fi) => fi.value === mainValue.value)

    const rowValue = (matchValue?.value?.toString() || '').toLowerCase()
    // const rowValue = ((data && data[filter.colId]?.toString()) || '').toLowerCase()

    //* Check if the row value contains any of the wildcard separators
    if (rowValue) {
      //* Split the row value by both separators to get all possible values
      const separatedValues =
        rowValue
          ?.split(MULTI_SELECT_SEPARATOR)
          ?.flatMap((val: string) => val?.split(PREDECESSOR_SUCCESSOR_SEPARATOR))
          ?.map((val: string) => val?.toLowerCase()?.trim())
          ?.filter(Boolean) || [] // Remove empty values

      // Check if any of the separated values match the mainValueToCompare
      const mainMatch = separatedValues.includes(mainValueToCompare)

      if (mainValue?.subValues) {
        const { colId: subColId, values: subValues } = mainValue.subValues
        if (!subValues.length) return mainMatch

        const subValueMatch = subValues.some(
          (subVal) => (row[subColId]?.toString() || '').toLowerCase() === subVal.value.toLowerCase(),
        )

        return mainMatch && subValueMatch
      }
      return mainMatch
    }

    //* If no wildcards present, do a simple check
    if (mainValue?.subValues) {
      const mainMatch = rowValue.includes(mainValueToCompare)
      const { colId: subColId, values: subValues } = mainValue.subValues
      if (!subValues.length) return mainMatch

      const subValueMatch = subValues.some(
        (subVal) => (row[subColId]?.toString() || '').toLowerCase() === subVal.value.toLowerCase(),
      )

      return mainMatch && subValueMatch
    } else {
      const mainMatch = rowValue === mainValueToCompare?.toLowerCase()
      return mainMatch
    }
  })
} */
// TODO: Generated version 2.0  For handleMultiSelectForWildcard function
const handleMultiSelectForWildCard = (
  row: IRowData,
  filter: FilterValue,
  visibleColumn: CustomColumnDef<any> | null,
): boolean => {
  return filter.values.some((mainValue) => {
    const mainValueToCompare = mainValue.value

    if (mainValueToCompare === 'Blanks') {
      return handleBlanksFilter(row, filter, visibleColumn)
    }

    const [lookupKey, masterKey] = visibleColumn?.childAccessorKey ?? []

    // Ensure arrayFormation is always an array
    let arrayFormation = (Array.isArray(row?.[lookupKey]) ? row?.[lookupKey] : [row?.[lookupKey]]).filter(Boolean)

    const options = masterKey
      ? prepareMultiPhaseCategoryDropdownOptions(arrayFormation, masterKey, filter.colId)
      : prepareDropdownOptions(arrayFormation, filter.colId)

    const matchValue = options?.find((fi) => fi.value === mainValueToCompare)

    const mainMatch = !!matchValue

    if (mainValue?.subValues) {
      const { colId: subColId, values: subValues } = mainValue.subValues

      if (!subValues.length) return mainMatch

      const subValueMatch = subValues.some(
        (subVal) => (row[subColId]?.toString() || '').toLowerCase() === subVal.value.toLowerCase(),
      )

      return mainMatch && subValueMatch
    }

    return mainMatch
  })
}

const handlePredecessorSuccessor = (row: IRowData, filter: FilterValue): boolean => {
  return filter?.values?.some((mainValue) => {
    const mainValueToCompare = mainValue.value.toLowerCase()?.trim()

    if (mainValue.value === 'Blanks') {
      return handleBlanksFilter(row, filter)
    }

    const rowValue = row[filter.colId] || []

    //* Split the row value by both separators to get all possible values
    const separatedValues = rowValue
      .map((pre: any) => {
        const projectPhaseCategories = pre?.DestinationProjectStatus?.LookupProjectToProjectPhaseCategory?.map(
          (item: ILookupProjectToProjectPhaseCategory) => item.MasterProjectPhaseCategory?.project_phase_category,
        )?.join(', ')
        const phase = pre?.DestinationProjectStatus?.LookupProjectToPhase.map(
          (item: ILookupProjectToPhase) => item.phase,
        )?.join(', ')
        const stageStatus = pre?.DestinationProjectStatus?.stage_status
        const subStage = pre?.DestinationProjectStatus?.sub_stage

        const name = `${projectPhaseCategories}${phase ? ` / ${phase}` : ''}${stageStatus ? ` / ${stageStatus}` : ''}${subStage ? ` / ${subStage}` : ''}`

        return { label: name?.trim(), value: pre?.project_status_id }
      })
      .join(';')
      ?.toLowerCase()

    const mainMatch = separatedValues.includes(mainValueToCompare)
    return mainMatch
  })
}

// Helper for default filter types
const handleDefaultFilter = (row: IRowData, filter: FilterValue): boolean => {
  return filter.values.some((mainValue) => {
    if (mainValue.value === 'Blanks') {
      return handleBlanksFilter(row, filter)
    }
    if (mainValue?.value?.toString() === 'true' || mainValue?.value?.toString() === 'false') {
      return handleBooleanFilter(row, filter)
    }
    const mainValueToCompare = mainValue?.value?.toString().toLowerCase()
    const mainMatch = (row[filter.colId]?.toString() || '').toLowerCase() === mainValueToCompare

    if (mainValue?.subValues) {
      const { colId: subColId, values: subValues } = mainValue.subValues
      if (!subValues.length) return mainMatch

      const subValueMatch = subValues
        .map((subVal) => subVal.value.toLowerCase())
        .includes((row[subColId]?.toString() || '').toLowerCase())

      return mainMatch && subValueMatch
    }

    return mainMatch
  })
}

export const dateSortingFn = (
  rowA: Row<IRowData>,
  rowB: Row<IRowData>,
  columnId: string,
  dateFormat: string = 'dd-MM-yyyy',
): number => {
  const dateStringA = rowA.getValue<string | null>(columnId)
  const dateStringB = rowB.getValue<string | null>(columnId)

  const parseRowDate = (dateString: string | null | undefined): Date | null => {
    if (!dateString || dateString === '-' || dateString === '') return null
    const parsedDate = parse(dateString, dateFormat, new Date())
    return isValid(parsedDate) ? parsedDate : null
  }

  const dateA = parseRowDate(dateStringA)
  const dateB = parseRowDate(dateStringB)

  if (dateA && dateB) return compareAsc(dateA, dateB)
  if (dateA && !dateB) return -1 // a is valid, b is invalid -> a comes first
  if (!dateA && dateB) return 1 // a is invalid, b is valid -> b comes first
  return 0 // both are invalid -> keep original order
}

/* export const isWildCardValue = (val: string) => {
  return val??.includes(MULTI_SELECT_SEPARATOR) || val??.includes(PREDECESSOR_SUCCESSOR_SEPARATOR)
} */

export const multiValueColumnId = (columnId: string, key: string): string => {
  switch (key) {
    case 'Multi categories':
      return 'LookupProjectToProjectPhaseCategory'
    case 'Multi phases':
      return 'LookupProjectToPhase'
    case 'Single phase':
      return 'LookupProjectToPhase'
    default:
      return columnId
  }
}

const getValueString = (row: Row<IRowData>, columnId: string, key?: string) => {
  const value = row.original[columnId]

  if (Array.isArray(value)) {
    if (key == 'Multi categories') {
      return value[0]?.MasterProjectPhaseCategory?.project_phase_category || ''
    } else if (key === 'Multi phases') {
      return value[0]?.phase || ''
    } else value
  } else if (key === 'Single phase') {
    return value?.phase || ''
  } else value

  return ''
}

export const multiValueSorting = (
  rowA: Row<IRowData>,
  rowB: Row<IRowData>,
  columnId: string,
  key: string = '',
): number => {
  let coulmnId = multiValueColumnId(columnId, key)

  const stringA = getValueString(rowA, coulmnId, key)
  const stringB = getValueString(rowB, coulmnId, key)

  if (typeof stringA === 'string' && typeof stringB === 'string') {
    return naturalSort(stringA, stringB)
  }
  return 0

  /* 
  const stringA = rowA.getValue<any | null>(coulmnId)
  const stringB = rowB.getValue<any | null>(coulmnId)
  const rowAString = isWildCardValue(stringA as string) ? key : stringA
  const rowBString = isWildCardValue(stringB as string) ? key : stringB
  if (typeof rowAString === 'string' && typeof rowBString === 'string') {
    // return rowAString.localeCompare(rowBString) // String comparison
    return naturalSort(rowAString, rowBString) // String comparison
  }
  return 0 */
}

export const singleAssociationValueSorting = (
  rowA: Row<IRowData>,
  rowB: Row<IRowData>,
  columnId: string,
  key: string = '',
): number => {
  let coulmnId = multiValueColumnId(columnId, key)

  const stringA = getValueString(rowA, coulmnId, key)
  const stringB = getValueString(rowB, coulmnId, key)

  if (typeof stringA === 'string' && typeof stringB === 'string') {
    return naturalSort(stringA, stringB)
  }
  return 0

  /* 
  const stringA = rowA.getValue<any | null>(coulmnId)
  const stringB = rowB.getValue<any | null>(coulmnId)
  const rowAString = isWildCardValue(stringA as string) ? key : stringA
  const rowBString = isWildCardValue(stringB as string) ? key : stringB
  if (typeof rowAString === 'string' && typeof rowBString === 'string') {
    // return rowAString.localeCompare(rowBString) // String comparison
    return naturalSort(rowAString, rowBString) // String comparison
  }
  return 0 */
}

export const numericValueSorting = (rowA: Row<IRowData>, rowB: Row<IRowData>, columnId: string): number => {
  const parseValue = (val: any) => {
    if (val === '-' || val === null || val === undefined) return -1
    return typeof val === 'number' ? val : parseFloat(val) || 0
  }

  const valueA = parseValue(rowA.getValue(columnId))
  const valueB = parseValue(rowB.getValue(columnId))

  return valueA - valueB
}

export const sortStringArray = (data: string[]) => {
  if (data.length === 0) {
    return []
  }

  return [...data].sort((a, b) => {
    return naturalSort(a.toLowerCase(), b.toLowerCase())
  })
}

export const sortData = (array: any[], key: string, convertNumber?: boolean) => {
  if (!Array.isArray(array) || array.length === 0 || !key) {
    return array // Return the original array if it's invalid
  }

  return Array.from(array).sort((a, b) => {
    const valueA = a[key]
    const valueB = b[key]

    // Handle null values: prioritize non-null over null
    if (valueA === null && valueB !== null) return 1
    if (valueA !== null && valueB === null) return -1

    // Convert field data to numbers if convertNumber is true
    let compA = valueA
    let compB = valueB

    if (convertNumber) {
      if (typeof valueA === 'string') compA = parseFloat(valueA)
      if (typeof valueB === 'string') compB = parseFloat(valueB)
    }

    // Sort based on numeric values if both are numbers
    if (typeof compA === 'number' && typeof compB === 'number') {
      return compA - compB // Numeric comparison
    }

    // Sort based on string values if both are strings
    if (typeof compA === 'string' && typeof compB === 'string') {
      return naturalSort(compA, compB) // String comparison
    }

    // Handle case where one value is a number and the other is a string
    if (typeof compA === 'number' && typeof compB === 'string') {
      return -1 // Numbers come before strings
    }
    if (typeof compA === 'string' && typeof compB === 'number') {
      return 1 // Strings come after numbers
    }

    // Handle other data types
    return 0 // If both are of the same type and non-comparable, keep original order
  })
}

export const sortColumnsByOrder = (orderArr: { column: string; order: number }[], columns: any[]): any[] => {
  // Create a map for quick lookup of order
  const orderMap = orderArr && orderArr?.length > 0 ? new Map(orderArr?.map((item) => [item.column, item.order])) : null

  // Split columns into those with an order and those without
  const withOrder = columns?.filter((col) => orderMap?.has(col.accessorKey))
  const withoutOrder = columns?.filter((col) => !orderMap?.has(col.accessorKey))

  // Sort columns with order
  withOrder.sort((a, b) => orderMap?.get(a.accessorKey)! - orderMap?.get(b.accessorKey)!)

  // Return sorted columns, followed by columns without order
  return [...withOrder, ...withoutOrder]
}
