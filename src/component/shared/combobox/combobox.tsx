import React, { ReactElement, useMemo } from 'react'
import CloseIcon from '@mui/icons-material/Close'
import { Avatar, Tooltip } from '@mui/material'
import Image from 'next/image'
import Select, { ControlProps, GroupBase, OptionProps, components, ClearIndicatorProps } from 'react-select'
import InfoIcon from '../../../../public/svg/info.svg'
import DropDownIcon from '../../svgImages/dropDownIcon'
import RotateIcon from '../../svgImages/rotateIcon'
import styles from '../combobox/Combobox.module.scss'
import { naturalSort } from '@/src/utils/sortingUtils'

interface OptionType {
  label: string
  value: string
  tooltip?: string
  avatar?: string // Add avatar property to options
}

interface ComboBoxProps {
  value: any
  onChange: (option: any) => void
  options?: any[]
  labelText?: string
  error?: boolean
  helperText?: string
  onBlur?: (event: any) => void
  required?: boolean
  placeholder?: string
  disabled?: boolean
  labelKey?: string
  valueKey?: string
  className?: string
  focusCustomClass?: string
  placeholderStyles?: string
  isMulti?: boolean
  clearIcon?: boolean
  isAvatarOption?: boolean
}

const CustomComboBox: React.FC<ComboBoxProps> = ({
  value,
  onChange,
  options,
  labelText = '',
  error = false,
  helperText = '',
  onBlur,
  required = false,
  placeholder = '',
  disabled = false,
  labelKey = '',
  valueKey = '',
  className = '',
  focusCustomClass,
  isMulti = false,
  placeholderStyles,
  clearIcon = false,
  isAvatarOption = false,
}): ReactElement => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)

  const isArrayOfStrings = useMemo(() => {
    return Array.isArray(options) && options.length > 0 && typeof options[0] === 'string'
  }, [options])

  const menuOptions = useMemo(() => {
    if (isArrayOfStrings && options) {
      return options
        .sort((a, b) => {
          const aStr = typeof a === 'string' ? a.toLowerCase() : String(a)
          const bStr = typeof b === 'string' ? b.toLowerCase() : String(b)
          // return aStr.localeCompare(bStr)
          return naturalSort(aStr, bStr)
        })
        .map((res) => ({ label: res, value: res }))
    } else {
      return options?.sort((a, b) => {
        const key = labelKey ? labelKey : 'label'
        const aStr = typeof a?.[key] === 'string' ? a?.[key].toLowerCase() : String(a?.[key])
        const bStr = typeof b?.[key] === 'string' ? b?.[key].toLowerCase() : String(b?.[key])
        // return aStr.localeCompare(bStr)
        return naturalSort(aStr, bStr)
      })
    }
  }, [isArrayOfStrings, options])

  const handleSelectBlur = (event: React.FocusEvent) => {
    if (onBlur) onBlur(event)
    setIsMenuOpen(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      setIsMenuOpen(false)
    }
  }

  const getOptionLabel = (option: any) => {
    if (labelKey) {
      return option[labelKey] || option.data[labelKey]
    }
    if (typeof option === 'object') {
      return option?.label || ''
    }
    return option || ''
  }

  const getOptionValue = (option: any) => {
    if (valueKey) {
      return option[valueKey]
    }
    if (typeof option === 'object') {
      return option?.value
    }
    return option
  }

  const DropdownIndicator = (props: any) => (
    <components.DropdownIndicator {...props}>
      {isMenuOpen ? (
        <RotateIcon rotate="up" fill="#444444" className={styles.searchIcon} />
      ) : (
        <DropDownIcon className={styles.arrowIcon} />
      )}
    </components.DropdownIndicator>
  )

  const ClearIndicator = (props: ClearIndicatorProps<any, boolean, GroupBase<any>>) => (
    <components.ClearIndicator {...props}>
      {clearIcon ? <CloseIcon className={styles.clearIcon} sx={{ width: '15px', height: '15px' }} /> : null}
    </components.ClearIndicator>
  )
  // Custom Option Component to show avatar with label
  const CustomOption = (props: OptionProps<OptionType>) => {
    const { data, innerRef, innerProps } = props
    const sorted = data?.tooltip
      ?.split('#')
      ?.map((item) => item.trim())
      ?.sort()
      ?.join(', ')
    return (
      <>
        <div ref={innerRef} {...innerProps} className={styles.option} style={{ display: 'flex', zIndex: 9998 }}>
          {isAvatarOption && <Avatar src={data.avatar} sx={{ width: 24, height: 24, marginRight: 2 }} />}
          {data.label}{' '}
          {data?.tooltip && (
            <span
              className="tooltip-container"
              style={{
                marginLeft: '8px',
                marginTop: '3px',
                display: 'inline-flex',
                alignItems: 'center',
                position: 'relative',
                zIndex: 10001,
              }}
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              <Tooltip
                title={sorted}
                arrow
                placement="right"
                PopperProps={{
                  sx: {
                    zIndex: 10002,
                    fontSize: '12px',
                    pointerEvents: 'auto',
                  },
                  modifiers: [
                    {
                      name: 'preventOverflow',
                      options: {
                        boundary: document.body,
                      },
                    },
                  ],
                }}
                componentsProps={{
                  tooltip: {
                    sx: {
                      bgcolor: 'white',
                      color: '#555',
                      border: '1px solid #e0e0e0',
                      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                      p: 1.5,
                      maxWidth: 350,
                      fontSize: '12px',
                      lineHeight: 1.4,
                      wordBreak: 'break-word',
                      '& .MuiTooltip-arrow': {
                        color: 'white',
                        '&:before': {
                          border: '1px solid #e0e0e0',
                        },
                      },
                    },
                  },
                }}
                enterDelay={100}
                leaveDelay={100}
              >
                <span>
                  <Image src={InfoIcon || '/placeholder.svg'} alt="info" width={16} height={16} />
                </span>
              </Tooltip>
            </span>
          )}
        </div>
      </>
    )
  }

  const getClassNames = (condition: boolean, trueClass: string, falseClass: string = '') =>
    condition ? trueClass : falseClass

  return (
    <div className={`${className} ${styles.root}`} onClick={(e) => e.stopPropagation()}>
      {labelText && (
        <label className={styles.label}>
          {labelText}
          {required && <div className={styles.required}>*</div>}
        </label>
      )}
      <Select
        isMulti={isMulti}
        menuIsOpen={isMenuOpen}
        openMenuOnFocus
        menuPosition="fixed"
        classNamePrefix={'react_select'}
        onBlur={handleSelectBlur}
        onKeyDown={handleKeyDown}
        closeMenuOnSelect
        onMenuOpen={() => setIsMenuOpen(true)}
        onMenuClose={() => setIsMenuOpen(false)}
        isSearchable
        isClearable={clearIcon}
        options={menuOptions}
        filterOption={(option, inputValue) => {
          const label = getOptionLabel(option)?.toLowerCase()
          const input = inputValue.toLowerCase()
          return label?.includes(input)
        }}
        getOptionLabel={getOptionLabel}
        getOptionValue={getOptionValue}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isDisabled={disabled}
        components={{
          DropdownIndicator,
          ClearIndicator: clearIcon ? ClearIndicator : undefined,
          Option: CustomOption as any,
        }}
        noOptionsMessage={() => 'No results Found'}
        classNames={{
          container: () => styles.container,
          control: (props: ControlProps<string, boolean, GroupBase<string>>) =>
            `${styles.control} ${getClassNames(props.isFocused, focusCustomClass || styles.focusBorder)} ${getClassNames(
              error,
              styles.errorBorder,
            )} ${getClassNames(props.isDisabled, styles.disabled)}`,
          valueContainer: () => styles.valueContainer,
          singleValue: () => styles.singleValue,
          placeholder: () => (placeholderStyles ? placeholderStyles : styles.placeholder),
          input: () => styles.inputField,
          indicatorsContainer: () => styles.indicatorsContainer,
          indicatorSeparator: () => styles.indicatorSeparator,
          menuPortal: () => styles.menuPortal,
          menu: () => styles.menu,
          menuList: () => styles.menuList,
          option: (props: OptionProps<string, boolean, GroupBase<string>>) =>
            `${styles.option} ${getClassNames(props.isSelected, styles.selectedOption)} ${getClassNames(
              props.isFocused,
              styles.focusedOption,
            )}`,
          noOptionsMessage: () => styles.noOptionsMessage,
        }}
        styles={{
          menuPortal: (base) => ({ ...base, zIndex: 9999 }),
          control: (base) => ({
            ...base,
            borderRadius: '5px',
            border: '1px solid #f2f2f2',
          }),
        }}
        menuPortalTarget={document.body}
      />
      {helperText && <span className={`${styles.text} ${getClassNames(error, styles.error)}`}>{helperText}</span>}
    </div>
  )
}

export default CustomComboBox
