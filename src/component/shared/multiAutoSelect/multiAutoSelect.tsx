import React, { useMemo } from 'react' // No need for useState here anymore, as it's controlled by parent
import ClearIcon from '@mui/icons-material/Clear'
import { Avatar, Checkbox } from '@mui/material'
import Autocomplete from '@mui/material/Autocomplete'
import styles from './MultiAutoSelect.module.scss'
import DropDownIcon from '../../svgImages/dropDownIcon'
import TextInput<PERSON>ield from '../textInputField'
import { sortArrayByKeyWithTypeConversion } from '@/src/utils/arrayUtils'

// Interface for the options in the autocomplete
export interface IAutoCompleteOption {
  id: string
  name: string
  avatar?: string
  subOptions?: IAutoCompleteOption[]
}

// Props interface for the MultiAutoSelect component
export interface IAutocompleteSwitch {
  isSubOption?: boolean
  options: IAutoCompleteOption[]
  handleSelectedOption: (option: string[]) => void
  value: string[] | number[]
  labelText?: string
  placeholder?: string
  className?: string
  isSx?: boolean
  isSort?: boolean
  disabled?: boolean
  clearIcon?: boolean
  isAvatarOption?: boolean
  isMultiLine?: boolean
  customRenderOption?: (props: React.HTMLAttributes<HTMLLIElement>, option: IAutoCompleteOption) => React.ReactNode
  onBlur?: () => void
  inputValue?: string // Now directly from props
  setInputValue?: (val: string) => void // Now directly from props
  isSelectAllApply?: boolean
}

// MultiAutoSelect component
const MultiSearchAutoSelect: React.FC<IAutocompleteSwitch> = ({
  isSubOption = true,
  value: valueParent = [],
  isSx = true,
  isSort = true,
  labelText,
  placeholder,
  options,
  className,
  disabled,
  handleSelectedOption,
  customRenderOption,
  clearIcon,
  isAvatarOption = false,
  isMultiLine,
  onBlur,
  inputValue, // Destructure from props
  setInputValue, // Destructure from props
  isSelectAllApply = true,
}) => {
  const SELECT_ALL_ID = '__select_all__'
  const val = (valueParent || [])?.map((item: any) => item) as string[]
  const handleChange = (id: string) => {
    const isSelected = val?.includes(id)
    // const selectAllId = 'Select All'

    // Recursive function to get all sub-option IDs
    const getSubOptionIds = (optionId: string, options: IAutoCompleteOption[]): string[] => {
      const option = options?.find((option) => option.id === optionId)
      if (!option || !option.subOptions) return []
      return option?.subOptions?.flatMap((sub) => [sub?.id, ...getSubOptionIds(sub?.id, options)])
    }

    // Calculate all option IDs, including sub-options
    const allOptionIds = options.flatMap((option) => [option?.id, ...getSubOptionIds(option?.id, options)])

    let updatedValues: string[] = []

    if (id === SELECT_ALL_ID) {
      const visibleIds = filteredOptions.filter((opt) => opt.id !== SELECT_ALL_ID).map((opt) => opt.id)
      const allVisibleSelected = visibleIds.every((id) => val.includes(id))
      let updatedValues: string[]
      if (allVisibleSelected) {
        // Deselect all visible
        updatedValues = val.filter((v) => !visibleIds.includes(v))
      } else {
        // Select all visible (add to already selected)
        updatedValues = Array.from(new Set([...val, ...visibleIds]))
      }
      handleSelectedOption(updatedValues)
      return
    } else {
      // Handle individual selection
      updatedValues = isSelected
        ? val?.filter((v) => v !== id) // Deselect
        : [
            ...val,
            id,
            ...getSubOptionIds(id, options), // Select the id and its sub-options
          ]

      // // Check if 'Select All' needs to be deselected
      // if (val?.includes(selectAllId)) {
      //   if (updatedValues.length !== allOptionIds.length) {
      //     updatedValues = updatedValues.filter((v) => v !== selectAllId)
      //   }
      // }
    }

    handleSelectedOption(updatedValues)
  }

  const handleClear = () => {
    handleSelectedOption([])
    setInputValue && setInputValue('') // Clear input value when clear icon is clicked
  }

  // Helper function to get option name by ID
  const getOptionNameById = (id: string): string => {
    // Search through options and their suboptions to find the matching ID
    for (const option of options) {
      if (option.id === id) return option.name

      if (option.subOptions) {
        for (const subOption of option.subOptions) {
          if (subOption.id === id) return subOption.name
        }
      }
    }
    return id // Return the ID if no matching option is found
  }

  const getSelectedOptions = () => {
    return (
      options?.filter(
        (option) =>
          val?.includes(option?.id) ||
          (option.subOptions && option?.subOptions?.some((sub) => val?.includes(sub?.id?.trim()))),
      ) || []
    )
  }

  const flattenOptions = (options: IAutoCompleteOption[]): IAutoCompleteOption[] => {
    return options?.flatMap((option) => [
      { id: option?.id?.trim(), name: option?.name?.trim(), avatar: option?.avatar },
      ...(option.subOptions || []).map((sub) => ({ id: sub?.id?.trim(), name: sub?.name?.trim() })),
    ])
  }

  const renderSelectedValues = () => {
    // Get option names from IDs
    const selectedNames = val?.map((id) => getOptionNameById(id))
    return selectedNames.join(', ')
  }

  const renderSubOptions = (subOptions: IAutoCompleteOption[]) => {
    return (
      <div className={styles.subOptions}>
        {subOptions?.map((subOption, index) => {
          return (
            subOption?.id && (
              <li
                key={`${subOption?.id}-${index}`}
                onClick={(e) => {
                  e.stopPropagation() // Prevent click event from bubbling up to the parent
                  handleChange(subOption?.id)
                }}
              >
                <Checkbox checked={val?.includes(subOption?.id)} classes={{ root: styles.checkBox }} />
                {subOption?.name}
              </li>
            )
          )
        })}
      </div>
    )
  }

  const defaultRenderOption = (props: React.HTMLAttributes<HTMLLIElement>, option: IAutoCompleteOption) => {
    // Check if this is the Select All option
    const isSelectAll = option.id === SELECT_ALL_ID
    let checked = false
    if (isSelectAll) {
      // All visible options except Select All
      const visibleIds = filteredOptions.filter((opt) => opt.id !== SELECT_ALL_ID).map((opt) => opt.id)
      checked = visibleIds.length > 0 && visibleIds.every((id) => val.includes(id))
    } else {
      checked = val.includes(option.id)
    }

    return (
      <li {...props} onClick={() => handleChange(option.id)} className={styles.optionLi}>
        <div className={styles.options}>
          <Checkbox checked={checked} classes={{ root: styles.checkBox }} />
          {isAvatarOption && option.avatar && (
            <Avatar src={option.avatar} sx={{ width: 24, height: 24, marginRight: 2, marginLeft: 2 }} />
          )}
          <div className={styles.mainOptions}>
            <div className={styles.optionName}>{option.name}</div>
          </div>
        </div>
        {option?.subOptions && renderSubOptions(option.subOptions)}
      </li>
    )
  }

  const selectedOptions = getSelectedOptions()
  const value = isSubOption ? flattenOptions(selectedOptions) : (val as unknown as IAutoCompleteOption[])

  const filteredOptions = useMemo(() => {
    let filtered = options
    if (inputValue && inputValue.trim() !== '') {
      const search = inputValue.trim().toLowerCase()
      filtered = options.filter((opt) => opt?.name?.toLowerCase().includes(search))
    }
    if (isSelectAllApply && filtered.length > 0) {
      // Only add Select All if there are visible options
      return [
        { id: SELECT_ALL_ID, name: 'Select All' },
        ...(isSort ? sortArrayByKeyWithTypeConversion(filtered, 'name') : filtered),
      ]
    }
    return isSort ? sortArrayByKeyWithTypeConversion(filtered, 'name') : filtered
  }, [options, inputValue, isSelectAllApply, isSort])

  return (
    <div className={styles.root}>
      {labelText && <div className={styles.labelText}>{labelText}</div>}
      <Autocomplete
        multiple
        disabled={disabled}
        size="small"
        id="checkboxes-tags-demo"
        // options={isSort ? sortArrayByKeyWithTypeConversion(options, 'name') : options}
        options={filteredOptions}
        inputValue={inputValue} // Use the inputValue from props
        onInputChange={(_, newInputValue, reason) => {
          // IMPORTANT: Only update inputValue if the reason is NOT 'reset'
          // 'reset' typically happens when an option is selected, and we want to keep the text.
          if (reason !== 'reset') {
            setInputValue && setInputValue(newInputValue)
          }
        }}
        filterOptions={(options) => options}
        value={value} // Use the processed 'value'
        disableCloseOnSelect
        clearIcon={
          clearIcon && val.length > 0 ? <ClearIcon onClick={handleClear} className={styles.clearIcon} /> : null
        }
        getOptionLabel={(option) => option.name}
        renderOption={customRenderOption ?? defaultRenderOption}
        popupIcon={<DropDownIcon className={styles.downArrow} />}
        renderTags={() => (
          <div className={`${isMultiLine ? styles.isMultiLine : ''} ${styles.inputText}`}>{renderSelectedValues()}</div>
        )}
        onBlur={onBlur}
        isOptionEqualToValue={(option, valItem) => option.id === valItem?.id}
        classes={{
          input: styles.tagInput,
          popper: styles.popper,
          paper: styles.paper,
          listbox: styles.listBox,
          popupIndicator: styles.popupIndicator,
        }}
        onClose={() => setInputValue && setInputValue('')}
        renderInput={(params) => (
          <TextInputField
            variant="outlined"
            {...params}
            className={className}
            placeholder={placeholder}
            sx={
              isSx
                ? {
                    '& .Mui-focused': {
                      backgroundColor: '#f0f8ff !important',
                      borderBottom: '1px solid #2333C2BF',
                      '& .MuiOutlinedInput-input': {
                        backgroundColor: '#f0f8ff',
                        color: 'black !important',
                      },
                    },
                  }
                : undefined
            }
          />
        )}
        sx={{
          '& .MuiAutocomplete-input': {
            minWidth: '30px !important',
          },
        }}
      />
    </div>
  )
}

export default MultiSearchAutoSelect
