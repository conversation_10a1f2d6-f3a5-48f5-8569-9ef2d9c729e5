import React, { useState, useRef } from 'react'
import { InputAdornment } from '@mui/material'
import Autocomplete from '@mui/material/Autocomplete'
import styles from './AutocompleteDropdown.module.scss'
import DropDownIcon from '../../svgImages/dropDownIcon'
import TextInputField from '../textInputField'

interface AutoCompleteDropdownProps {
  options: string[]
  value: string
  label?: string
  onChange: (value: string) => void
  sx?: any
  className?: any
  placeHolder?: any
  readOnly?: boolean
}

const AutoCompleteDropdown: React.FC<AutoCompleteDropdownProps> = ({
  options: initialOptions,
  value,
  label,
  onChange,
  sx,
  className,
  placeHolder,
  readOnly = false,
}) => {
  const [options, setOptions] = useState<string[]>(initialOptions || [])
  const [inputValue, setInputValue] = useState<string>('')
  const inputRef = useRef<HTMLInputElement>(null)

  const [isOpen, setIsOpen] = useState(false)

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value)
  }

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      const newInputValue = inputValue.trim()
      if (newInputValue && !options.includes(newInputValue)) {
        const newOptions = [...options, newInputValue]
        setOptions(newOptions)
        onChange(newInputValue)
        setInputValue('')
      }
    }
  }

  return (
    <div className={styles.root}>
      <div className={styles.labelText}>{label}</div>
      <Autocomplete
        freeSolo
        value={value}
        options={options}
        openOnFocus
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue || '')
          onChange(newInputValue)
        }}
        classes={{
          root: styles.autoCompleteRoot,
          input: styles.tagInput,
          popper: styles.popper,
          paper: styles.paper,
          listbox: styles.listBox,
        }}
        popupIcon={<DropDownIcon className={styles.downArrow} />}
        open={isOpen}
        onOpen={() => setIsOpen(true)}
        onClose={() => setIsOpen(false)}
        readOnly={readOnly}
        disableCloseOnSelect
        clearIcon={true}
        renderInput={(params) => (
          <TextInputField
            {...params}
            placeholder={placeHolder}
            variant="outlined"
            inputRef={inputRef}
            className={className}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
            // InputProps={{
            //   endAdornment: (
            //     <InputAdornment
            //       position="start"
            //       className={styles.startAdornment}
            //     >
            //       <DropDownIcon className={styles.downArrow} onClick={() => setIsOpen(!isOpen)}/>
            //     </InputAdornment>
            //   ),
            // }}

            // sx={
            //   sx
            //     ? {
            //         "& .Mui-focused": {
            //           backgroundColor: "#f4f4fc !important",
            //           borderBottom: "1px solid #2333C2BF",
            //           "& .MuiOutlinedInput-input": {
            //             backgroundColor: "#f4f4fc",
            //             color: "black !important",
            //           },
            //         },
            //       }
            //     : undefined
            // }
          />
        )}
      />
    </div>
  )
}

export default AutoCompleteDropdown
