import React, { FC } from 'react'
import Typography, { TypographyProps } from '@mui/material/Typography'
import { DARK } from '@/src/constant/color'

interface CustomTypographyProps extends Omit<TypographyProps, 'variant'> {
  variant?: keyof typeof customVariants | TypographyProps['variant']
  text: string
  className?: string
}

const customVariants: any = {
  bodyBold: { fontSize: '0.75rem', fontWeight: 700, lineHeight: '1.125rem', color: DARK }, // 12px -> 0.75rem
  bodySemiBold: { fontSize: '0.875rem', fontWeight: 600, lineHeight: '1.3125rem', color: DARK }, // 14px -> 0.875rem
  captionSemiBold: { fontSize: '0.75rem', fontWeight: 600, lineHeight: '1.125rem', color: DARK }, // 12px -> 0.75rem
  thin: { fontSize: '0.625rem', fontWeight: 400, lineHeight: '0.9375rem', color: DARK }, // 10px -> 0.625rem
  h1: { fontSize: '3rem', fontWeight: 600, lineHeight: '3.5rem', color: DARK }, // 48px -> 3rem
  h2: { fontSize: '1rem', fontWeight: 700, lineHeight: '1.5rem', color: DARK }, // 16px -> 1rem
  h3: { fontSize: '2rem', fontWeight: 400, lineHeight: '2.5rem', color: DARK }, // 32px -> 2rem
  h4: { fontSize: '1.5rem', fontWeight: 600, lineHeight: '2rem', color: DARK }, // 24px -> 1.5rem
  h5: { fontSize: '1.125rem', fontWeight: 600, lineHeight: '1.4375rem', color: DARK }, // 18px -> 1.125rem
  h7: { fontSize: '1rem', fontWeight: 400, lineHeight: '1.5rem', color: DARK }, // 16px -> 1rem
  body1: { fontSize: '0.875rem', fontWeight: 400, lineHeight: '1.25rem', color: DARK }, // 14px -> 0.875rem
  subheadingSemiBold: { fontSize: '1rem', fontWeight: 600, lineHeight: '1.5rem', color: DARK }, // 16px -> 1rem
  subheading: { fontSize: '1rem', fontWeight: 700, lineHeight: '1.5rem', color: DARK }, // 16px -> 1rem
  caption: { fontSize: '0.75rem', fontWeight: 400, lineHeight: '1.125rem', color: DARK }, // 12px -> 0.75rem
  darkText: { fontSize: '0.875rem', fontWeight: 700, lineHeight: '1.25rem', color: DARK }, // 14px -> 0.875rem
}

const TypographyField: FC<CustomTypographyProps> = ({ variant = 'body1', text, className, ...props }) => {
  return (
    <Typography className={className} sx={customVariants[variant] || {}} {...props}>
      {text}
    </Typography>
  )
}

export default TypographyField
