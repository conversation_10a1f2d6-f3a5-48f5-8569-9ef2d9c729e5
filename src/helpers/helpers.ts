import { format, parse } from 'date-fns'
import { multiSelectOption, userType, whiteListedUserList } from '../constant/enum'
import { tooltipText } from '../constant/tooltipText'
import { ICurrentUser } from '../redux/authorization/interface'

export const parseDateFromString = (date: string) => {
  if (/^\d+$/.test(date)) {
    const dateTimeStamp = Number(date)
    return new Date(dateTimeStamp)
  }
  return new Date(date)
}

export const formatDMYDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = String(date.getFullYear())
  return `${day}/${month}/${year}`
}

export const getLastUpdatedString = (lastUpdated: string): string => {
  const currentTime: Date = new Date()
  const updatedTime: Date = new Date(lastUpdated)
  const timeDifference: number = currentTime.getTime() - updatedTime.getTime()
  const secondsDifference: number = Math.floor(timeDifference / 1000)
  const minutesDifference: number = Math.floor(secondsDifference / 60)
  const hoursDifference: number = Math.floor(minutesDifference / 60)
  const daysDifference: number = Math.floor(hoursDifference / 24)

  if (secondsDifference < 60) {
    return 'Just Now'
  } else if (minutesDifference === 1) {
    return '1 minute ago'
  } else if (minutesDifference < 60) {
    return `${minutesDifference} minutes ago`
  } else if (hoursDifference === 1) {
    return '1 hour ago'
  } else if (hoursDifference < 24) {
    return `${hoursDifference} hours ago`
  } else if (daysDifference === 1) {
    return '1 day ago'
  } else {
    return `${daysDifference} days ago`
  }
}

export const populateDropdownOptions = <T>(arr: T[], property: keyof T): Array<T[keyof T]> => {
  return arr.map((obj) => obj[property])
}

export const getUrlOfUploadedFileByProjectManagement = (
  array: Array<{ field_name: string; file_path: string; media_type: string }>,
  field_name: string,
): { field_name: string; file_path: string } | undefined => {
  const foundItem = array?.find((item) => item.media_type === field_name)
  // const foundItem = array?.find((item) => item.field_name === field_name)
  return foundItem
}

export const allowDigits = (event: any) => {
  const inputValue = event.target.value
  let updatedValue = inputValue
  const isValidInput = /^[0-9]+$/.test(inputValue)
  if (isValidInput) {
    // allow input if it's a valid number or decimal
    event.target.value = inputValue
    updatedValue = inputValue
  } else {
    // if the input is not valid, revert to the previous value
    event.target.value = ''
    updatedValue = ''
  }
  return updatedValue
}

export function convertDateFormat(dateString: string) {
  // Split the input date string into day, month, and year
  var parts = dateString && dateString?.split('-')
  var year: any = parts?.[0]
  var month: any = parts?.[1]
  var day: any = parts?.[2]
  // Create a Date object using the input date
  var date = new Date(year, month - 1, day)

  // Array of month names
  var monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ]

  // Format the date
  var formattedDate = monthNames[date.getMonth()] + ' ' + date.getDate() + ', ' + date.getFullYear()

  return formattedDate
}

export function formateParamsPeriod(date: any) {
  const inputDate = new Date(date)

  // Check if inputDate is a valid date
  if (isNaN(inputDate.getTime())) {
    return '' // or handle the error in a suitable way
  }

  // Get year, month, and day from the date object
  const yyyy = inputDate.getFullYear()
  const mm = String(inputDate.getMonth() + 1).padStart(2, '0')
  const dd = String(inputDate.getDate()).padStart(2, '0')

  // Form the yyyy-mm-dd format
  const formattedDate = dd + '-' + mm + '-' + yyyy

  return formattedDate
}

export function toISOFormatDate(dateString: string) {
  if (dateString) {
    const trimmedDateString = dateString.toString().trim()
    let day, month, year

    // Check if the string contains "-"
    if (trimmedDateString.includes('-')) {
      const parts = trimmedDateString.split('-')

      if (parts.length === 3) {
        if (parts[0].length === 4) {
          // Format is "YYYY-MM-DD"
          ;[year, month, day] = parts
        } else {
          // Format is "DD-MM-YYYY"
          ;[day, month, year] = parts
        }

        // Validate the date parts
        if (
          year.length === 4 &&
          month.length === 2 &&
          day.length === 2 &&
          !isNaN(Date.parse(`${year}-${month}-${day}`))
        ) {
          const dateObject = new Date(`${year}-${month}-${day}`)
          // Ensure the constructed date is valid
          if (
            dateObject.getFullYear() === parseInt(year) &&
            dateObject.getMonth() + 1 === parseInt(month) &&
            dateObject.getDate() === parseInt(day)
          ) {
            const isoDateString = dateObject.toISOString().split('T')[0]
            return isoDateString
          }
        }
      }
    }
  }
  return ''
}

export function convertYYYYMMDD(isoDate: string) {
  if (isoDate && toISOFormatDate(isoDate)) {
    const date = new Date(toISOFormatDate(isoDate))

    // Extracting year, month, and day
    const year = date.getFullYear().toString() // Get last two digits of the year
    const month = (date.getMonth() + 1).toString().padStart(2, '0') // Adding leading zero if necessary
    const day = date.getDate().toString().padStart(2, '0') // Adding leading zero if necessary

    // Concatenating in YY-DD-MM format
    const YYYYMMDDFormat = `${day}-${month}-${year}`
    return YYYYMMDDFormat
  } else {
    return ''
  }
}

export function payloadDateFormate(isoDate: string) {
  if (toISOFormatDate(isoDate)) {
    const date = new Date(toISOFormatDate(isoDate))

    // Extracting year, month, and day
    const year = date.getFullYear().toString() // Get last two digits of the year
    const month = (date.getMonth() + 1).toString().padStart(2, '0') // Adding leading zero if necessary
    const day = date.getDate().toString().padStart(2, '0') // Adding leading zero if necessary

    // Concatenating in YY-DD-MM format
    const YYYYMMDDFormat = `${year}-${month}-${day}`
    return YYYYMMDDFormat
  } else {
    return null
  }
}

export function isoToPayload(isoDate: string | Date): string {
  // Check if isoDate is a valid date
  const date = new Date(isoDate)
  if (isNaN(date.getTime())) return ''

  // Extracting year, month, and day
  const year = date.getFullYear().toString()
  const month = (date.getMonth() + 1).toString().padStart(2, '0') // Adding leading zero if necessary
  const day = date.getDate().toString().padStart(2, '0') // Adding leading zero if necessary

  // Concatenating in YYYY-MM-DD format
  const YYYYMMDDFormat = `${year}-${month}-${day}`
  return YYYYMMDDFormat
}

export function sortByKey(array: any, key: any) {
  if (array && array?.length > 0 && key) {
    return Array.from(array)?.sort((a: any, b: any) => {
      // If either a or b doesn't have the specified key, prioritize the one with the key
      if (a[key] === null && b[key] !== null) return 1
      if (a[key] !== null && b[key] === null) return -1
      // If both have the key or both don't, sort based on the key
      if (typeof a[key] === 'number' && typeof b[key] === 'number') {
        return a[key] - b[key]
      } else if (typeof a[key] === 'string' && typeof b[key] === 'string') {
        return a[key].localeCompare(b[key])
      } else {
        // Handle other data types if needed
        return 0
      }
    })
  } else {
    return array
  }
}

export function sortByKeyConvertNumber(array: any, key: any) {
  if (array && array?.length > 0 && key) {
    return Array.from(array)?.sort((a: any, b: any) => {
      // If either a or b doesn't have the specified key, prioritize the one with the key
      if (a[key] === null && b[key] !== null) return 1
      if (a[key] !== null && b[key] === null) return -1

      // Convert field data to numbers if possible
      const numericA = typeof a[key] === 'string' ? parseFloat(a[key]) : a[key]
      const numericB = typeof b[key] === 'string' ? parseFloat(b[key]) : b[key]

      // If both have the key or both don't, sort based on the key
      if (!isNaN(numericA) && !isNaN(numericB)) {
        return numericA - numericB
      } else if (typeof a[key] === 'string' && typeof b[key] === 'string') {
        return a[key].localeCompare(b[key])
      } else {
        // Handle other data types if needed
        return 0
      }
    })
  } else {
    return array
  }
}

export function convertMultiSelectOption(array: any[], subStageName?: any, subStageOption?: any[]): any[] {
  const subOption = subStageOption?.filter((item) => item?.length && item !== null)
  const data = array?.map((item) => {
    const option: any = {
      id: item,
      name: item,
      subOptions:
        subStageName && item === subStageName
          ? subOption?.map((subItem) => {
              if (!subItem.length) return
              return {
                id: subItem,
                name: subItem,
              }
            })
          : [],
    }
    return option
  })
  return data
}

export function formatNumberWithCommas(number: number) {
  if (number == 0) return 0
  if (!number || number?.toString() === '0.00%') {
    return ''
  }
  /*
  Convert a number to a string with commas for thousands separators.
  */
  return number?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export function getUniqueValues(array: any) {
  const uniqueValues: any = []
  array.forEach((value: any) => {
    if (value !== null && !uniqueValues.includes(value)) {
      uniqueValues.push(value)
    }
  })
  return uniqueValues
}

export function convertToISO(dateString: string) {
  // Ensure the input format is correct (dd-mm-yyyy)
  if (!/^\d{2}-\d{2}-\d{4}$/.test(dateString)) {
    return ''
  }

  let parts = dateString.split('-')

  // Extract day, month, and year from the parts
  let day = parseInt(parts[0], 10)
  let month = parseInt(parts[1], 10) - 1 // JavaScript months are 0-based
  let year = parseInt(parts[2], 10)

  // Create a new Date object using the extracted parts
  let dateObject = new Date(Date.UTC(year, month, day))

  // Convert the Date object to an ISO 8601 string
  let isoString = dateObject.toISOString()

  return isoString
}

export function formatDateString(dateString: any) {
  const currentFormat = 'yyyy-MM-dd'
  const outputFormat = 'MMM dd, yyyy'

  // Parse the date string into a Date object
  const parsedDate: any = parse(dateString, currentFormat, new Date())
  // Format the parsed date into the desired output format
  return format(parsedDate, outputFormat)
}

export function convertDDMMYYYYToLongDate(dateString: string) {
  if (dateString) {
    const [day, month, year] = dateString && dateString?.split('-')
    const date = new Date(`${month}-${day}-${year}`)
    const formattedDate = date.toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Asia/Kolkata',
    })
    if (formattedDate === 'Invalid Date') {
      const [year, month, day] = dateString?.split('-')
      const date = new Date(`${month}-${day}-${year}`)
      const formattedDate = date.toLocaleString('en-US', {
        weekday: 'short',
        month: 'short',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Kolkata',
      })
      return formattedDate
    }
    return formattedDate
  } else {
    return ''
  }
}

/**
 * Stores an item in localStorage with the given key.
 *
 * @param key - The key under which the value will be stored.
 * @param value - The value to be stored. This will be stringified before storage.
 *
 * @example
 * setLocalStorageItem('user', { name: 'Alice', age: 30 });
 * // Stores the object { name: 'Alice', age: 30 } under the key 'user'
 */
export const setLocalStorageItem = (key: string, value: any): void => {
  localStorage.setItem(key, JSON.stringify(value))
}

/**
 * Retrieves an item from localStorage by the given key.
 *
 * @param key - The key of the item to be retrieved.
 * @returns The value associated with the key, or null if the key does not exist.
 *
 * @example
 * const user = getLocalStorageItem('user');
 * // Retrieves the value associated with the key 'user', which was previously stored
 */
export const getLocalStorageItem = (key: string): any | null => {
  const value = localStorage.getItem(key)
  return value ? JSON.parse(value) : null
}

/**
 * Removes an item from localStorage by the given key.
 *
 * @param key - The key of the item to be removed.
 *
 * @example
 * removeLocalStorageItem('user');
 * // Removes the item associated with the key 'user'
 */
export const removeLocalStorageItem = (key: string): void => {
  localStorage.removeItem(key)
}

export function getUniqueValuesFromArray(arr: string[]): string[] {
  const seen: Set<string> = new Set()
  const result: string[] = []

  arr.forEach((value) => {
    if (!seen.has(value)) {
      seen.add(value)
      result.push(value)
    }
  })

  return result
}

// first freezeType  if is unfreeze return true
//userFreeze he  toh check kro condition Update Over User Freeze Period
export const canEditUser = (
  currentUser: ICurrentUser,
  freezeType: string,
  currentPeriod: string,
  mainPeriod: string,
) => {
  const { role, user_type } = currentUser

  const hasPermission = role.view_permissions.includes('Update Over User Freeze Period')

  if (currentPeriod !== mainPeriod) {
    return false
  }

  switch (freezeType) {
    case 'unFreeze':
      return true

    case 'userFreeze':
      return hasPermission

    default:
      return false
  }

  // if (currentUser.role.view_permissions.includes('Update Over User Freeze Period')) return true
  // else if (
  //   freezeType === 'userFreeze' &&
  //   (currentUser.user_type === userType.USER || currentUser.user_type === userType.SUPER_USER)
  // )
  //   return false
  // if (freezeType === 'finalFreeze' && currentUser.user_type === userType.SUPER_ADMIN) return false
  // return false
}

const normalizeDate = (dateStr: any) => {
  if (!dateStr) return
  // If the date format is DD-MM-YYYY, convert it to YYYY-MM-DD
  if (dateStr.includes('-')) {
    const parts = dateStr.split('-')
    if (parts[0].length === 2 && parts[2].length === 4) {
      return `${parts[2]}-${parts[1]}-${parts[0]}`
    }
  }
  return dateStr
}

/**
 * Finds the latest date from an array of dates.
 *
 * This function performs the following steps:
 * 1. Filters out null and undefined values from the array of dates.
 * 2. Converts the remaining date values into Date objects after normalizing them.
 * 3. Finds the maximum date from the array of Date objects.
 * 4. Returns the latest date if valid, or an empty string if no valid dates are found.
 *
 * @param dates - An array of dates (can include null or undefined values).
 * @returns The latest date as a Date object, or an empty string if no valid dates are present.
 */
export const findLatestDate = (dates: any) => {
  // Filter out null and undefined dates
  const validDates = dates.filter((date: any) => date !== null && date !== undefined)

  // Convert valid dates to Date objects
  const dateObjects = validDates.map((date: any) => new Date(normalizeDate(date)))

  // Find the maximum date from the Date objects
  const latestDate: any = new Date(Math.max(...dateObjects))

  // Check if the latest date is valid
  if (isNaN(latestDate.getTime())) {
    return ''
  }

  return latestDate
}

/**
 * Filters and sorts an array of objects, optionally including a "Select All" option.
 *
 * This function performs the following steps:
 * 1. Defines the "Select All" option.
 * 2. Filters out objects with empty `id` or `name` properties.
 * 3. Checks if the "Select All" option is included in the data.
 * 4. Filters out the "Select All" option from the data if it is present.
 * 5. Sorts the remaining data by the `name` property in alphabetical order.
 * 6. Returns an array with the "Select All" option prepended if it was included, otherwise returns only the sorted data.
 *
 * @param data - An array of objects with `id` and `name` properties.
 * @returns A sorted array of objects, optionally including the "Select All" option at the beginning.
 */
export const convertAndSortData = (data: { id: string; name: string }[]) => {
  // Define the "Select All" option
  const selectAllOption = { id: multiSelectOption.SELECT_ALL, name: multiSelectOption.SELECT_ALL }

  // Filter out objects with empty id or name
  const filteredData = data?.filter((item) => item.id && item.name)

  // Separate the "Select All" option if available
  const selectAllIncluded = filteredData?.some((item) => item.id === selectAllOption.id)
  const filteredWithoutSelectAll = filteredData?.filter((item) => item.id !== selectAllOption.id)

  // Sort the filtered data by name in alphabetical order
  const sortedData = filteredWithoutSelectAll?.sort((a, b) => a.name?.localeCompare(b.name))

  // Combine "Select All" option with the sorted data
  return selectAllIncluded ? [selectAllOption, ...sortedData] : sortedData
}
/**
 * Checks if the given user has a role that includes 'Design Team'.
 *
 * This function performs the following:
 * 1. Accesses the `role_name` property of the user's `role` object.
 * 2. Checks if the `role_name` includes the substring 'Design Team'.
 * 3. Returns true if 'Design Team' is found, otherwise returns false.
 *
 * @param user - An object representing the user, which may include a `role` object with a `role_name` property.
 * @returns True if the user's role includes 'Design Team', otherwise false.
 */
export const hasDesignTeamRole = (user: { role?: { role_name?: string } }) =>
  user?.role?.role_name?.includes('Design Team') || false

/**
 * Checks if the given user type is 'USER'.
 *
 * @param type - The user type to be checked.
 * @returns True if the type matches 'USER', otherwise false.
 *
 * @example
 * const isUser = isUser('USER');
 * // Returns true if 'type' is 'USER'
 */
export const isUser = (type: string) => {
  return type === userType.USER
}

/**
 * Checks if the given user type is 'SUPER_USER'.
 *
 * @param type - The user type to be checked.
 * @returns True if the type matches 'SUPER_USER', otherwise false.
 *
 * @example
 * const isSuperUser = isSuperUser('SUPER_USER');
 * // Returns true if 'type' is 'SUPER_USER'
 */
export const isSuperUser = (type: string) => {
  return type === userType.SUPER_USER
}

/**
 * Checks if the given user type is 'SUPER_ADMIN'.
 *
 * @param type - The user type to be checked.
 * @returns True if the type matches 'SUPER_ADMIN', otherwise false.
 *
 * @example
 * const isSuperAdmin = isSuperAdmin('SUPER_ADMIN');
 * // Returns true if 'type' is 'SUPER_ADMIN'
 */
export const isSuperAdmin = (type: string) => {
  return type === userType.SUPER_ADMIN
}

//
// `convertToPercentage` converts a numeric value to a percentage representation
// (multiplies by 100 and formats to two decimal places) for displaying in the update
// progress form and status table. Returns an empty string if the input is null or undefined.
//
// @param value - The numeric value to convert to a percentage.
// @returns A string representing the percentage value or an empty string if input is null or undefined.
//
export const convertToPercentage = (value: string | number | undefined | null) => {
  if (value === null || !value) return ''

  const percentage = Number(value) * 100
  return percentage === 100 ? '100' : percentage.toFixed(2).toString()
}
//
// `toString` converts a value to its string representation. It is used for displaying values
// in the update progress form and status table. Returns an empty string if the input is null or undefined.
//
// @param value - The value to convert to a string.
// @returns The string representation of the value or an empty string if input is null or undefined.
//
export const toString = (value: string | number | undefined | null) => (value != null ? value.toString() : '')

const stageTableCalculation: Record<string, string> = {
  variance: tooltipText.stageTable_variance,
  actualLWeek: tooltipText.stageTable_actualLWeek,
  planLWeek: tooltipText.stageTable_planLWeek,
  start_date: tooltipText.stageTable_start_date,
}

const SPAGroupATableCalculation: Record<string, string> = {
  // spa_variance: 'Revised SPA Communication (or Actual/Forecast Milestone Collection if unavailable) – SPA Date',
  spa_variance: tooltipText.SPAGroupA_spa_variance,
  communication_variance: tooltipText.SPAGroupA_communication_variance,
}

const SPAGroupBTableCalculation: Record<string, string> = {
  contract_variance: tooltipText.SPAGroupB_contract_variance,
  communication_variance: tooltipText.SPAGroupB_communication_variance,
}

const SPAGovernmentTableCalculation: Record<string, string> = {
  variance_in_days: tooltipText.SPAGovernment_variance_in_days,
  planned_completion_date: tooltipText.SPAGovernment_planned_completion_date,
}

export const getTooltipFormulaStageTableToColumn = (column: any): string => {
  // Assuming column.columnDef.meta?.tableId or similar identifies the table context
  if (column?.columnDef?.tableId === 'stageTable') {
    return stageTableCalculation[column?.columnDef?.accessorKey] || ''
  } else if (column?.columnDef?.tableId === 'SPAGroupATable') {
    return SPAGroupATableCalculation[column?.columnDef?.accessorKey] || ''
  } else if (column?.columnDef?.tableId === 'SPAGroupBTable') {
    return SPAGroupBTableCalculation[column?.columnDef?.accessorKey] || ''
  } else if (column?.columnDef?.tableId === 'SPAGovernmentTable') {
    return SPAGovernmentTableCalculation[column?.columnDef?.accessorKey] || ''
  }

  return column?.columnDef?.header || ''
}

export const convertValuesToCommaSeparated = (value: string | any) => {
  // if (!value) {
  //   return 'No Phase'
  // }
  if (typeof value !== 'string') {
    return value
  }

  return value?.split('$@').join(', ')
}

export const getUniquePhases = (data: any) => {
  // Use a Set to automatically handle uniqueness
  const uniquePhases = new Set()
  data?.forEach((record: any) => {
    // Ensure the phase property exists and is a string
    if (typeof record === 'string' && record.trim() !== '') {
      // Split the phase string by '$@' to handle multiple phases
      const phases = record.split('$@').map((phase: any) => phase.trim())
      phases.forEach((phase: any) => {
        if (phase !== '') {
          // Ensure empty strings from splitting are not added
          uniquePhases.add(phase)
        }
      })
    }
  })

  // Convert the Set back to an array and sort it for consistent order
  return Array.from(uniquePhases)
}

/**
 * Sanitizes the Object by removing specified fields.
 * @param object - The original object.
 * @param fieldsToRemove - An array of field names to remove from the object.
 * @returns The sanitized object.
 */
export const sanitizeObject = (object: Record<string, any>, fieldsToRemove: string[]): Record<string, any> => {
  const sanitizedPayload = { ...object }

  fieldsToRemove.forEach((field) => {
    if (sanitizedPayload.hasOwnProperty(field)) {
      delete sanitizedPayload[field]
    }
  })

  return sanitizedPayload
}

export const ALLOWED_TYPES_PDF_EXCEL = [
  'application/pdf', // .pdf
  'application/vnd.ms-excel', // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'text/plain',
]

/**
 * Delays execution for the specified number of milliseconds.
 *
 * Useful for adding artificial delays, such as:
 * - Waiting before hiding a loader/spinner
 * - Simulating network latency in development
 * - Rate-limiting or throttling actions
 *
 * @param ms - The number of milliseconds to wait
 * @returns A Promise that resolves after the specified delay
 *
 * @example
 * await wait(1000); // pauses for 1 second
 */
export const wait = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
