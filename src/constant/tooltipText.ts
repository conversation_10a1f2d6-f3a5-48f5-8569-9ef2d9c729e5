export const tooltipText = {
  Overall_Planned_Finish_Date: 'Maximum Planned Finish Date Excluding DLP',
  Overall_Forecasted_Finish: 'Maximum Forecast Finish Date Excluding DLP',

  //*Formula - Update Progress Screen
  time_elapsed_perc: '((Today - Contract Start Date) / (Contract End Date - Contract Start Date)) * 100',
  SPI: '(Actual Progress %) / (Contractor Plan Progress %)',
  forecast_completion_last_week: 'Previous Period Forecast / Actual Finish Date',
  delay_this_week: '(Forecast / Actual Finish Date) - (Last Period Forecast / Actual Finish Date)',
  cumulative_delay: '(Forecast / Actual Finish Date) - Contract End Date',

  // *Formula - Table header
  stageTable_variance: `Construction: (Potential EOT to Contractor or Contract End Date) – Actual Finish Date, \n Others: Baseline Plan Finish – Actual Finish Date`,
  stageTable_actualLWeek: 'Actual Progress % Last Period',
  stageTable_planLWeek: 'Plan Progress % Last Period',
  stageTable_start_date: '1 day after the completion of its predecessor',

  SPAGroupA_spa_variance:
    '(Actual / Forecast Milestone Collection) – (Revised SPA Communication) (If no Revised SPA Communication, then use SPA Date)',
  SPAGroupA_communication_variance: 'Latest Communication - Revised SPA Communication',

  SPAGroupB_contract_variance: 'EOT - Contract Date',
  SPAGroupB_communication_variance: 'Latest Communication - Revised SPA Communication',

  SPAGovernment_variance_in_days: '(Planned Completion Date) - (Forecasted / Actual Completion Date)',
  SPAGovernment_planned_completion_date: '(Duration days - 1) + Contract Start Date',
}
