export const StorageKeys = {
  IS_LOGIN: 'isLogin',
}

export const Routes = {
  LOGIN: '/login',
  REDIRECT: '/redirect',
  HOME: '/',
  PROJECTS: '/projects',
  EDIT_STATUS: '/update-progress',
  USER_MANAGEMENT: '/user-management',
  USER_MANAGEMENT_USER: '/user-management/user',
  USER_MANAGEMENT_ROLE: '/user-management/role',
  SUMMARY: '/summary',
  BATCH_UPLOAD: '/upload',
}
export const permissionList = [
  'Summary',
  'Summary-Project Details',
  'Summary-Project Management',
  'Summary-Health And Safety',
  'Summary-Sustainability',
  'Status',
  'Status-Initiation',
  'Status-LDC Procurement',
  'Status-Design',
  'Status-Contractor Procurement',
  'Status-Construction',
  'Status-DLP and Project Closeout',
  'Status-Add Phase/Status',
  'Commercials',
  'Commercials-Project Summary',
  'Commercials-Contract & VOs',
  'Commercials-VOWD',
  'Project Overview',
  'SPA & Milestones',
  'Governance',
  'Project Sorting',
  'Executive Sorting',
  'Media',
  'Media-Decree',
  'Media-Plot Plan',
  'Media-Static Images',
  'Media-Dynamic Images',
  'Media-Additional Documents',
  'Authority Approvals Tracking',
  'Handover',
  'Progress Edit Permission',
  'Navigate To Historical Periods',
  'Edit Lead Functionality',
  'SPA & Milestones Edit Permission',
  'Update Over User Freeze Period',
  'Key Achievements Sorting',
  'Batch Upload',
  'Edit Forecast Finish Date',
  'Edit Baseline Plan Finish Date And Plan Duration',
  'Key Achievement',
  'Key Risk',
]

export const whiteListedUserList: string[] = [
  // '<EMAIL>',
  // '<EMAIL>',
  // '<EMAIL>',
  // '<EMAIL>',
  // '<EMAIL>',
]

export const userType = {
  USER: 'user',
  SUPER_USER: 'super_user',
  SUPER_ADMIN: 'super_admin',
}

export const multiSelectOption = {
  SELECT_ALL: 'Select All',
}

export const STATUS_OPTIONS = {
  ON_HOLD: 'On Hold',
  CANCELED: 'Canceled',
  TRANSFER_IN_PROGRESS: 'Transfer In Progress',
  TRANSFERRED: 'Transferred',
  RESUME: 'Resume',
  CLOSED_OUT: 'Closed Out',
}

export const ERROR_MESSAGE = 'Something went wrong. Please try again later.'

//* Unique Table Name List
export const TableNameList = {
  PROGRESS_TABLE: 'ProgressTable',
}
