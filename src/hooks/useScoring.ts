import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterScoring,
  deleteMasterScoring,
  deleteQuarterScoring,
  getMasterScoring,
  updateMasterScoring,
} from '../services/scoring'
import { IGetMasterScoringResponse, IUpdateScoring } from '../services/scoring/interface'
import { errorToast, successToast } from '../utils/toastUtils'

export const QUERY_SCORING = 'entity-scoring'

/**
 * Hook to fetch Scoring
 * @param enabled - Enables or disables the query
 */
export const useGetScoring = (enabled: boolean = false, quarter: string) => {
  const { data, isError, ...rest } = useQuery<IGetMasterScoringResponse>({
    queryKey: [QUERY_SCORING, quarter],
    queryFn: () => getMasterScoring(quarter),
    enabled,
    gcTime: 0,
    staleTime: 0,
  })

  return {
    entityScoring: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Scoring
 */
export const useCreateScoring = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterScoring,
    onSuccess: (response: any) => {
      successToast(response.data.message)
      queryClient.invalidateQueries({ queryKey: [QUERY_SCORING] })
    },
    onError: (err: any, _, context) => {
      errorToast(err?.response?.data?.message)
    },
  })
}

/**
 * Hook to delete a Scoring
 */
export const useDeleteScoring = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterScoring,
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SCORING] })
      successToast(response.message)
    },
    onError: (err: any, _, context) => {
      errorToast(err?.response?.message)
    },
  })
}

/**
 * Hook to delete a quarter Scoring
 */
export const useDeleteQuarterScoring = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteQuarterScoring,
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SCORING] })
      successToast(response.message)
    },
    onError: (err: any, _, context) => {
      errorToast(err?.response?.message)
    },
  })
}

/**
 * Hook to update a Scoring
 */
export const useUpdateScoring = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterScoring,
    onMutate: async (updatedData: IUpdateScoring) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_SCORING] })
      const previousData = queryClient.getQueryData<IGetMasterScoringResponse>([QUERY_SCORING])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterScoringResponse>([QUERY_SCORING], (old: any) => {
        if (!old) return old
        return { ...old, data: updatedData?.entityScore }
      })

      return { previousData }
    },
    onSuccess: (response) => {
      // response will contain { data, message, status }
      successToast(response.data.message)
      queryClient.invalidateQueries({ queryKey: ['entityScoring'] })
    },
    onError: (err: any, _, context) => {
      errorToast(err?.response?.data?.message)
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_SCORING], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_SCORING] })
    },
  })
}
