import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  createMasterDistrict,
  deleteMasterDistrict,
  getMasterDistricts,
  updateMasterDistrict,
} from '../services/districts'
import { IGetMasterDistrictsResponse, IUpdateDistrict } from '../services/districts/interface'

export const QUERY_DISTRICT = 'districts'

/**
 * Hook to fetch Districts
 * @param enabled - Enables or disables the query
 */
export const useGetDistricts = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetMasterDistrictsResponse>({
    queryKey: [QUERY_DISTRICT],
    queryFn: getMasterDistricts,
    enabled,
  })

  return {
    districts: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Districts
 */
export const useCreateDistricts = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createMasterDistrict,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DISTRICT] })
    },
  })
}

/**
 * Hook to delete a Districts
 */
export const useDeleteDistricts = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteMasterDistrict,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DISTRICT] })
    },
  })
}

/**
 * Hook to update a Districts
 */
export const useUpdateDistricts = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateMasterDistrict,
    onMutate: async (updatedData: IUpdateDistrict) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_DISTRICT] })
      const previousData = queryClient.getQueryData<IGetMasterDistrictsResponse>([QUERY_DISTRICT])
      // Optimistically update the cache
      queryClient.setQueryData<IGetMasterDistrictsResponse>([QUERY_DISTRICT], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, district: updatedData.district } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_DISTRICT], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_DISTRICT] })
    },
  })
}
