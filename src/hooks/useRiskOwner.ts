import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { createRiskOwner, deleteRiskOwner, getRiskOwner, updateRiskOwner } from '../services/riskOwner'
import { IGetRiskOwnerResponse, IUpdateRiskOwner } from '../services/riskOwner/interface'

export const QUERY_RISK_OWNER = 'risk_owner'

/**
 * Hook to fetch Risk Owners
 * @param enabled - Enables or disables the query
 */
export const useGetRiskOwner = (enabled: boolean = true) => {
  const { data, isError, ...rest } = useQuery<IGetRiskOwnerResponse>({
    queryKey: [QUERY_RISK_OWNER],
    queryFn: getRiskOwner,
    enabled,
  })

  return {
    riskOwners: data?.data ?? [],
    data,
    isError,
    ...rest,
  }
}

/**
 * Hook to create a Risk Owner
 */
export const useCreateRiskOwner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createRiskOwner,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_OWNER] })
    },
  })
}

/**
 * Hook to delete a Risk Owner
 */
export const useDeleteRiskOwner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteRiskOwner,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_OWNER] })
    },
  })
}

/**
 * Hook to update a Risk Owner
 */
export const useUpdateRiskOwner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: updateRiskOwner,
    onMutate: async (updatedData: IUpdateRiskOwner) => {
      await queryClient.cancelQueries({ queryKey: [QUERY_RISK_OWNER] })
      const previousData = queryClient.getQueryData<IGetRiskOwnerResponse>([QUERY_RISK_OWNER])
      // Optimistically update the cache
      queryClient.setQueryData<IGetRiskOwnerResponse>([QUERY_RISK_OWNER], (old) => {
        if (!old) return old
        return {
          ...old,
          data: old.data?.map((manager) =>
            manager.id === updatedData.id ? { ...manager, risk_owner: updatedData.risk_owner } : manager,
          ),
        }
      })

      return { previousData }
    },
    onError: (err, _, context) => {
      // Revert to previous data if mutation fails
      if (context?.previousData) {
        queryClient.setQueryData([QUERY_RISK_OWNER], context.previousData)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_RISK_OWNER] })
    },
  })
}
