{"name": "data-entry", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 8080", "format": "prettier --write . --fix", "eslint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint": "next lint --fix", "lint-fix": "npm run eslint && npm run format"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/lab": "^5.0.0-alpha.165", "@mui/material": "^5.15.10", "@mui/styled-engine-sc": "^6.0.0-alpha.16", "@mui/x-data-grid": "^7.5.0", "@mui/x-data-grid-pro": "^7.5.0", "@mui/x-date-pickers": "^6.19.5", "@reduxjs/toolkit": "^2.2.1", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.74.6", "@tanstack/react-table": "^8.17.3", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "axios": "^1.6.8", "axios-retry": "^4.5.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "formik": "^2.4.5", "jodit-react": "^5.2.19", "motion": "^12.6.2", "next": "14.1.0", "path": "^0.12.7", "quill": "^2.0.3", "react": "^18", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-quill": "^2.0.0", "react-redux": "^9.1.0", "react-select": "^5.8.0", "redux-persist": "^6.0.0", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "styled-components": "^6.1.8", "tailwind-merge": "^3.0.2", "xlsx": "^0.18.5", "yup": "^1.3.3", "yup-phone-lite": "^2.0.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.3.3", "sass": "^1.71.1", "typescript": "^5"}, "proxy": {"/api/*": {"target": "http://localhost:4000", "secure": false}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "myapp:build", "dev": true}, "configurations": {"development": {"buildTarget": "myapp:build:development", "dev": true, "hostname": "0.0.0.0"}, "production": {"buildTarget": "myapp:build:production", "dev": false}}}}